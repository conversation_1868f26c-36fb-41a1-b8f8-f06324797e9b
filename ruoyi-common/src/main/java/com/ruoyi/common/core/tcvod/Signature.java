package com.ruoyi.common.core.tcvod;


import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.Random;


// 签名工具类
public class Signature {
    private String secretId;
    private String secretKey;
    private long currentTime;
    private int random;
    private int signValidDuration;

    public Signature() {}

    public Signature( String secretId, String secretKey) {
        this.secretId = secretId;
        this.secretKey = secretKey;
        this.currentTime = System.currentTimeMillis() / 1000;
        this.random = new Random().nextInt(Integer.MAX_VALUE);
        this.signValidDuration = 3600 * 24 * 2;
    }

    private static final String HMAC_ALGORITHM = "HmacSHA1"; //签名算法
    private static final String CONTENT_CHARSET = "UTF-8";

    public static byte[] byteMerger(byte[] byte1, byte[] byte2) {
        byte[] byte3 = new byte[byte1.length + byte2.length];
        System.arraycopy(byte1, 0, byte3, 0, byte1.length);
        System.arraycopy(byte2, 0, byte3, byte1.length, byte2.length);
        return byte3;
    }
    // 获取签名
    public static String getUploadSignature(Signature signature, String sourceContext, String vodSubAppId, String procedure) throws Exception {
        //&procedure=QCVB_SimpleProcessFile(0%2C0%2C10%2C%200)&classId=0&vodSubAppId=1253990026
        String strSign = "";
        String contextStr = "";

        // 生成原始参数字符串
        long endTime = (signature.currentTime + signature.signValidDuration);
        contextStr += "secretId=" + java.net.URLEncoder.encode(signature.secretId, "utf8");
        contextStr += "&currentTimeStamp=" + signature.currentTime;
        contextStr += "&expireTime=" + endTime;
        contextStr += "&random=" + signature.random;
        if(StringUtils.isNotBlank(sourceContext)){
            contextStr += "&sourceContext=" + sourceContext;
        }
        if(StringUtils.isNotBlank(vodSubAppId)){
            contextStr += "&vodSubAppId=" + vodSubAppId;
        }
        if(StringUtils.isNotBlank(procedure)){
            contextStr += "&procedure=" + procedure;
        }

        try {
            Mac mac = Mac.getInstance(HMAC_ALGORITHM);
            SecretKeySpec secretKey = new SecretKeySpec(signature.secretKey.getBytes(CONTENT_CHARSET), mac.getAlgorithm());
            mac.init(secretKey);

            byte[] hash = mac.doFinal(contextStr.getBytes(CONTENT_CHARSET));
            byte[] sigBuf = byteMerger(hash, contextStr.getBytes("utf8"));
            strSign = base64Encode(sigBuf);
            strSign = strSign.replace(" ", "").replace("\n", "").replace("\r", "");
        } catch (Exception e) {
            throw e;
        }
        return strSign;
    }

    private static String base64Encode(byte[] buffer) {
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(buffer);
    }

    public void setSecretId(String secretId) {
        this.secretId = secretId;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public void setCurrentTime(long currentTime) {
        this.currentTime = currentTime;
    }

    public void setRandom(int random) {
        this.random = random;
    }

    public void setSignValidDuration(int signValidDuration) {
        this.signValidDuration = signValidDuration;
    }
}





