package com.ruoyi.common.core.tcvod;

import java.util.Random;

public class SignatureTest {
    public static void main(String[] args) {
        Signature sign = new Signature();
        // 设置 App 的云 API 密钥
        sign.setSecretId("AKID8bpvU9HMrDpi2GdfHyfFi8mKdAN3bHdt");
        sign.setSecretKey("oYcYO3YrVQ6qRvH1hUNk4dzVITm77IkV");
        sign.setCurrentTime(System.currentTimeMillis() / 1000);
        sign.setRandom(new Random().nextInt(Integer.MAX_VALUE));
        sign.setSignValidDuration(3600 * 24 * 2); // 签名有效期：2天

//&procedure=QCVB_SimpleProcessFile(0%2C0%2C10%2C%200)&classId=0&vodSubAppId=1253990026
        try {
            String signature = Signature.getUploadSignature(sign,"testId_testUserId","1253990026","QCVB_SimpleProcessFile(0%2C0%2C10%2C%200)");
            System.out.println("signature : " + signature);
        } catch (Exception e) {
            System.out.print("获取签名失败");
            e.printStackTrace();
        }
    }
}
