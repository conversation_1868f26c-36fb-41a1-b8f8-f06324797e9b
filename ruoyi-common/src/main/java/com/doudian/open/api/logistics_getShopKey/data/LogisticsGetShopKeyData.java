package com.doudian.open.api.logistics_getShopKey.data;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;

//auto generated, do not edit

public class LogisticsGetShopKeyData {


	@SerializedName("key")
	@OpField(desc = "公钥加密后的对称密钥，用于解密电子面单密文", example = "1")
	private String key;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setKey(String key){
		this.key = key;
	}

	
	public String getKey(){
		return this.key;
	}

}