package com.doudian.open.api.logistics_getRecommendedAndDeliveryExpressByOrder;

import java.util.*;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.logistics_getRecommendedAndDeliveryExpressByOrder.data.*;

//auto generated, do not edit

public class LogisticsGetRecommendedAndDeliveryExpressByOrderResponse extends DoudianOpResponse<List<DataItem>> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}