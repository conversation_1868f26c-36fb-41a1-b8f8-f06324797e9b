package com.doudian.open.api.product_getCascadeValue;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.product_getCascadeValue.data.*;

//auto generated, do not edit

public class ProductGetCascadeValueResponse extends DoudianOpResponse<ProductGetCascadeValueData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}