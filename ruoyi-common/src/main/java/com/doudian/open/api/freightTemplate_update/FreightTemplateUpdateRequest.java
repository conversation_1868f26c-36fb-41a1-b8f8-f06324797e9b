package com.doudian.open.api.freightTemplate_update;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.freightTemplate_update.param.*;

//auto generated, do not edit

public class FreightTemplateUpdateRequest extends DoudianOpRequest<FreightTemplateUpdateParam> {



	@Override
	public String getUrlPath(){
		return "/freightTemplate/update";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return FreightTemplateUpdateResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}