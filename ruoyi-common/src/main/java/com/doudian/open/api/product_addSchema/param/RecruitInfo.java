package com.doudian.open.api.product_addSchema.param;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;

//auto generated, do not edit

public class RecruitInfo {


	@SerializedName("recruit_follow_id")
	@OpField(required = true , desc = "线索ID", example= "28423315")
	private String recruitFollowId;

	@SerializedName("recruit_type")
	@OpField(required = true , desc = "招商类型", example= "99")
	private String recruitType;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setRecruitFollowId(String recruitFollowId){
		this.recruitFollowId = recruitFollowId;
	}

	
	public String getRecruitFollowId(){
		return this.recruitFollowId;
	}

	
	public void setRecruitType(String recruitType){
		this.recruitType = recruitType;
	}

	
	public String getRecruitType(){
		return this.recruitType;
	}

}