package com.doudian.open.api.order_logisticsAddSinglePack;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.order_logisticsAddSinglePack.data.*;

//auto generated, do not edit

public class OrderLogisticsAddSinglePackResponse extends DoudianOpResponse<OrderLogisticsAddSinglePackData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}