package com.doudian.open.api.order_uploadextrapackage;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.order_uploadextrapackage.param.*;

//auto generated, do not edit

public class OrderUploadextrapackageRequest extends DoudianOpRequest<OrderUploadextrapackageParam> {



	@Override
	public String getUrlPath(){
		return "/order/uploadextrapackage";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return OrderUploadextrapackageResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}