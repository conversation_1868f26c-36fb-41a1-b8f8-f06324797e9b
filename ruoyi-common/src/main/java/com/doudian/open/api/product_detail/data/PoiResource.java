package com.doudian.open.api.product_detail.data;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;
import java.util.List;

//auto generated, do not edit

public class PoiResource {


	@SerializedName("coupon_return_methods")
	@OpField(desc = "1 随时退+过期自动退，2 不支持退", example = "[1]")
	private List<Long> couponReturnMethods;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setCouponReturnMethods(List<Long> couponReturnMethods){
		this.couponReturnMethods = couponReturnMethods;
	}

	
	public List<Long> getCouponReturnMethods(){
		return this.couponReturnMethods;
	}

}