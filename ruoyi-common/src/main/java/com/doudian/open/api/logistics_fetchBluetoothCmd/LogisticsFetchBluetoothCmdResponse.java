package com.doudian.open.api.logistics_fetchBluetoothCmd;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.logistics_fetchBluetoothCmd.data.*;

//auto generated, do not edit

public class LogisticsFetchBluetoothCmdResponse extends DoudianOpResponse<LogisticsFetchBluetoothCmdData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}