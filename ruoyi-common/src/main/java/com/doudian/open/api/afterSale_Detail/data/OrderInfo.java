package com.doudian.open.api.afterSale_Detail.data;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;
import java.util.List;

//auto generated, do not edit

public class OrderInfo {


	@SerializedName("shop_order_id")
	@OpField(desc = "店铺单ID", example = "12345")
	private Long shopOrderId;

	@SerializedName("sku_order_infos")
	@OpField(desc = "sku单信息", example = "")
	private List<SkuOrderInfosItem> skuOrderInfos;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setShopOrderId(Long shopOrderId){
		this.shopOrderId = shopOrderId;
	}

	
	public Long getShopOrderId(){
		return this.shopOrderId;
	}

	
	public void setSkuOrderInfos(List<SkuOrderInfosItem> skuOrderInfos){
		this.skuOrderInfos = skuOrderInfos;
	}

	
	public List<SkuOrderInfosItem> getSkuOrderInfos(){
		return this.skuOrderInfos;
	}

}