package com.doudian.open.api.rights_info;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.rights_info.param.*;

//auto generated, do not edit

public class RightsInfoRequest extends DoudianOpRequest<RightsInfoParam> {



	@Override
	public String getUrlPath(){
		return "/rights/info";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return RightsInfoResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}