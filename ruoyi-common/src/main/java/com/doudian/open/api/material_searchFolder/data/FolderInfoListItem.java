package com.doudian.open.api.material_searchFolder.data;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;

//auto generated, do not edit

public class FolderInfoListItem {


	@SerializedName("folder_id")
	@OpField(desc = "文件夹id", example = "70032596029638413241510")
	private String folderId;

	@SerializedName("folder_type")
	@OpField(desc = "文件夹类型 0-用户创建 1-默认 2-系统文件夹", example = "0")
	private Integer folderType;

	@SerializedName("name")
	@OpField(desc = "文件夹名称", example = "我是文件夹")
	private String name;

	@SerializedName("operate_status")
	@OpField(desc = "文件夹状态。1-有效 4-在回收站中", example = "1")
	private Integer operateStatus;

	@SerializedName("parent_folder_id")
	@OpField(desc = "父文件夹id", example = "70032596029638413241510")
	private String parentFolderId;

	@SerializedName("create_time")
	@OpField(desc = "文件夹创建时间", example = "2020-09-03 18:28:01")
	private String createTime;

	@SerializedName("update_time")
	@OpField(desc = "文件夹最近一次编辑时间", example = "2020-09-03 18:28:01")
	private String updateTime;

	@SerializedName("delete_time")
	@OpField(desc = "文件夹删除时间。当文件夹在回收站时返回，未删除时为", example = "2020-09-03 18:28:01")
	private String deleteTime;

	@SerializedName("folder_attr")
	@OpField(desc = "文件夹属性，0-文件夹（不限素材类型），1-图片文件夹（只能上传图片），2-视频文件夹（只能上传视频）", example = "0")
	private String folderAttr;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setFolderId(String folderId){
		this.folderId = folderId;
	}

	
	public String getFolderId(){
		return this.folderId;
	}

	
	public void setFolderType(Integer folderType){
		this.folderType = folderType;
	}

	
	public Integer getFolderType(){
		return this.folderType;
	}

	
	public void setName(String name){
		this.name = name;
	}

	
	public String getName(){
		return this.name;
	}

	
	public void setOperateStatus(Integer operateStatus){
		this.operateStatus = operateStatus;
	}

	
	public Integer getOperateStatus(){
		return this.operateStatus;
	}

	
	public void setParentFolderId(String parentFolderId){
		this.parentFolderId = parentFolderId;
	}

	
	public String getParentFolderId(){
		return this.parentFolderId;
	}

	
	public void setCreateTime(String createTime){
		this.createTime = createTime;
	}

	
	public String getCreateTime(){
		return this.createTime;
	}

	
	public void setUpdateTime(String updateTime){
		this.updateTime = updateTime;
	}

	
	public String getUpdateTime(){
		return this.updateTime;
	}

	
	public void setDeleteTime(String deleteTime){
		this.deleteTime = deleteTime;
	}

	
	public String getDeleteTime(){
		return this.deleteTime;
	}

	
	public void setFolderAttr(String folderAttr){
		this.folderAttr = folderAttr;
	}

	
	public String getFolderAttr(){
		return this.folderAttr;
	}

}