package com.doudian.open.api.logistics_appendSubOrder;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.logistics_appendSubOrder.data.*;

//auto generated, do not edit

public class LogisticsAppendSubOrderResponse extends DoudianOpResponse<LogisticsAppendSubOrderData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}