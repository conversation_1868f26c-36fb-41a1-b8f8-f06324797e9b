package com.doudian.open.api.sku_syncStock;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.sku_syncStock.param.*;

//auto generated, do not edit

public class SkuSyncStockRequest extends DoudianOpRequest<SkuSyncStockParam> {



	@Override
	public String getUrlPath(){
		return "/sku/syncStock";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return SkuSyncStockResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}