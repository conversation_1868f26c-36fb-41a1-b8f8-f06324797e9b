package com.doudian.open.api.address_getAreasByProvince;

import java.util.*;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.address_getAreasByProvince.data.*;

//auto generated, do not edit

public class AddressGetAreasByProvinceResponse extends DoudianOpResponse<List<DataItem>> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}