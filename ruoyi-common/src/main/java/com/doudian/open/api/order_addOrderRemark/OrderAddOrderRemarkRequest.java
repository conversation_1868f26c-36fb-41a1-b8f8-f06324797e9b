package com.doudian.open.api.order_addOrderRemark;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.order_addOrderRemark.param.*;

//auto generated, do not edit

public class OrderAddOrderRemarkRequest extends DoudianOpRequest<OrderAddOrderRemarkParam> {



	@Override
	public String getUrlPath(){
		return "/order/addOrderRemark";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return OrderAddOrderRemarkResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}