package com.doudian.open.api.product_removeChannelProduct;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.product_removeChannelProduct.param.*;

//auto generated, do not edit

public class ProductRemoveChannelProductRequest extends DoudianOpRequest<ProductRemoveChannelProductParam> {



	@Override
	public String getUrlPath(){
		return "/product/removeChannelProduct";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return ProductRemoveChannelProductResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}