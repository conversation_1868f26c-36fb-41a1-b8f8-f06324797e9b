package com.doudian.open.api.member_getOutboundId;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.member_getOutboundId.param.*;

//auto generated, do not edit

public class MemberGetOutboundIdRequest extends DoudianOpRequest<MemberGetOutboundIdParam> {



	@Override
	public String getUrlPath(){
		return "/member/getOutboundId";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return MemberGetOutboundIdResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}