package com.doudian.open.api.member_getOutboundId.param;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;

//auto generated, do not edit

public class MemberGetOutboundIdParam {


	@SerializedName("doudian_open_id")
	@OpField(required = true , desc = "订单接口返回的doudian_open_id", example= "111")
	private String doudianOpenId;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setDoudianOpenId(String doudianOpenId){
		this.doudianOpenId = doudianOpenId;
	}

	
	public String getDoudianOpenId(){
		return this.doudianOpenId;
	}

}