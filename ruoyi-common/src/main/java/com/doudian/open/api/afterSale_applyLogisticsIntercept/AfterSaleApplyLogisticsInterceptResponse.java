package com.doudian.open.api.afterSale_applyLogisticsIntercept;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.afterSale_applyLogisticsIntercept.data.*;

//auto generated, do not edit

public class AfterSaleApplyLogisticsInterceptResponse extends DoudianOpResponse<AfterSaleApplyLogisticsInterceptData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}