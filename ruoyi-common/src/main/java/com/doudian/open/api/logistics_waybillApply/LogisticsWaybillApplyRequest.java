package com.doudian.open.api.logistics_waybillApply;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.logistics_waybillApply.param.*;

//auto generated, do not edit

public class LogisticsWaybillApplyRequest extends DoudianOpRequest<LogisticsWaybillApplyParam> {



	@Override
	public String getUrlPath(){
		return "/logistics/waybillApply";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return LogisticsWaybillApplyResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}