package com.doudian.open.api.order_searchByReceiver;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.order_searchByReceiver.data.*;

//auto generated, do not edit

public class OrderSearchByReceiverResponse extends DoudianOpResponse<OrderSearchByReceiverData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}