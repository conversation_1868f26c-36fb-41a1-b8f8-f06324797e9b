package com.doudian.open.api.product_setOnline;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.product_setOnline.param.*;

//auto generated, do not edit

public class ProductSetOnlineRequest extends DoudianOpRequest<ProductSetOnlineParam> {



	@Override
	public String getUrlPath(){
		return "/product/setOnline";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return ProductSetOnlineResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}