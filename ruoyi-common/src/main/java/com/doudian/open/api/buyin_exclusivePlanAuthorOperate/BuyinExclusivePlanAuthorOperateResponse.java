package com.doudian.open.api.buyin_exclusivePlanAuthorOperate;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.buyin_exclusivePlanAuthorOperate.data.*;

//auto generated, do not edit

public class BuyinExclusivePlanAuthorOperateResponse extends DoudianOpResponse<BuyinExclusivePlanAuthorOperateData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}