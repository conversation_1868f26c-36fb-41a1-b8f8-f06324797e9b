package com.doudian.open.api.product_batchGetProductLocks;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.product_batchGetProductLocks.data.*;

//auto generated, do not edit

public class ProductBatchGetProductLocksResponse extends DoudianOpResponse<ProductBatchGetProductLocksData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}