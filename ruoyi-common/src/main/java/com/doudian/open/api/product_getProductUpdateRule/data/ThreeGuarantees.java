package com.doudian.open.api.product_getProductUpdateRule.data;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;
import java.util.List;

//auto generated, do not edit

public class ThreeGuarantees {


	@SerializedName("options")
	@OpField(desc = "三包服务类型，1-寄修，2-延保", example = "")
	private List<OptionsItem_4> options;

	@SerializedName("must_select")
	@OpField(desc = "是否必填", example = "true")
	private Boolean mustSelect;

	@SerializedName("enable")
	@OpField(desc = "是否支持三包服务", example = "true")
	private Boolean enable;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setOptions(List<OptionsItem_4> options){
		this.options = options;
	}

	
	public List<OptionsItem_4> getOptions(){
		return this.options;
	}

	
	public void setMustSelect(Boolean mustSelect){
		this.mustSelect = mustSelect;
	}

	
	public Boolean getMustSelect(){
		return this.mustSelect;
	}

	
	public void setEnable(Boolean enable){
		this.enable = enable;
	}

	
	public Boolean getEnable(){
		return this.enable;
	}

}