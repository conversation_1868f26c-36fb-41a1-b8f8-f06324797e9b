package com.doudian.open.api.member_getOutboundId.data;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;

//auto generated, do not edit

public class MemberGetOutboundIdData {


	@SerializedName("outbound_id")
	@OpField(desc = "外呼id，有效期：1h", example = "17037442535v6Nz7_3u0")
	private String outboundId;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setOutboundId(String outboundId){
		this.outboundId = outboundId;
	}

	
	public String getOutboundId(){
		return this.outboundId;
	}

}