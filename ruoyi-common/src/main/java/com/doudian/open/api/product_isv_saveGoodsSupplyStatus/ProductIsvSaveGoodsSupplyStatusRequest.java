package com.doudian.open.api.product_isv_saveGoodsSupplyStatus;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.product_isv_saveGoodsSupplyStatus.param.*;

//auto generated, do not edit

public class ProductIsvSaveGoodsSupplyStatusRequest extends DoudianOpRequest<ProductIsvSaveGoodsSupplyStatusParam> {



	@Override
	public String getUrlPath(){
		return "/product/isv/saveGoodsSupplyStatus";
	}

	@Override
	public  Class<? extends DoudianOpResponse<?>> getResponseClass(){
		return ProductIsvSaveGoodsSupplyStatusResponse.class;
	}

	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}