package com.doudian.open.api.open_materialToken.data;

import com.doudian.open.gson.annotations.SerializedName;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.annotation.OpField;

//auto generated, do not edit

public class OpenMaterialTokenData {


	@SerializedName("auth_query")
	@OpField(desc = "获取下载地址query", example = "https://imagex.bytedanceapi.com/?Action=ApplyImageUpload&FileExtension=.jpg&ServiceId=0cskzgtrpl&StoreKeys=6864478719709300238%2F666%2F1.jpg&UploadNum=1&Version=2018-08-01&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKLTMzI0ZDYxZjg3MTIwNGQ2Mzg2NGI1ZDI5Y2RiNmVhN2I%2F20230216%2Fcn-north-1%2FImageX%2Faws4_request&X-Amz-Date=20230216T091632Z&X-Amz-Expires=180&X-Amz-NotSignBody=&X-Amz-Signature=f549fadb3d1a98f04c8d29859361957e899323de76dd56bbdcf4379424cc3bb8&X-Amz-SignedHeaders=&X-Amz-SignedQueries=Action%3BFileExtension%3BServiceId%3BStoreKeys%3BUploadNum%3BVersion%3BX-Amz-Algorithm%3BX-Amz-Credential%3BX-Amz-Date%3BX-Amz-Expires%3BX-Amz-NotSignBody%3BX-Amz-SignedHeaders%3BX-Amz-SignedQueries")
	private String authQuery;


	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

	
	public void setAuthQuery(String authQuery){
		this.authQuery = authQuery;
	}

	
	public String getAuthQuery(){
		return this.authQuery;
	}

}