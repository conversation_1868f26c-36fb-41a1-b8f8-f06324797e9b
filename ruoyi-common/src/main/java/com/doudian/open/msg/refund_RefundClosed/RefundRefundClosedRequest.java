package com.doudian.open.msg.refund_RefundClosed;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_RefundClosed.param.*;

//auto generated, do not edit

public class RefundRefundClosedRequest extends DoudianOpMsgRequest<RefundRefundClosedParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}