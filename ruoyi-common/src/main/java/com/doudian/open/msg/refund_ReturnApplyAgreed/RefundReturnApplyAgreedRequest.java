package com.doudian.open.msg.refund_ReturnApplyAgreed;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_ReturnApplyAgreed.param.*;

//auto generated, do not edit

public class RefundReturnApplyAgreedRequest extends DoudianOpMsgRequest<RefundReturnApplyAgreedParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}