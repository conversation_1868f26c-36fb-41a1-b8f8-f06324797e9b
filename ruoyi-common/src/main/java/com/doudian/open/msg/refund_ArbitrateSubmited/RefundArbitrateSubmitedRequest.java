package com.doudian.open.msg.refund_ArbitrateSubmited;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_ArbitrateSubmited.param.*;

//auto generated, do not edit

public class RefundArbitrateSubmitedRequest extends DoudianOpMsgRequest<RefundArbitrateSubmitedParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}