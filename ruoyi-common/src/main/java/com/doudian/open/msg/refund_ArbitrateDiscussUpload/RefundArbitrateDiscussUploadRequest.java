package com.doudian.open.msg.refund_ArbitrateDiscussUpload;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_ArbitrateDiscussUpload.param.*;

//auto generated, do not edit

public class RefundArbitrateDiscussUploadRequest extends DoudianOpMsgRequest<RefundArbitrateDiscussUploadParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}