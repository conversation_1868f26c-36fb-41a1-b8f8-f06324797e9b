package com.doudian.open.msg.refund_ReturnApplyRefused;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_ReturnApplyRefused.param.*;

//auto generated, do not edit

public class RefundReturnApplyRefusedRequest extends DoudianOpMsgRequest<RefundReturnApplyRefusedParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}