package com.doudian.open.msg.refund_ArbitrateSubmiting;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_ArbitrateSubmiting.param.*;

//auto generated, do not edit

public class RefundArbitrateSubmitingRequest extends DoudianOpMsgRequest<RefundArbitrateSubmitingParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}