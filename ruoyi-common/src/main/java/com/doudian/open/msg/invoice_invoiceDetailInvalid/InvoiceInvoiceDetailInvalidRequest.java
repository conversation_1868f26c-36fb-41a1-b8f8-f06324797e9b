package com.doudian.open.msg.invoice_invoiceDetailInvalid;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.invoice_invoiceDetailInvalid.param.*;

//auto generated, do not edit

public class InvoiceInvoiceDetailInvalidRequest extends DoudianOpMsgRequest<InvoiceInvoiceDetailInvalidParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}