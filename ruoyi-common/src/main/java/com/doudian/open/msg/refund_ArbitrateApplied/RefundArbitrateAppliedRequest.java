package com.doudian.open.msg.refund_ArbitrateApplied;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_ArbitrateApplied.param.*;

//auto generated, do not edit

public class RefundArbitrateAppliedRequest extends DoudianOpMsgRequest<RefundArbitrateAppliedParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}