package com.doudian.open.msg.invoice_InvoiceApply;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.invoice_InvoiceApply.param.*;

//auto generated, do not edit

public class InvoiceInvoiceApplyRequest extends DoudianOpMsgRequest<InvoiceInvoiceApplyParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}