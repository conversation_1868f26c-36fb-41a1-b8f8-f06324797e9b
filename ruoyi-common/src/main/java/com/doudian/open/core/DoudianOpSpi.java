package com.doudian.open.core;

import com.doudian.open.utils.ReflectUtil;

import javax.servlet.http.HttpServletRequest;

public class <PERSON>udianOpSpi {

    public static class Configurator {
        <PERSON>udianOpSpiRequest<?> request;

        public String responseJson() {
            return request.responseJson();
        }
    }

    public static Configurator  config(Class<? extends DoudianOpSpiRequest<?>> requestClass, DoudianOpSpiBizHandler bizHandler, HttpServletRequest httpServletRequest) {
        Configurator configurator = new Configurator();
        DoudianOpSpiRequest<?> request = ReflectUtil.newObject(requestClass);
        request.initWithConfig(httpServletRequest);
        request.registerHandler(bizHandler);
        configurator.request = request;
        return configurator;
    }

}
