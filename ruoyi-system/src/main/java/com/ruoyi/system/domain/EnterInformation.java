package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 *  入驻信息对象 enter_information
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@Data
public class EnterInformation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 入驻类型.1个人,2机构 */
    @Excel(name = "入驻类型.1个人,2机构")
    private Integer entityType;

    /** 个人-身份证正面链接,人像面,类型为2的时候为法人正面 */
    @Excel(name = "个人-身份证正面链接,人像面,类型为2的时候为法人正面")
    private String frontPath;

    /** 个人-身份反面链接,国徽面,类型为2的时候为法人反面 */
    @Excel(name = "个人-身份反面链接,国徽面,类型为2的时候为法人反面")
    private String backPath;

    /** 真实姓名,类型为2的时候为法人姓名 */
    @Excel(name = "真实姓名,类型为2的时候为法人姓名")
    private String realName;

    /** 个人-身份证号码,类型为2的时候为法人身份证号码 */
    @Excel(name = "个人-身份证号码,类型为2的时候为法人身份证号码")
    private String idNumber;

    /** 个人-讲师姓名 */
    @Excel(name = "个人-讲师姓名")
    private String teacherName;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String businessLicenseCompanyName;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    private String businessLicenseNo;

    /** 机构-营业执照文件访问路径 */
    @Excel(name = "机构-营业执照文件访问路径")
    private String businessLicensePath;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String telNum;

    /** 课程形式,1 线上课程2线下课程 */
    @Excel(name = "课程形式,1 线上课程2线下课程")
    private Integer courseForm;

    /** 类目选择一级id */
    @Excel(name = "类目选择一级id")
    private Long firstClassId;

    /** 类目选择一级pid */
    @Excel(name = "类目选择一级pid")
    private Long firstClassPid;

    /** 类目选择一级名称 */
    @Excel(name = "类目选择一级名称")
    private String firstClassTitle;

    /** 类目选择一级抖音类目id */
    @Excel(name = "类目选择一级抖音类目id")
    private Long firstClassDouyinClassId;

    /** 类目选择二级id */
    @Excel(name = "类目选择二级id")
    private Long secondClassId;

    /** 类目选择二级pid */
    @Excel(name = "类目选择二级pid")
    private Long secondClassPid;

    /** 类目选择二级名称 */
    @Excel(name = "类目选择二级名称")
    private String secondClassTitle;

    /** 类目选择二级抖音类目id */
    @Excel(name = "类目选择二级抖音类目id")
    private Long secondClassDouyinClassId;

    /** 店铺昵称 */
    @Excel(name = "店铺昵称")
    private String shopNickname;

    /** 店铺头像 */
    @Excel(name = "店铺头像")
    private String shopAvatarUrl;

    /** 店铺介绍 */
    @Excel(name = "店铺介绍")
    private String shopDesc;

    /** 入驻平台，0抖音 1微信 2快手 3视频号 8知识店铺 9pc端 5h5端(wap端) 7小红书, 多个用逗号隔开 */
    @Excel(name = "入驻平台，0抖音 1微信 2快手 3视频号 8知识店铺 9pc端 5h5端(wap端) 7小红书, 多个用逗号隔开")
    private String platform;

    /**  抖音账号 */
    @Excel(name = " 抖音账号")
    private String dyAccount;

    /** 抖音uid */
    @Excel(name = "抖音uid")
    private String dyUid;

    /** 快手账号 */
    @Excel(name = "快手账号")
    private String ksAccount;

    /** 微信账号 */
    @Excel(name = "微信账号")
    private String wxAccount;

    /** 视频号账号 */
    @Excel(name = "视频号账号")
    private String sphAccount;

    /** 服务开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "服务开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date serviceBeginTime;

    /** 服务结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "服务结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date serviceEndTime;

    /** 1.免费版本,2.基础版,3.标准版4专业版5旗舰版 */
    @Excel(name = "1.免费版本,2.基础版,3.标准版4专业版5旗舰版")
    private Integer version;

    /** 抽佣类型1阶梯,2统提 */
    @Excel(name = "抽佣类型1阶梯,2统提")
    private Integer rateType;

    /** 抽佣比例 */
    @Excel(name = "抽佣比例")
    private Long rate;

    /** 开户专员 */
    @Excel(name = "开户专员")
    private String accountSpecialist;

    /** 客服专员 */
    @Excel(name = "客服专员")
    private String customerServiceSpecialist;

    /** 店铺账号 */
    @Excel(name = "店铺账号")
    private String shopAccount;

    /** 店铺id(老师id) */
    @Excel(name = "店铺id(老师id)")
    private Long shopId;

    /** 审核类型 0审核通过 1审核中 2驳回 */
    @Excel(name = "审核类型 0审核通过 1审核中 2驳回")
    private Integer auditType;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    private String rejectReason;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 抖音粉丝数 */
    @Excel(name = "抖音粉丝数")
    private Integer dyFansNum;

    /** 快手粉丝数 */
    @Excel(name = "快手粉丝数")
    private Integer ksFansNum;

    /** 视频号粉丝数 */
    @Excel(name = "视频号粉丝数")
    private Integer sphFansNum;

    /** 抖音主图片地址 */
    @Excel(name = "抖音主图片地址")
    private String dyMasterImg;

    /** 快手主图片地址 */
    @Excel(name = "快手主图片地址")
    private String ksMasterImg;

    /** 微信主图片地址 */
    @Excel(name = "微信主图片地址")
    private String wxMasterImg;

    /** 视频号主图片地址 */
    @Excel(name = "视频号主图片地址")
    private String sphMasterImg;

    /** 店铺所属APP，1问到好课，2问到课堂 */
    @Excel(name = "店铺所属APP，1问到好课，2问到课堂")
    private Integer appNameType;

    /** 抖音抽成比例 */
    @Excel(name = "抖音抽成比例")
    private BigDecimal dyRate;

    /** 快手抽成比例 */
    @Excel(name = "快手抽成比例")
    private BigDecimal ksRate;

    /** 微信抽成比例 */
    @Excel(name = "微信抽成比例")
    private BigDecimal wxRate;

    /** 视频号抽成比例 */
    @Excel(name = "视频号抽成比例")
    private BigDecimal sphRate;

    /** 知识店铺抽成比例 */
    @Excel(name = "知识店铺抽成比例")
    private BigDecimal zsdpRate;

    /** 小红书抽成比例 */
    @Excel(name = "小红书抽成比例")
    private BigDecimal xhsRate;

    /** h5抽成比例 */
    @Excel(name = "h5抽成比例")
    private BigDecimal h5Rate;

    /** pc端抽成比例 */
    @Excel(name = "pc端抽成比例")
    private BigDecimal pcRate;

    /** 是否开通赠送课程功能,0没开通,1开通了 */
    @Excel(name = "是否开通赠送课程功能,0没开通,1开通了")
    private Integer giftCourse;

    /** 是否开通赠送课程功能,0没开通,1开通了 */
    @Excel(name = "是否开通h5直播功能,0没开通,1开通了")
    private Integer wapLiveOpen;

    /** 抖店端抽成比例 */
    @Excel(name = "抖店端抽成比例")
    private BigDecimal ddRate;

    /** 授权的抖店列表 */
    @Excel(name = "授权的抖店列表")
    private String ddShopIds;

    /** 代理商系统或后台系统的部门id */
    @Excel(name = "代理商系统或后台系统的部门id")
    private Long deptId;

    /** 代理商系统或后台系统的用户id */
    @Excel(name = "代理商系统或后台系统的用户id")
    private Long userId;

    /**
     * 是否代理商1是0否
     */
    private Integer isAgent;


    private Integer openPromoter;
    private Integer promoterNum;
}
