package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 员工销售数据统计对象 employee_sales_statistics
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class EmployeeSalesStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 统计ID */
    private Long id;

    /** 统计类型 */
    @Excel(name = "统计类型")
    private String statType;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 商务专员ID */
    @Excel(name = "商务专员ID")
    private Long userId;

    /** 总销售额 */
    @Excel(name = "总销售额")
    private BigDecimal totalSales;

    /** 总订单数 */
    @Excel(name = "总订单数")
    private Integer totalOrders;

    /** 平均订单金额 */
    @Excel(name = "平均订单金额")
    private BigDecimal avgOrderAmount;

    /** 增长率(%) */
    @Excel(name = "增长率(%)")
    private BigDecimal growthRate;

    /** 直接主管id */
    @Excel(name = "直接主管id")
    private Long leaderId;

    /** 直接主管昵称姓名 */
    @Excel(name = "直接主管昵称姓名")
    private String leaderNickName;

    /** 上级主管id */
    @Excel(name = "上级主管id")
    private Long parentLeaderId;

    /** 上级主管昵称姓名 */
    @Excel(name = "上级主管昵称姓名")
    private String parentLeaderNickName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatType(String statType) 
    {
        this.statType = statType;
    }

    public String getStatType() 
    {
        return statType;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setTotalSales(BigDecimal totalSales) 
    {
        this.totalSales = totalSales;
    }

    public BigDecimal getTotalSales() 
    {
        return totalSales;
    }

    public void setTotalOrders(Integer totalOrders) 
    {
        this.totalOrders = totalOrders;
    }

    public Integer getTotalOrders() 
    {
        return totalOrders;
    }

    public void setAvgOrderAmount(BigDecimal avgOrderAmount) 
    {
        this.avgOrderAmount = avgOrderAmount;
    }

    public BigDecimal getAvgOrderAmount() 
    {
        return avgOrderAmount;
    }

    public void setGrowthRate(BigDecimal growthRate) 
    {
        this.growthRate = growthRate;
    }

    public BigDecimal getGrowthRate() 
    {
        return growthRate;
    }

    public void setLeaderId(Long leaderId) 
    {
        this.leaderId = leaderId;
    }

    public Long getLeaderId() 
    {
        return leaderId;
    }

    public void setLeaderNickName(String leaderNickName) 
    {
        this.leaderNickName = leaderNickName;
    }

    public String getLeaderNickName() 
    {
        return leaderNickName;
    }

    public void setParentLeaderId(Long parentLeaderId) 
    {
        this.parentLeaderId = parentLeaderId;
    }

    public Long getParentLeaderId() 
    {
        return parentLeaderId;
    }

    public void setParentLeaderNickName(String parentLeaderNickName) 
    {
        this.parentLeaderNickName = parentLeaderNickName;
    }

    public String getParentLeaderNickName() 
    {
        return parentLeaderNickName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statType", getStatType())
            .append("deptId", getDeptId())
            .append("userId", getUserId())
            .append("totalSales", getTotalSales())
            .append("totalOrders", getTotalOrders())
            .append("avgOrderAmount", getAvgOrderAmount())
            .append("growthRate", getGrowthRate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("leaderId", getLeaderId())
            .append("leaderNickName", getLeaderNickName())
            .append("parentLeaderId", getParentLeaderId())
            .append("parentLeaderNickName", getParentLeaderNickName())
            .toString();
    }
} 