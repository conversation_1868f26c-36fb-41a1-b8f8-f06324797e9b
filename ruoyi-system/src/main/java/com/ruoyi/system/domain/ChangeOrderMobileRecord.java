package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 修改订单手机号码记录对象 change_order_mobile_record
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
public class ChangeOrderMobileRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderId;

    /** 订单里老的用户手机id */
    @Excel(name = "订单里老的用户手机id")
    private String oldBuyerUserMobile;

    /** 订单里新的用户手机id */
    @Excel(name = "订单里新的用户手机id")
    private String newBuyerUserMobile;

    /** 操作员的手机号码 */
    @Excel(name = "操作员的手机号码")
    private String operatorMobile;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setOldBuyerUserMobile(String oldBuyerUserMobile) 
    {
        this.oldBuyerUserMobile = oldBuyerUserMobile;
    }

    public String getOldBuyerUserMobile() 
    {
        return oldBuyerUserMobile;
    }
    public void setNewBuyerUserMobile(String newBuyerUserMobile) 
    {
        this.newBuyerUserMobile = newBuyerUserMobile;
    }

    public String getNewBuyerUserMobile() 
    {
        return newBuyerUserMobile;
    }
    public void setOperatorMobile(String operatorMobile) 
    {
        this.operatorMobile = operatorMobile;
    }

    public String getOperatorMobile() 
    {
        return operatorMobile;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("oldBuyerUserMobile", getOldBuyerUserMobile())
            .append("newBuyerUserMobile", getNewBuyerUserMobile())
            .append("operatorMobile", getOperatorMobile())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
