package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 讲师入驻对象 teacher_enter
 *
 * <AUTHOR>
 * @date 2023-08-19
 */
@Data
public class TeacherEnter extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * openId
     */
    @Excel(name = "openId")
    private String openId;

    /**
     * 入驻讲师姓名
     */
    @Excel(name = "入驻讲师姓名")
    private String teacherEnterName;

    /**
     * 入驻讲师手机号
     */
    @Excel(name = "入驻讲师手机号")
    private Long teacherEnterPhone;

    /**
     * 入驻平台 0抖音 1微信 2快手 3视频号
     */
    @Excel(name = "入驻平台 0抖音 1微信 2快手 3视频号")
    private Integer appPlatform;

    /**
     * 入驻抖音号
     */
    @Excel(name = "入驻抖音号")
    private String teacherEnterDy;

    /**
     * 粉丝数
     */
    @Excel(name = "粉丝数")
    private Integer fansNum;

    /**
     * 邀请码
     */
    @Excel(name = "邀请码")
    private String invitationCode;

    /**
     * 所属类目 ：1传统文化,2体育健身,3家庭教育,4人文,5心理健康,6生活兴趣,7语言培训,8个人提升,9企业管理,10职业考试,11其他
     */
    @Excel(name = "所属类目 ：1传统文化,2体育健身,3家庭教育,4人文,5心理健康,6生活兴趣,7语言培训,8个人提升,9企业管理,10职业考试,11其他")
    private Integer categoryType;

    /**
     * 入驻类型 0个人 1机构
     */
    @Excel(name = "入驻类型 0个人 1机构")
    private Integer teacherEnterType;

    /**
     * 是否有开播经验 0否 1是
     */
    @Excel(name = "是否有开播经验 0否 1是")
    private Integer isExperience;

    /**
     * 是否有自己课程 0否（分销课程） 1是（自制课程）
     */
    @Excel(name = "是否有自己课程 0否", readConverterExp = "分=销课程")
    private Integer isCourse;

    /**
     * 入驻申请处理状态 0待处理 1已处理
     */
    @Excel(name = "入驻申请处理状态 0待处理 1已处理")
    private Integer status;

    /**
     * 入驻讲师姓名
     */
    @Excel(name = "处理人名字")
    private String handleName;


    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    private String wendaoPromoterNickName;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getHandleName() {
        return handleName;
    }

    public void setHandleName(String handleName) {
        this.handleName = handleName;
    }

    public void setTeacherEnterName(String teacherEnterName) {
        this.teacherEnterName = teacherEnterName;
    }

    public String getTeacherEnterName() {
        return teacherEnterName;
    }

    public void setTeacherEnterPhone(Long teacherEnterPhone) {
        this.teacherEnterPhone = teacherEnterPhone;
    }

    public Long getTeacherEnterPhone() {
        return teacherEnterPhone;
    }

    public void setTeacherEnterDy(String teacherEnterDy) {
        this.teacherEnterDy = teacherEnterDy;
    }

    public String getTeacherEnterDy() {
        return teacherEnterDy;
    }

    public void setFansNum(Integer fansNum) {
        this.fansNum = fansNum;
    }

    public Integer getFansNum() {
        return fansNum;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setCategoryType(Integer categoryType) {
        this.categoryType = categoryType;
    }

    public Integer getCategoryType() {
        return categoryType;
    }

    public void setTeacherEnterType(Integer teacherEnterType) {
        this.teacherEnterType = teacherEnterType;
    }

    public Integer getTeacherEnterType() {
        return teacherEnterType;
    }

    public void setIsExperience(Integer isExperience) {
        this.isExperience = isExperience;
    }

    public Integer getIsExperience() {
        return isExperience;
    }

    public void setIsCourse(Integer isCourse) {
        this.isCourse = isCourse;
    }

    public Integer getIsCourse() {
        return isCourse;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("teacherEnterName", getTeacherEnterName())
                .append("teacherEnterPhone", getTeacherEnterPhone())
                .append("teacherEnterDy", getTeacherEnterDy())
                .append("fansNum", getFansNum())
                .append("invitationCode", getInvitationCode())
                .append("categoryType", getCategoryType())
                .append("teacherEnterType", getTeacherEnterType())
                .append("isExperience", getIsExperience())
                .append("isCourse", getIsCourse())
                .append("isDelete", getIsDelete())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("status",getStatus())
                .append("handleName",getHandleName())
                .toString();
    }
}
