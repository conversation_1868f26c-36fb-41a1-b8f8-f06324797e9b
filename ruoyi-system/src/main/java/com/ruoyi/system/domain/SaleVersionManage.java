package com.ruoyi.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 问到系统售卖版本管理对象 sale_version_manage
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
public class SaleVersionManage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 售卖版本控制表主键id */
    private Long id;

    /** 版本名称(必填) */
    @Excel(name = "版本名称(必填)")
    private String versionName;

    /** 版本标志图(可选,每个版本应该有不同的标志) */
    @Excel(name = "版本标志图(可选,每个版本应该有不同的标志)")
    private String versionImage;

    /** 版本id(必填,默认为上一个最大id下一个数字) */
    @Excel(name = "版本id(必填,默认为上一个最大id下一个数字)")
    private Integer versionId;

    /** 版本详细说明(可选) */
    @Excel(name = "版本详细说明(可选)")
    private String versionDesc;

    /** 0禁用,1启动(可选,不填默认启用) */
    @Excel(name = "0禁用,1启动(可选,不填默认启用)")
    private Integer isEnabled;

    /** 版本销售价格(可选,不填为0,销售价格不可退) */
    @Excel(name = "版本销售价格(可选,不填为0,销售价格不可退)")
    private BigDecimal versionPrice;

    /** 版本规定的保证金(可选,不填为0,保证金可退) */
    @Excel(name = "版本规定的保证金(可选,不填为0,保证金可退)")
    private BigDecimal marginMargin;

    /** gmv保证,gmv达成,退保证金 */
    @Excel(name = "gmv保证,gmv达成,退保证金")
    private BigDecimal gmvGuarantee;

    /** Y代表年默认值,M代表月,W代表周,D代表天 */
    @Excel(name = "Y代表年默认值,M代表月,W代表周,D代表天")
    private String versionPurchaseCycle;

    @Excel(name = "版本统一抽成比例,默认10")
    private BigDecimal rate;

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setVersionName(String versionName) 
    {
        this.versionName = versionName;
    }

    public String getVersionName() 
    {
        return versionName;
    }
    public void setVersionImage(String versionImage) 
    {
        this.versionImage = versionImage;
    }

    public String getVersionImage() 
    {
        return versionImage;
    }
    public void setVersionId(Integer versionId) 
    {
        this.versionId = versionId;
    }

    public Integer getVersionId() 
    {
        return versionId;
    }
    public void setVersionDesc(String versionDesc) 
    {
        this.versionDesc = versionDesc;
    }

    public String getVersionDesc() 
    {
        return versionDesc;
    }
    public void setIsEnabled(Integer isEnabled) 
    {
        this.isEnabled = isEnabled;
    }

    public Integer getIsEnabled() 
    {
        return isEnabled;
    }
    public void setVersionPrice(BigDecimal versionPrice) 
    {
        this.versionPrice = versionPrice;
    }

    public BigDecimal getVersionPrice() 
    {
        return versionPrice;
    }
    public void setMarginMargin(BigDecimal marginMargin) 
    {
        this.marginMargin = marginMargin;
    }

    public BigDecimal getMarginMargin() 
    {
        return marginMargin;
    }
    public void setGmvGuarantee(BigDecimal gmvGuarantee) 
    {
        this.gmvGuarantee = gmvGuarantee;
    }

    public BigDecimal getGmvGuarantee() 
    {
        return gmvGuarantee;
    }
    public void setVersionPurchaseCycle(String versionPurchaseCycle) 
    {
        this.versionPurchaseCycle = versionPurchaseCycle;
    }

    public String getVersionPurchaseCycle() 
    {
        return versionPurchaseCycle;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("versionName", getVersionName())
            .append("versionImage", getVersionImage())
            .append("versionId", getVersionId())
            .append("versionDesc", getVersionDesc())
            .append("isEnabled", getIsEnabled())
            .append("versionPrice", getVersionPrice()).append("rate", getVersionPrice())
            .append("marginMargin", getMarginMargin())
            .append("gmvGuarantee", getGmvGuarantee())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("versionPurchaseCycle", getVersionPurchaseCycle())
            .toString();
    }
}
