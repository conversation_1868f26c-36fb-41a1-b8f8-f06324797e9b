package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 员工客户老师数据统计对象 employee_teacher_statistics
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class EmployeeTeacherStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 统计日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statisticsDate;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 老师手机号 */
    @Excel(name = "老师手机号")
    private String mobile;

    /** 店铺名称 */
    @Excel(name = "店铺名称")
    private String shopName;

    /** 店铺所属APP，1问到好课，2问到课堂 */
    @Excel(name = "店铺所属APP")
    private Integer appNameType;

    /** 交易金额 */
    @Excel(name = "交易金额")
    private BigDecimal dealAmount;

    /** 已提现金额 */
    @Excel(name = "已提现金额")
    private BigDecimal withdrawnAmount;

    /** 在途资金 */
    @Excel(name = "在途资金")
    private BigDecimal moneyInTransit;

    /** 可提现金额 */
    @Excel(name = "可提现金额")
    private BigDecimal withdrawableAmount;

    /** 服务费(废弃) */
    @Excel(name = "服务费")
    private BigDecimal serviceFee;

    /** 服务费率(废弃) */
    @Excel(name = "服务费率")
    private BigDecimal serviceFeeRate;

    /** 时间查询区间 */
    @Excel(name = "时间查询区间")
    private String timeQueryStr;

    /** 已提现金额收取的抽佣费(新) */
    @Excel(name = "已提现金额收取的抽佣费")
    private BigDecimal withdrawnAmountFee;

    /** 未提现金额将来要抽的费用(新) */
    @Excel(name = "未提现金额将来要抽的费用")
    private BigDecimal notWithdrawnFee;

    /** 老师所属员工id */
    @Excel(name = "老师所属员工id")
    private Long employeeId;

    /** 员工昵称姓名 */
    @Excel(name = "员工昵称姓名")
    private String nickName;

    /** 直接主管id */
    @Excel(name = "直接主管id")
    private Long leaderId;

    /** 直接主管昵称姓名 */
    @Excel(name = "直接主管昵称姓名")
    private String leaderNickName;

    /** 上级主管id */
    @Excel(name = "上级主管id")
    private Long parentLeaderId;

    /** 上级主管昵称姓名 */
    @Excel(name = "上级主管昵称姓名")
    private String parentLeaderNickName;

    /** 订单数量 */
    @Excel(name = "订单数量")
    private Integer orderNum;

    /** 上一个周期增长率 */
    @Excel(name = "上一个周期增长率")
    private BigDecimal growRatio;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatisticsDate(Date statisticsDate) 
    {
        this.statisticsDate = statisticsDate;
    }

    public Date getStatisticsDate() 
    {
        return statisticsDate;
    }

    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }

    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }

    public void setShopName(String shopName) 
    {
        this.shopName = shopName;
    }

    public String getShopName() 
    {
        return shopName;
    }

    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }

    public void setDealAmount(BigDecimal dealAmount) 
    {
        this.dealAmount = dealAmount;
    }

    public BigDecimal getDealAmount() 
    {
        return dealAmount;
    }

    public void setWithdrawnAmount(BigDecimal withdrawnAmount) 
    {
        this.withdrawnAmount = withdrawnAmount;
    }

    public BigDecimal getWithdrawnAmount() 
    {
        return withdrawnAmount;
    }

    public void setMoneyInTransit(BigDecimal moneyInTransit) 
    {
        this.moneyInTransit = moneyInTransit;
    }

    public BigDecimal getMoneyInTransit() 
    {
        return moneyInTransit;
    }

    public void setWithdrawableAmount(BigDecimal withdrawableAmount) 
    {
        this.withdrawableAmount = withdrawableAmount;
    }

    public BigDecimal getWithdrawableAmount() 
    {
        return withdrawableAmount;
    }

    public void setServiceFee(BigDecimal serviceFee) 
    {
        this.serviceFee = serviceFee;
    }

    public BigDecimal getServiceFee() 
    {
        return serviceFee;
    }

    public void setServiceFeeRate(BigDecimal serviceFeeRate) 
    {
        this.serviceFeeRate = serviceFeeRate;
    }

    public BigDecimal getServiceFeeRate() 
    {
        return serviceFeeRate;
    }

    public void setTimeQueryStr(String timeQueryStr) 
    {
        this.timeQueryStr = timeQueryStr;
    }

    public String getTimeQueryStr() 
    {
        return timeQueryStr;
    }

    public void setWithdrawnAmountFee(BigDecimal withdrawnAmountFee) 
    {
        this.withdrawnAmountFee = withdrawnAmountFee;
    }

    public BigDecimal getWithdrawnAmountFee() 
    {
        return withdrawnAmountFee;
    }

    public void setNotWithdrawnFee(BigDecimal notWithdrawnFee) 
    {
        this.notWithdrawnFee = notWithdrawnFee;
    }

    public BigDecimal getNotWithdrawnFee() 
    {
        return notWithdrawnFee;
    }

    public void setEmployeeId(Long employeeId) 
    {
        this.employeeId = employeeId;
    }

    public Long getEmployeeId() 
    {
        return employeeId;
    }

    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }

    public void setLeaderId(Long leaderId) 
    {
        this.leaderId = leaderId;
    }

    public Long getLeaderId() 
    {
        return leaderId;
    }

    public void setLeaderNickName(String leaderNickName) 
    {
        this.leaderNickName = leaderNickName;
    }

    public String getLeaderNickName() 
    {
        return leaderNickName;
    }

    public void setParentLeaderId(Long parentLeaderId) 
    {
        this.parentLeaderId = parentLeaderId;
    }

    public Long getParentLeaderId() 
    {
        return parentLeaderId;
    }

    public void setParentLeaderNickName(String parentLeaderNickName) 
    {
        this.parentLeaderNickName = parentLeaderNickName;
    }

    public String getParentLeaderNickName() 
    {
        return parentLeaderNickName;
    }

    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }

    public void setGrowRatio(BigDecimal growRatio) 
    {
        this.growRatio = growRatio;
    }

    public BigDecimal getGrowRatio() 
    {
        return growRatio;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statisticsDate", getStatisticsDate())
            .append("teacherId", getTeacherId())
            .append("mobile", getMobile())
            .append("shopName", getShopName())
            .append("appNameType", getAppNameType())
            .append("dealAmount", getDealAmount())
            .append("withdrawnAmount", getWithdrawnAmount())
            .append("moneyInTransit", getMoneyInTransit())
            .append("withdrawableAmount", getWithdrawableAmount())
            .append("serviceFee", getServiceFee())
            .append("serviceFeeRate", getServiceFeeRate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("timeQueryStr", getTimeQueryStr())
            .append("withdrawnAmountFee", getWithdrawnAmountFee())
            .append("notWithdrawnFee", getNotWithdrawnFee())
            .append("employeeId", getEmployeeId())
            .append("nickName", getNickName())
            .append("leaderId", getLeaderId())
            .append("leaderNickName", getLeaderNickName())
            .append("parentLeaderId", getParentLeaderId())
            .append("parentLeaderNickName", getParentLeaderNickName())
            .append("orderNum", getOrderNum())
            .append("growRatio", getGrowRatio())
            .toString();
    }
} 