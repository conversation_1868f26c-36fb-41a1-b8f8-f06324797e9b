package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 员工销售数据统计按日期对象 employee_sales_statistics_days
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class EmployeeSalesStatisticsDays extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 统计ID */
    private Long id;

    /** 统计日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statDate;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 商务专员ID */
    @Excel(name = "商务专员ID")
    private Long userId;

    /** 总销售额 */
    @Excel(name = "总销售额")
    private BigDecimal totalSales;

    /** 直接主管id */
    @Excel(name = "直接主管id")
    private Long leaderId;

    /** 直接主管昵称姓名 */
    @Excel(name = "直接主管昵称姓名")
    private String leaderNickName;

    /** 上级主管id */
    @Excel(name = "上级主管id")
    private Long parentLeaderId;

    /** 上级主管昵称姓名 */
    @Excel(name = "上级主管昵称姓名")
    private String parentLeaderNickName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatDate(Date statDate) 
    {
        this.statDate = statDate;
    }

    public Date getStatDate() 
    {
        return statDate;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setTotalSales(BigDecimal totalSales) 
    {
        this.totalSales = totalSales;
    }

    public BigDecimal getTotalSales() 
    {
        return totalSales;
    }

    public void setLeaderId(Long leaderId) 
    {
        this.leaderId = leaderId;
    }

    public Long getLeaderId() 
    {
        return leaderId;
    }

    public void setLeaderNickName(String leaderNickName) 
    {
        this.leaderNickName = leaderNickName;
    }

    public String getLeaderNickName() 
    {
        return leaderNickName;
    }

    public void setParentLeaderId(Long parentLeaderId) 
    {
        this.parentLeaderId = parentLeaderId;
    }

    public Long getParentLeaderId() 
    {
        return parentLeaderId;
    }

    public void setParentLeaderNickName(String parentLeaderNickName) 
    {
        this.parentLeaderNickName = parentLeaderNickName;
    }

    public String getParentLeaderNickName() 
    {
        return parentLeaderNickName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statDate", getStatDate())
            .append("deptId", getDeptId())
            .append("userId", getUserId())
            .append("totalSales", getTotalSales())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("leaderId", getLeaderId())
            .append("leaderNickName", getLeaderNickName())
            .append("parentLeaderId", getParentLeaderId())
            .append("parentLeaderNickName", getParentLeaderNickName())
            .toString();
    }
} 