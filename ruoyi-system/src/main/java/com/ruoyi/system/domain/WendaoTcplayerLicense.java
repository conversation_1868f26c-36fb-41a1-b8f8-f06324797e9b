package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 腾讯播放器证书对应对象 wendao_tcplayer_license
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public class WendaoTcplayerLicense extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** license_url证书地址 */
    @Excel(name = "license_url证书地址")
    private String licenseUrl;

    /** license_key证书秘钥 */
    @Excel(name = "license_key证书秘钥")
    private String licenseKey;

    /** 老师店铺域名 */
    @Excel(name = "老师店铺域名")
    private String knowledgeStoreDomain;

    /** 是否使用0未使用,1已使用 */
    @Excel(name = "是否使用0未使用,1已使用")
    private Integer isUsed;

    /** 课程删除0未删除，1已删除 */
    @Excel(name = "课程删除0未删除，1已删除")
    private Integer isDelete;

    /** 有效期开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "有效期开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date validStartTime;

    /** 有效期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "有效期结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date validEndTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setLicenseUrl(String licenseUrl) 
    {
        this.licenseUrl = licenseUrl;
    }

    public String getLicenseUrl() 
    {
        return licenseUrl;
    }
    public void setLicenseKey(String licenseKey) 
    {
        this.licenseKey = licenseKey;
    }

    public String getLicenseKey() 
    {
        return licenseKey;
    }
    public void setKnowledgeStoreDomain(String knowledgeStoreDomain) 
    {
        this.knowledgeStoreDomain = knowledgeStoreDomain;
    }

    public String getKnowledgeStoreDomain() 
    {
        return knowledgeStoreDomain;
    }
    public void setIsUsed(Integer isUsed) 
    {
        this.isUsed = isUsed;
    }

    public Integer getIsUsed() 
    {
        return isUsed;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }
    public void setValidStartTime(Date validStartTime) 
    {
        this.validStartTime = validStartTime;
    }

    public Date getValidStartTime() 
    {
        return validStartTime;
    }
    public void setValidEndTime(Date validEndTime) 
    {
        this.validEndTime = validEndTime;
    }

    public Date getValidEndTime() 
    {
        return validEndTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("licenseUrl", getLicenseUrl())
            .append("licenseKey", getLicenseKey())
            .append("knowledgeStoreDomain", getKnowledgeStoreDomain())
            .append("isUsed", getIsUsed())
            .append("isDelete", getIsDelete())
            .append("validStartTime", getValidStartTime())
            .append("validEndTime", getValidEndTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
