package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 老师购买的资源包对象 teacher_resource_package
 */
@Data
public class TeacherResourcePackage
{
    /** 老师购买的资源包主键id */
    private Long pkgId;

    /** 定义的资源包主键id */
    private Long resourcePkgId;

    /** 老师id */
    private Long teacherId;

    /** 资源包名称 */
    private String resourcePkgName;

    /** 资源包定价 */
    private BigDecimal pkgPrice;

    /** 有效期(单位天) */
    private Integer validityPeriod;

    /** 资源包包含原始资源数量 */
    private Integer resourceCount;

    /** 资源包中剩余资源数量 */
    private Integer remainingCount;

    /** 是否生效,1生效,0不生效,如果时间过了失效时间就失效 */
    private Integer isEnable;

    /** 关联功能代码 */
    private String featureCode;

    /** 资源包生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 资源包失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 