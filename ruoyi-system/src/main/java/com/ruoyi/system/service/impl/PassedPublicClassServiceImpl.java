package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.PassedPublicClassMapper;
import com.ruoyi.system.domain.PassedPublicClass;
import com.ruoyi.system.service.IPassedPublicClassService;

/**
 * 公共资质授权Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@Service
public class PassedPublicClassServiceImpl implements IPassedPublicClassService 
{
    @Autowired
    private PassedPublicClassMapper passedPublicClassMapper;

    /**
     * 查询公共资质授权
     * 
     * @param id 公共资质授权主键
     * @return 公共资质授权
     */
    @Override
    public PassedPublicClass selectPassedPublicClassById(Long id)
    {
        return passedPublicClassMapper.selectPassedPublicClassById(id);
    }

    /**
     * 查询公共资质授权列表
     * 
     * @param passedPublicClass 公共资质授权
     * @return 公共资质授权
     */
    @Override
    public List<PassedPublicClass> selectPassedPublicClassList(PassedPublicClass passedPublicClass)
    {
        return passedPublicClassMapper.selectPassedPublicClassList(passedPublicClass);
    }

    /**
     * 新增公共资质授权
     * 
     * @param passedPublicClass 公共资质授权
     * @return 结果
     */
    @Override
    public int insertPassedPublicClass(PassedPublicClass passedPublicClass)
    {
        passedPublicClass.setCreateTime(DateUtils.getNowDate());
        return passedPublicClassMapper.insertPassedPublicClass(passedPublicClass);
    }

    /**
     * 修改公共资质授权
     * 
     * @param passedPublicClass 公共资质授权
     * @return 结果
     */
    @Override
    public int updatePassedPublicClass(PassedPublicClass passedPublicClass)
    {
        passedPublicClass.setUpdateTime(DateUtils.getNowDate());
        return passedPublicClassMapper.updatePassedPublicClass(passedPublicClass);
    }

    /**
     * 批量删除公共资质授权
     * 
     * @param ids 需要删除的公共资质授权主键
     * @return 结果
     */
    @Override
    public int deletePassedPublicClassByIds(Long[] ids)
    {
        return passedPublicClassMapper.deletePassedPublicClassByIds(ids);
    }

    /**
     * 删除公共资质授权信息
     * 
     * @param id 公共资质授权主键
     * @return 结果
     */
    @Override
    public int deletePassedPublicClassById(Long id)
    {
        return passedPublicClassMapper.deletePassedPublicClassById(id);
    }
}
