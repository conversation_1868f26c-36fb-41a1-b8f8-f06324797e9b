package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.PublicWendaoCertInfoMapper;
import com.ruoyi.system.domain.PublicWendaoCertInfo;
import com.ruoyi.system.service.IPublicWendaoCertInfoService;

/**
 * 资质中心信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@Service
public class PublicWendaoCertInfoServiceImpl implements IPublicWendaoCertInfoService 
{
    @Autowired
    private PublicWendaoCertInfoMapper publicWendaoCertInfoMapper;

    /**
     * 查询资质中心信息
     * 
     * @param id 资质中心信息主键
     * @return 资质中心信息
     */
    @Override
    public PublicWendaoCertInfo selectPublicWendaoCertInfoById(Long id)
    {
        return publicWendaoCertInfoMapper.selectPublicWendaoCertInfoById(id);
    }

    /**
     * 查询资质中心信息列表
     * 
     * @param publicWendaoCertInfo 资质中心信息
     * @return 资质中心信息
     */
    @Override
    public List<PublicWendaoCertInfo> selectPublicWendaoCertInfoList(PublicWendaoCertInfo publicWendaoCertInfo)
    {
        return publicWendaoCertInfoMapper.selectPublicWendaoCertInfoList(publicWendaoCertInfo);
    }

    /**
     * 新增资质中心信息
     * 
     * @param publicWendaoCertInfo 资质中心信息
     * @return 结果
     */
    @Override
    public int insertPublicWendaoCertInfo(PublicWendaoCertInfo publicWendaoCertInfo)
    {
        publicWendaoCertInfo.setCreateTime(DateUtils.getNowDate());
        return publicWendaoCertInfoMapper.insertPublicWendaoCertInfo(publicWendaoCertInfo);
    }

    /**
     * 修改资质中心信息
     * 
     * @param publicWendaoCertInfo 资质中心信息
     * @return 结果
     */
    @Override
    public int updatePublicWendaoCertInfo(PublicWendaoCertInfo publicWendaoCertInfo)
    {
        publicWendaoCertInfo.setUpdateTime(DateUtils.getNowDate());
        return publicWendaoCertInfoMapper.updatePublicWendaoCertInfo(publicWendaoCertInfo);
    }

    /**
     * 批量删除资质中心信息
     * 
     * @param ids 需要删除的资质中心信息主键
     * @return 结果
     */
    @Override
    public int deletePublicWendaoCertInfoByIds(Long[] ids)
    {
        return publicWendaoCertInfoMapper.deletePublicWendaoCertInfoByIds(ids);
    }

    /**
     * 删除资质中心信息信息
     * 
     * @param id 资质中心信息主键
     * @return 结果
     */
    @Override
    public int deletePublicWendaoCertInfoById(Long id)
    {
        return publicWendaoCertInfoMapper.deletePublicWendaoCertInfoById(id);
    }

    @Override
    public PublicWendaoCertInfo selectPublicWendaoCertInfoByEntityId(String entityId) {
        return publicWendaoCertInfoMapper.selectPublicWendaoCertInfoByEntityId(entityId);
    }

    @Override
    public int updatePublicWendaoCertInfo1(PublicWendaoCertInfo publicWendaoCertInfo) {
        publicWendaoCertInfoMapper.updatePublicWendaoCertInfo2(publicWendaoCertInfo);
        return publicWendaoCertInfoMapper.updatePublicWendaoCertInfo1(publicWendaoCertInfo);
    }

    @Override
    public int updatePublicWendaoCertInfo2(PublicWendaoCertInfo publicWendaoCertInfo) {
        return publicWendaoCertInfoMapper.updatePublicWendaoCertInfo2(publicWendaoCertInfo);
    }

    @Override
    public int insertPublicWendaoCertInfoWendao(PublicWendaoCertInfo publicWendaoCertInfo) {
        return publicWendaoCertInfoMapper.insertPublicWendaoCertInfoWendao(publicWendaoCertInfo);
    }
}
