package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.EnterInformationBackupMapper;
import com.ruoyi.system.domain.EnterInformationBackup;
import com.ruoyi.system.service.IEnterInformationBackupService;

/**
 * 入驻信息备份Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@Service
public class EnterInformationBackupServiceImpl implements IEnterInformationBackupService 
{
    @Autowired
    private EnterInformationBackupMapper enterInformationBackupMapper;

    /**
     * 查询入驻信息备份
     * 
     * @param id 入驻信息备份主键
     * @return 入驻信息备份
     */
    @Override
    public EnterInformationBackup selectEnterInformationBackupById(Long id)
    {
        return enterInformationBackupMapper.selectEnterInformationBackupById(id);
    }

    /**
     * 查询入驻信息备份列表
     * 
     * @param enterInformationBackup 入驻信息备份
     * @return 入驻信息备份
     */
    @Override
    public List<EnterInformationBackup> selectEnterInformationBackupList(EnterInformationBackup enterInformationBackup)
    {
        return enterInformationBackupMapper.selectEnterInformationBackupList(enterInformationBackup);
    }

    /**
     * 新增入驻信息备份
     * 
     * @param enterInformationBackup 入驻信息备份
     * @return 结果
     */
    @Override
    public int insertEnterInformationBackup(EnterInformationBackup enterInformationBackup)
    {
        enterInformationBackup.setCreateTime(DateUtils.getNowDate());
        return enterInformationBackupMapper.insertEnterInformationBackup(enterInformationBackup);
    }

    /**
     * 修改入驻信息备份
     * 
     * @param enterInformationBackup 入驻信息备份
     * @return 结果
     */
    @Override
    public int updateEnterInformationBackup(EnterInformationBackup enterInformationBackup)
    {
        enterInformationBackup.setUpdateTime(DateUtils.getNowDate());
        return enterInformationBackupMapper.updateEnterInformationBackup(enterInformationBackup);
    }

    /**
     * 批量删除入驻信息备份
     * 
     * @param ids 需要删除的入驻信息备份主键
     * @return 结果
     */
    @Override
    public int deleteEnterInformationBackupByIds(Long[] ids)
    {
        return enterInformationBackupMapper.deleteEnterInformationBackupByIds(ids);
    }

    /**
     * 删除入驻信息备份信息
     * 
     * @param id 入驻信息备份主键
     * @return 结果
     */
    @Override
    public int deleteEnterInformationBackupById(Long id)
    {
        return enterInformationBackupMapper.deleteEnterInformationBackupById(id);
    }
    
    /**
     * 根据客户端入驻信息ID查询备份信息
     *
     * @param clientEnterInformationId 客户端入驻信息ID
     * @return 入驻信息备份
     */
    @Override
    public EnterInformationBackup selectEnterInformationBackupByClientId(Long clientEnterInformationId) {
        return enterInformationBackupMapper.selectEnterInformationBackupByClientId(clientEnterInformationId);
    }
} 