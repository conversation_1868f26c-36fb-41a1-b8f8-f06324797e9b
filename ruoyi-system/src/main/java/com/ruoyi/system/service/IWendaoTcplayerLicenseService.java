package com.ruoyi.system.service;


import com.ruoyi.system.domain.WendaoTcplayerLicense;

import java.util.List;

/**
 * 腾讯播放器证书对应Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface IWendaoTcplayerLicenseService 
{
    /**
     * 查询腾讯播放器证书对应
     * 
     * @param id 腾讯播放器证书对应主键
     * @return 腾讯播放器证书对应
     */
    public WendaoTcplayerLicense selectWendaoTcplayerLicenseById(Long id);

    /**
     * 查询腾讯播放器证书对应列表
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 腾讯播放器证书对应集合
     */
    public List<WendaoTcplayerLicense> selectWendaoTcplayerLicenseList(WendaoTcplayerLicense wendaoTcplayerLicense);

    /**
     * 新增腾讯播放器证书对应
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 结果
     */
    public int insertWendaoTcplayerLicense(WendaoTcplayerLicense wendaoTcplayerLicense);

    /**
     * 修改腾讯播放器证书对应
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 结果
     */
    public int updateWendaoTcplayerLicense(WendaoTcplayerLicense wendaoTcplayerLicense);

    /**
     * 批量删除腾讯播放器证书对应
     * 
     * @param ids 需要删除的腾讯播放器证书对应主键集合
     * @return 结果
     */
    public int deleteWendaoTcplayerLicenseByIds(Long[] ids);

    /**
     * 删除腾讯播放器证书对应信息
     * 
     * @param id 腾讯播放器证书对应主键
     * @return 结果
     */
    public int deleteWendaoTcplayerLicenseById(Long id);

    WendaoTcplayerLicense selectNoUsedDomainRandom();

    WendaoTcplayerLicense selectByDomain(String knowledgeStoreDomain);
}
