package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.PublicWendaoCertInfo;

/**
 * 资质中心信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
public interface IPublicWendaoCertInfoService 
{
    /**
     * 查询资质中心信息
     * 
     * @param id 资质中心信息主键
     * @return 资质中心信息
     */
    public PublicWendaoCertInfo selectPublicWendaoCertInfoById(Long id);

    /**
     * 查询资质中心信息列表
     * 
     * @param publicWendaoCertInfo 资质中心信息
     * @return 资质中心信息集合
     */
    public List<PublicWendaoCertInfo> selectPublicWendaoCertInfoList(PublicWendaoCertInfo publicWendaoCertInfo);

    /**
     * 新增资质中心信息
     * 
     * @param publicWendaoCertInfo 资质中心信息
     * @return 结果
     */
    public int insertPublicWendaoCertInfo(PublicWendaoCertInfo publicWendaoCertInfo);

    /**
     * 修改资质中心信息
     * 
     * @param publicWendaoCertInfo 资质中心信息
     * @return 结果
     */
    public int updatePublicWendaoCertInfo(PublicWendaoCertInfo publicWendaoCertInfo);

    /**
     * 批量删除资质中心信息
     * 
     * @param ids 需要删除的资质中心信息主键集合
     * @return 结果
     */
    public int deletePublicWendaoCertInfoByIds(Long[] ids);

    /**
     * 删除资质中心信息信息
     * 
     * @param id 资质中心信息主键
     * @return 结果
     */
    public int deletePublicWendaoCertInfoById(Long id);

    PublicWendaoCertInfo selectPublicWendaoCertInfoByEntityId(String entityId);

    int updatePublicWendaoCertInfo1(PublicWendaoCertInfo publicWendaoCertInfo);
    int updatePublicWendaoCertInfo2(PublicWendaoCertInfo publicWendaoCertInfo);

    int insertPublicWendaoCertInfoWendao(PublicWendaoCertInfo publicWendaoCertInfo);

}
