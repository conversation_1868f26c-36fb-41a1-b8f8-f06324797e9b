package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SaleVersionManageMapper;
import com.ruoyi.system.domain.SaleVersionManage;
import com.ruoyi.system.service.ISaleVersionManageService;

/**
 * 问到系统售卖版本管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Service
public class SaleVersionManageServiceImpl implements ISaleVersionManageService 
{
    @Autowired
    private SaleVersionManageMapper saleVersionManageMapper;

    /**
     * 查询问到系统售卖版本管理
     * 
     * @param id 问到系统售卖版本管理主键
     * @return 问到系统售卖版本管理
     */
    @Override
    public SaleVersionManage selectSaleVersionManageById(Long id)
    {
        return saleVersionManageMapper.selectSaleVersionManageById(id);
    }

    /**
     * 查询问到系统售卖版本管理列表
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 问到系统售卖版本管理
     */
    @Override
    public List<SaleVersionManage> selectSaleVersionManageList(SaleVersionManage saleVersionManage)
    {
        return saleVersionManageMapper.selectSaleVersionManageList(saleVersionManage);
    }

    /**
     * 新增问到系统售卖版本管理
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 结果
     */
    @Override
    public int insertSaleVersionManage(SaleVersionManage saleVersionManage)
    {
        saleVersionManage.setCreateTime(DateUtils.getNowDate());
        return saleVersionManageMapper.insertSaleVersionManage(saleVersionManage);
    }

    /**
     * 修改问到系统售卖版本管理
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 结果
     */
    @Override
    public int updateSaleVersionManage(SaleVersionManage saleVersionManage)
    {
        saleVersionManage.setUpdateTime(DateUtils.getNowDate());
        return saleVersionManageMapper.updateSaleVersionManage(saleVersionManage);
    }

    /**
     * 批量删除问到系统售卖版本管理
     * 
     * @param ids 需要删除的问到系统售卖版本管理主键
     * @return 结果
     */
    @Override
    public int deleteSaleVersionManageByIds(Long[] ids)
    {
        return saleVersionManageMapper.deleteSaleVersionManageByIds(ids);
    }

    /**
     * 删除问到系统售卖版本管理信息
     * 
     * @param id 问到系统售卖版本管理主键
     * @return 结果
     */
    @Override
    public int deleteSaleVersionManageById(Long id)
    {
        return saleVersionManageMapper.deleteSaleVersionManageById(id);
    }

    @Override
    public Integer selectMaxVersionId() {
        return saleVersionManageMapper.selectMaxVersionId();
    }
}
