package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.PassedPublicClass;

/**
 * 公共资质授权Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
public interface IPassedPublicClassService 
{
    /**
     * 查询公共资质授权
     * 
     * @param id 公共资质授权主键
     * @return 公共资质授权
     */
    public PassedPublicClass selectPassedPublicClassById(Long id);

    /**
     * 查询公共资质授权列表
     * 
     * @param passedPublicClass 公共资质授权
     * @return 公共资质授权集合
     */
    public List<PassedPublicClass> selectPassedPublicClassList(PassedPublicClass passedPublicClass);

    /**
     * 新增公共资质授权
     * 
     * @param passedPublicClass 公共资质授权
     * @return 结果
     */
    public int insertPassedPublicClass(PassedPublicClass passedPublicClass);

    /**
     * 修改公共资质授权
     * 
     * @param passedPublicClass 公共资质授权
     * @return 结果
     */
    public int updatePassedPublicClass(PassedPublicClass passedPublicClass);

    /**
     * 批量删除公共资质授权
     * 
     * @param ids 需要删除的公共资质授权主键集合
     * @return 结果
     */
    public int deletePassedPublicClassByIds(Long[] ids);

    /**
     * 删除公共资质授权信息
     * 
     * @param id 公共资质授权主键
     * @return 结果
     */
    public int deletePassedPublicClassById(Long id);
}
