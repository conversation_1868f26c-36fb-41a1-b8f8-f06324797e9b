package com.ruoyi.system.service.impl;

import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.EmployeeSalesStatisticsDaysMapper;
import com.ruoyi.system.domain.EmployeeSalesStatisticsDays;
import com.ruoyi.system.service.IEmployeeSalesStatisticsDaysService;

/**
 * 员工销售数据统计按日期Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class EmployeeSalesStatisticsDaysServiceImpl implements IEmployeeSalesStatisticsDaysService 
{
    @Autowired
    private EmployeeSalesStatisticsDaysMapper employeeSalesStatisticsDaysMapper;

    /**
     * 查询员工销售数据统计按日期
     * 
     * @param id 员工销售数据统计按日期主键
     * @return 员工销售数据统计按日期
     */
    @Override
    public EmployeeSalesStatisticsDays selectEmployeeSalesStatisticsDaysById(Long id)
    {
        return employeeSalesStatisticsDaysMapper.selectEmployeeSalesStatisticsDaysById(id);
    }

    /**
     * 查询员工销售数据统计按日期列表
     * 
     * @param employeeSalesStatisticsDays 员工销售数据统计按日期
     * @return 员工销售数据统计按日期
     */
    @Override
    public List<EmployeeSalesStatisticsDays> selectEmployeeSalesStatisticsDaysList(EmployeeSalesStatisticsDays employeeSalesStatisticsDays)
    {
        return employeeSalesStatisticsDaysMapper.selectEmployeeSalesStatisticsDaysList(employeeSalesStatisticsDays);
    }

    /**
     * 新增员工销售数据统计按日期
     * 
     * @param employeeSalesStatisticsDays 员工销售数据统计按日期
     * @return 结果
     */
    @Override
    public int insertEmployeeSalesStatisticsDays(EmployeeSalesStatisticsDays employeeSalesStatisticsDays)
    {
        return employeeSalesStatisticsDaysMapper.insertEmployeeSalesStatisticsDays(employeeSalesStatisticsDays);
    }

    /**
     * 修改员工销售数据统计按日期
     * 
     * @param employeeSalesStatisticsDays 员工销售数据统计按日期
     * @return 结果
     */
    @Override
    public int updateEmployeeSalesStatisticsDays(EmployeeSalesStatisticsDays employeeSalesStatisticsDays)
    {
        return employeeSalesStatisticsDaysMapper.updateEmployeeSalesStatisticsDays(employeeSalesStatisticsDays);
    }

    /**
     * 批量删除员工销售数据统计按日期
     * 
     * @param ids 需要删除的员工销售数据统计按日期主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeSalesStatisticsDaysByIds(Long[] ids)
    {
        return employeeSalesStatisticsDaysMapper.deleteEmployeeSalesStatisticsDaysByIds(ids);
    }

    /**
     * 删除员工销售数据统计按日期信息
     * 
     * @param id 员工销售数据统计按日期主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeSalesStatisticsDaysById(Long id)
    {
        return employeeSalesStatisticsDaysMapper.deleteEmployeeSalesStatisticsDaysById(id);
    }

    @Override
    public List<EmployeeSalesStatisticsDays> selectEmployeeSalesStatisticsDaysListSumParentLeaderId(EmployeeSalesStatisticsDays employeeSalesStatisticsDays) {
        return employeeSalesStatisticsDaysMapper.selectEmployeeSalesStatisticsDaysListSumParentLeaderId(employeeSalesStatisticsDays);
    }

    @Override
    public List<EmployeeSalesStatisticsDays> selectEmployeeSalesStatisticsDaysListSumLeaderId(EmployeeSalesStatisticsDays employeeSalesStatisticsDays) {
        return employeeSalesStatisticsDaysMapper.selectEmployeeSalesStatisticsDaysListSumLeaderId(employeeSalesStatisticsDays);
    }
} 