package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.doudian.open.api.sms_send.SmsSendRequest;
import com.doudian.open.api.sms_send.SmsSendResponse;
import com.doudian.open.api.sms_send.param.SmsSendParam;
import com.doudian.open.api.token_create.TokenCreateRequest;
import com.doudian.open.api.token_create.TokenCreateResponse;
import com.doudian.open.api.token_create.param.TokenCreateParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.GlobalConfig;
import com.google.gson.Gson;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.SmsService;
import darabonba.core.client.ClientOverrideConfiguration;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Service
public class SmsServiceImpl implements SmsService {

    // 云片API密钥
    private static final String APIKEY = "102222afce402a62dd7b44276e736fff";
    private static final String ENCODING = "UTF-8";
    private static final String API_URL = "https://sms.yunpian.com/v2/sms/tpl_single_send.json";

    @Autowired
    private RedisCache redisCache;
    private static final String doudian_access_token_prefix = "doudian_access_token:";

    //章鱼博士课堂
    private static final Long zyShopId = 184833512L;

    //成美知识小店
    private static final Long cmShopId = 188161674L;

    static {
        GlobalConfig.initAppKey("7406208857338316322");
        GlobalConfig.initAppSecret("7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
    }

    @Autowired
    private IEnterInformationService enterInformationService;


    @Override
    public void sendCreateUserAccount(String phoneNumber, String userName, String password, Integer appNameType) {
        try {
            sendUserNamePwd(phoneNumber, userName, password, appNameType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String sendValidateCodeSms(String phoneNumber, String code) {
        String uuid = send(phoneNumber, code);
        //插入记录
        int row = enterInformationService.insertSmsRecord(phoneNumber, code, uuid);
        return uuid;
    }

    @Override
    public void doudianSmsResend(String phoneNumber, String productName, String code) {
        try {
            sendDoudianLearnSms(phoneNumber, "课程", code);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void doudianSmsResendByDoudian(String phoneNumber, String productName, String code) {

        String smsUrl = "go.wendao101.com/dd/" + code;
        //发送短信链接1
//        {
//            AccessToken accessToken = getAccessToken(zyShopId);
//            SmsSendRequest request = new SmsSendRequest();
//            SmsSendParam param = request.getParam();
//            param.setSmsAccount("7f07982f");
//            param.setSign("问到课堂");
//            param.setTemplateId("ST_7f078d7a");
//            param.setTemplateParam("{\"productName\":\"" + "课程" + "\",\"learnUrl\":\"" + smsUrl + "\"}");
//            param.setPostTel(phoneNumber);
//            SmsSendResponse response = request.execute(accessToken);
//            System.out.println(JSON.toJSONString(response));
//        }
        //发送短信链接2
        {
            AccessToken accessToken = getAccessToken(cmShopId);
            SmsSendRequest request = new SmsSendRequest();
            SmsSendParam param = request.getParam();
            param.setSmsAccount("7f07982f");
            param.setSign("成美知识小店");
            param.setTemplateId("ST_7f474b0e");
            param.setTemplateParam("{\"productName\":\"" + "课程" + "\",\"learnUrl\":\"" + smsUrl + "\"}");
            param.setPostTel(phoneNumber);
            SmsSendResponse response = request.execute(accessToken);
            System.out.println(JSON.toJSONString(response));
        }
//        {
//            AccessToken accessToken = getAccessToken(cmShopId);
//            SmsSendRequest request = new SmsSendRequest();
//            SmsSendParam param = request.getParam();
//            param.setSmsAccount("7f07982f");
//            param.setSign("成美知识小店");
//            param.setTemplateId("ST_7f474b0e");
//            param.setTemplateParam("{\"productName\":\"" + "课程" + "\",\"learnUrl\":\"" + smsUrl + "\"}");
//            param.setPostTel(phoneNumber);
//            SmsSendResponse response = request.execute(accessToken);
//            System.out.println(JSON.toJSONString(response));
//        }

    }

    private AccessToken getAccessToken(Long shopId) {
        String shopIdStr = String.valueOf(shopId);
        AccessToken accessToken = redisCache.getCacheObject(doudian_access_token_prefix + shopIdStr);
        if (accessToken == null) {
            TokenCreateRequest request = new TokenCreateRequest();
            TokenCreateParam param = request.getParam();
            param.setCode("");
            param.setGrantType("authorization_self");
            param.setShopId(shopIdStr);
            TokenCreateResponse response = request.execute(null);
            String jsonString = JSON.toJSONString(response);
            System.out.println("创建token成功!token内容:" + jsonString);
            accessToken = JSON.parseObject(jsonString, AccessToken.class);
            redisCache.setCacheObjectLong(doudian_access_token_prefix + shopIdStr, accessToken, accessToken.getExpireIn() - 600L, TimeUnit.SECONDS);
        }
        return accessToken;
    }

    /**
     * 发送验证码
     *
     * @param phoneNumber
     * @param code
     * @return
     * @throws Exception
     */
    private String send(String phoneNumber, String code) {
        String uuid = UUID.randomUUID().toString();
        //sendCode(phoneNumber, code);
        try {
            StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                    .accessKeyId("LTAI5tRfCNfcpvFCRaM1ydiy")
                    .accessKeySecret("******************************")
                    .build());

            AsyncClient client = AsyncClient.builder()
                    .region("cn-hangzhou") // Region ID
                    .credentialsProvider(provider)
                    .overrideConfiguration(
                            ClientOverrideConfiguration.create()
                                    .setEndpointOverride("dysmsapi.aliyuncs.com")
                    )
                    .build();

            SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                    .signName("问到")
                    .templateCode("SMS_244365161")
                    .phoneNumbers(phoneNumber)
                    .templateParam("{\"code\":\"" + code + "\"}")
                    .build();

            CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
            SendSmsResponse resp = response.get();
            System.out.println(new Gson().toJson(resp));
            client.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return uuid;
    }

//    private void sendCode(String mobile,String code) {
//        // 接收短信的手机号码
//        //String mobile = "18667016502";
//        // 模板ID
//        long tplId = 6056480;
//
//        // 构建模板参数
//        Map<String, String> tplParams = new HashMap<>();
//        tplParams.put("code", code);
//
//        try {
//            String result = tplSingleSend(mobile, tplId, tplParams);
//            System.out.println("发送结果：" + result);
//        } catch (Exception e) {
//            System.err.println("发送失败：" + e.getMessage());
//        }
//    }

    private String tplSingleSend(String mobile, long tplId, Map<String, String> tplParams) throws IOException {
        Map<String, String> params = new HashMap<>();
        params.put("apikey", APIKEY);
        params.put("mobile", mobile);
        params.put("tpl_id", String.valueOf(tplId));

        // 构建模板变量值字符串
        StringBuilder tplValue = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : tplParams.entrySet()) {
            if (!first) {
                tplValue.append("&");
            }
            tplValue.append(URLEncoder.encode("#" + entry.getKey() + "#", ENCODING))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue(), ENCODING));
            first = false;
        }
        params.put("tpl_value", tplValue.toString());

        return post(API_URL, params);
    }

    private String post(String url, Map<String, String> params) throws IOException {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);

        // 构建表单参数
        StringBuilder postData = new StringBuilder();
        for (Map.Entry<String, String> param : params.entrySet()) {
            if (postData.length() != 0) {
                postData.append('&');
            }
            postData.append(URLEncoder.encode(param.getKey(), ENCODING));
            postData.append('=');
            postData.append(URLEncoder.encode(param.getValue(), ENCODING));
        }

        // 设置请求头
        post.setHeader("Accept", "application/json;charset=utf-8");
        post.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

        // 设置请求体
        StringEntity entity = new StringEntity(postData.toString());
        post.setEntity(entity);

        // 发送请求并获取响应
        HttpResponse response = client.execute(post);
        String result = EntityUtils.toString(response.getEntity(), ENCODING);

        client.close();
        return result;
    }

    private void sendUserNamePwd(String phoneNumber, String userName, String password, Integer appNameType) throws Exception {
        //String uuid = UUID.randomUUID().toString();

        // HttpClient Configuration
        /*HttpClient httpClient = new ApacheAsyncHttpClientBuilder()
                .connectionTimeout(Duration.ofSeconds(10)) // Set the connection timeout time, the default is 10 seconds
                .responseTimeout(Duration.ofSeconds(10)) // Set the response timeout time, the default is 20 seconds
                .maxConnections(128) // Set the connection pool size
                .maxIdleTimeOut(Duration.ofSeconds(50)) // Set the connection pool timeout, the default is 30 seconds
                // Configure the proxy
                .proxy(new ProxyOptions(ProxyOptions.Type.HTTP, new InetSocketAddress("<your-proxy-hostname>", 9001))
                        .setCredentials("<your-proxy-username>", "<your-proxy-password>"))
                // If it is an https connection, you need to configure the certificate, or ignore the certificate(.ignoreSSL(true))
                .x509TrustManagers(new X509TrustManager[]{})
                .keyManagers(new KeyManager[]{})
                .ignoreSSL(false)
                .build();*/

        // Configure Credentials authentication information, including ak, secret, token
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                .accessKeyId("LTAI5tRfCNfcpvFCRaM1ydiy")
                .accessKeySecret("******************************")
                //.securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS token
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region("cn-hangzhou") // Region ID
                //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
                .credentialsProvider(provider)
                //.serviceConfiguration(Configuration.create()) // Service-level configuration
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
                                .setEndpointOverride("dysmsapi.aliyuncs.com")
                        //.setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();


        //问到课堂
        //SMS_464835324
        String tempId = "SMS_463125053";
        if (appNameType == 2) {
            tempId = "SMS_464790776";
        }
        // Parameter settings for API request
        SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                .signName("问到")
                .templateCode(tempId)
                .phoneNumbers(phoneNumber)
                .templateParam("{\"sjhm\":\"" + userName + "\",\"dlmm\":\"" + password + "\"}")
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
        // Synchronously get the return value of the API request
        SendSmsResponse resp = response.get();
        //System.out.println(new Gson().toJson(resp));
        // Asynchronous processing of return values
        /*response.thenAccept(resp -> {
            System.out.println(new Gson().toJson(resp));
        }).exceptionally(throwable -> { // Handling exceptions
            System.out.println(throwable.getMessage());
            return null;
        });*/

        // Finally, close the client
        client.close();
        //return uuid;
    }

    private void sendDoudianLearnSms(String phoneNumber, String productName, String code) throws Exception {
        //String uuid = UUID.randomUUID().toString();

        // HttpClient Configuration
        /*HttpClient httpClient = new ApacheAsyncHttpClientBuilder()
                .connectionTimeout(Duration.ofSeconds(10)) // Set the connection timeout time, the default is 10 seconds
                .responseTimeout(Duration.ofSeconds(10)) // Set the response timeout time, the default is 20 seconds
                .maxConnections(128) // Set the connection pool size
                .maxIdleTimeOut(Duration.ofSeconds(50)) // Set the connection pool timeout, the default is 30 seconds
                // Configure the proxy
                .proxy(new ProxyOptions(ProxyOptions.Type.HTTP, new InetSocketAddress("<your-proxy-hostname>", 9001))
                        .setCredentials("<your-proxy-username>", "<your-proxy-password>"))
                // If it is an https connection, you need to configure the certificate, or ignore the certificate(.ignoreSSL(true))
                .x509TrustManagers(new X509TrustManager[]{})
                .keyManagers(new KeyManager[]{})
                .ignoreSSL(false)
                .build();*/

        // Configure Credentials authentication information, including ak, secret, token
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                .accessKeyId("LTAI5tRfCNfcpvFCRaM1ydiy")
                .accessKeySecret("******************************")
                //.securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS token
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region("cn-hangzhou") // Region ID
                //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
                .credentialsProvider(provider)
                //.serviceConfiguration(Configuration.create()) // Service-level configuration
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
                                .setEndpointOverride("dysmsapi.aliyuncs.com")
                        //.setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();


        //问到课堂
        //SMS_464835324
        String tempId = "SMS_472750052";
        // Parameter settings for API request
        SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                .signName("问到")
                .templateCode(tempId)
                .phoneNumbers(phoneNumber)
                .templateParam("{\"productName\":\"" + productName + "\",\"code\":\"" + code + "\"}")
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
        // Synchronously get the return value of the API request
        SendSmsResponse resp = response.get();
        //System.out.println(new Gson().toJson(resp));
        // Asynchronous processing of return values
        /*response.thenAccept(resp -> {
            System.out.println(new Gson().toJson(resp));
        }).exceptionally(throwable -> { // Handling exceptions
            System.out.println(throwable.getMessage());
            return null;
        });*/

        // Finally, close the client
        client.close();
        //return uuid;
    }

}
