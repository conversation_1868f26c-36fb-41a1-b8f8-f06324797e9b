package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.EmployeeSalesStatistics;

/**
 * 员工销售数据统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IEmployeeSalesStatisticsService 
{
    /**
     * 查询员工销售数据统计
     * 
     * @param id 员工销售数据统计主键
     * @return 员工销售数据统计
     */
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsById(Long id);

    /**
     * 查询员工销售数据统计列表
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 员工销售数据统计集合
     */
    public List<EmployeeSalesStatistics> selectEmployeeSalesStatisticsList(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 新增员工销售数据统计
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 结果
     */
    public int insertEmployeeSalesStatistics(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 修改员工销售数据统计
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 结果
     */
    public int updateEmployeeSalesStatistics(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 批量删除员工销售数据统计
     * 
     * @param ids 需要删除的员工销售数据统计主键集合
     * @return 结果
     */
    public int deleteEmployeeSalesStatisticsByIds(Long[] ids);

    /**
     * 删除员工销售数据统计信息
     * 
     * @param id 员工销售数据统计主键
     * @return 结果
     */
    public int deleteEmployeeSalesStatisticsById(Long id);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByUserId(Long userId,String statType);

    int queryHasTeam(Long userId);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByParentLeaderId(Long userId, String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByParentLeaderIdAndDeptId(Long userId, String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByLeaderId(Long userId, String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByLeaderIdAndDeptId(Long userId, String statType);
}