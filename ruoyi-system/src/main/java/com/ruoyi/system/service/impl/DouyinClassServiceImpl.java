package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.DouyinClassMapper;
import com.ruoyi.system.domain.DouyinClass;
import com.ruoyi.system.service.IDouyinClassService;

/**
 * 抖音分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-21
 */
@Service
public class DouyinClassServiceImpl implements IDouyinClassService 
{
    @Autowired
    private DouyinClassMapper douyinClassMapper;

    /**
     * 查询抖音分类
     * 
     * @param id 抖音分类主键
     * @return 抖音分类
     */
    @Override
    public DouyinClass selectDouyinClassById(Long id)
    {
        return douyinClassMapper.selectDouyinClassById(id);
    }

    /**
     * 查询抖音分类列表
     * 
     * @param douyinClass 抖音分类
     * @return 抖音分类
     */
    @Override
    public List<DouyinClass> selectDouyinClassList(DouyinClass douyinClass)
    {
        return douyinClassMapper.selectDouyinClassList(douyinClass);
    }

    /**
     * 新增抖音分类
     * 
     * @param douyinClass 抖音分类
     * @return 结果
     */
    @Override
    public int insertDouyinClass(DouyinClass douyinClass)
    {
        return douyinClassMapper.insertDouyinClass(douyinClass);
    }

    /**
     * 修改抖音分类
     * 
     * @param douyinClass 抖音分类
     * @return 结果
     */
    @Override
    public int updateDouyinClass(DouyinClass douyinClass)
    {
        return douyinClassMapper.updateDouyinClass(douyinClass);
    }

    /**
     * 批量删除抖音分类
     * 
     * @param ids 需要删除的抖音分类主键
     * @return 结果
     */
    @Override
    public int deleteDouyinClassByIds(Long[] ids)
    {
        return douyinClassMapper.deleteDouyinClassByIds(ids);
    }

    /**
     * 删除抖音分类信息
     * 
     * @param id 抖音分类主键
     * @return 结果
     */
    @Override
    public int deleteDouyinClassById(Long id)
    {
        return douyinClassMapper.deleteDouyinClassById(id);
    }
}
