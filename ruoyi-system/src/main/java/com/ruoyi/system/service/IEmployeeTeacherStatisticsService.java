package com.ruoyi.system.service;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.system.domain.EmployeeTeacherStatistics;

/**
 * 员工客户老师数据统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IEmployeeTeacherStatisticsService 
{
    /**
     * 查询员工客户老师数据统计
     * 
     * @param id 员工客户老师数据统计主键
     * @return 员工客户老师数据统计
     */
    public EmployeeTeacherStatistics selectEmployeeTeacherStatisticsById(Long id);

    /**
     * 查询员工客户老师数据统计列表
     * 
     * @param employeeTeacherStatistics 员工客户老师数据统计
     * @return 员工客户老师数据统计集合
     */
    public List<EmployeeTeacherStatistics> selectEmployeeTeacherStatisticsList(EmployeeTeacherStatistics employeeTeacherStatistics);

    /**
     * 新增员工客户老师数据统计
     * 
     * @param employeeTeacherStatistics 员工客户老师数据统计
     * @return 结果
     */
    public int insertEmployeeTeacherStatistics(EmployeeTeacherStatistics employeeTeacherStatistics);

    /**
     * 修改员工客户老师数据统计
     * 
     * @param employeeTeacherStatistics 员工客户老师数据统计
     * @return 结果
     */
    public int updateEmployeeTeacherStatistics(EmployeeTeacherStatistics employeeTeacherStatistics);

    /**
     * 批量删除员工客户老师数据统计
     * 
     * @param ids 需要删除的员工客户老师数据统计主键集合
     * @return 结果
     */
    public int deleteEmployeeTeacherStatisticsByIds(Long[] ids);

    /**
     * 删除员工客户老师数据统计信息
     * 
     * @param id 员工客户老师数据统计主键
     * @return 结果
     */
    public int deleteEmployeeTeacherStatisticsById(Long id);

    /**
     * 根据老师ID查询员工客户老师数据统计
     * 
     * @param teacherId 老师ID
     * @return 员工客户老师数据统计
     */
    public EmployeeTeacherStatistics selectEmployeeTeacherStatisticsByTeacherId(Long teacherId);

    /**
     * 根据员工ID查询员工客户老师数据统计列表
     * 
     * @param employeeId 员工ID
     * @return 员工客户老师数据统计集合
     */
    public List<EmployeeTeacherStatistics> selectEmployeeTeacherStatisticsByEmployeeId(Long employeeId);

    /**
     * 根据直接主管ID查询员工客户老师数据统计列表
     * 
     * @param leaderId 直接主管ID
     * @return 员工客户老师数据统计集合
     */
    public List<EmployeeTeacherStatistics> selectEmployeeTeacherStatisticsByLeaderId(Long leaderId);

    /**
     * 根据上级主管ID查询员工客户老师数据统计列表
     * 
     * @param parentLeaderId 上级主管ID
     * @return 员工客户老师数据统计集合
     */
    public List<EmployeeTeacherStatistics> selectEmployeeTeacherStatisticsByParentLeaderId(Long parentLeaderId);

    /**
     * 根据员工ID列表和时间查询区间查询，按交易金额倒序排列，取前20个
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 员工客户老师数据统计集合
     */
    public List<EmployeeTeacherStatistics> selectTop20ByEmployeeIdsAndTimeQueryStr(List<Long> employeeIds, String timeQueryStr);

    /**
     * 统计店铺总数
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 店铺总数
     */
    public Integer countTotalShops(List<Long> employeeIds, String timeQueryStr);

    /**
     * 统计交易金额大于0的店铺数
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 有交易的店铺数
     */
    public Integer countShopsWithDealAmount(List<Long> employeeIds, String timeQueryStr);

    /**
     * 计算总销售额
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 总销售额
     */
    public BigDecimal sumTotalDealAmount(List<Long> employeeIds, String timeQueryStr);

    /**
     * 计算平均销售额
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 平均销售额
     */
    public BigDecimal getAverageDealAmount(List<Long> employeeIds, String timeQueryStr);

    /**
     * 根据老师ID列表和时间查询区间查询数据
     * 
     * @param teacherIds 老师ID列表
     * @param timeQueryStr 时间查询区间
     * @return 员工客户老师数据统计集合
     */
    public List<EmployeeTeacherStatistics> selectByTeacherIdsAndTimeQueryStr(List<Long> teacherIds, String timeQueryStr);
} 