package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.WendaoCertInfo;
import com.ruoyi.system.mapper.WendaoCertInfoMapper;
import com.ruoyi.system.service.IWendaoCertInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资质中心信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-02
 */
@Service
public class WendaoCertInfoServiceImpl implements IWendaoCertInfoService
{
    @Autowired
    private WendaoCertInfoMapper wendaoCertInfoMapper;

    /**
     * 查询资质中心信息
     * 
     * @param id 资质中心信息主键
     * @return 资质中心信息
     */
    @Override
    public WendaoCertInfo selectWendaoCertInfoById(Long id)
    {
        return wendaoCertInfoMapper.selectWendaoCertInfoById(id);
    }

    /**
     * 查询资质中心信息列表
     * 
     * @param wendaoCertInfo 资质中心信息
     * @return 资质中心信息
     */
    @Override
    public List<WendaoCertInfo> selectWendaoCertInfoList(WendaoCertInfo wendaoCertInfo)
    {
        return wendaoCertInfoMapper.selectWendaoCertInfoList(wendaoCertInfo);
    }

    /**
     * 新增资质中心信息
     * 
     * @param wendaoCertInfo 资质中心信息
     * @return 结果
     */
    @Override
    public int insertWendaoCertInfo(WendaoCertInfo wendaoCertInfo)
    {
        wendaoCertInfo.setCreateTime(DateUtils.getNowDate());
        return wendaoCertInfoMapper.insertWendaoCertInfo(wendaoCertInfo);
    }

    /**
     * 修改资质中心信息
     * 
     * @param wendaoCertInfo 资质中心信息
     * @return 结果
     */
    @Override
    public int updateWendaoCertInfo(WendaoCertInfo wendaoCertInfo)
    {
        wendaoCertInfo.setUpdateTime(DateUtils.getNowDate());
        return wendaoCertInfoMapper.updateWendaoCertInfo(wendaoCertInfo);
    }

    @Override
    public int updateWendaoCertInfo1(WendaoCertInfo wendaoCertInfo)
    {
        wendaoCertInfo.setUpdateTime(DateUtils.getNowDate());
        return wendaoCertInfoMapper.updateWendaoCertInfo1(wendaoCertInfo);
    }

    /**
     * 批量删除资质中心信息
     * 
     * @param ids 需要删除的资质中心信息主键
     * @return 结果
     */
    @Override
    public int deleteWendaoCertInfoByIds(Long[] ids)
    {
        return wendaoCertInfoMapper.deleteWendaoCertInfoByIds(ids);
    }

    /**
     * 删除资质中心信息信息
     * 
     * @param id 资质中心信息主键
     * @return 结果
     */
    @Override
    public int deleteWendaoCertInfoById(Long id)
    {
        return wendaoCertInfoMapper.deleteWendaoCertInfoById(id);
    }

    @Override
    public List<WendaoCertInfo> selectWendaoCertInfoBySearchValue(WendaoCertInfo wendaoCertInfo) {
        return wendaoCertInfoMapper.selectWendaoCertInfoBySearchValue(wendaoCertInfo);
    }
}
