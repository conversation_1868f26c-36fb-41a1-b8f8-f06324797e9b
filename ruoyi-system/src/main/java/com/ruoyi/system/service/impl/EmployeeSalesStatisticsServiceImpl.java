package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.EmployeeSalesStatisticsMapper;
import com.ruoyi.system.domain.EmployeeSalesStatistics;
import com.ruoyi.system.service.IEmployeeSalesStatisticsService;

/**
 * 员工销售数据统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class EmployeeSalesStatisticsServiceImpl implements IEmployeeSalesStatisticsService 
{
    @Autowired
    private EmployeeSalesStatisticsMapper employeeSalesStatisticsMapper;

    /**
     * 查询员工销售数据统计
     * 
     * @param id 员工销售数据统计主键
     * @return 员工销售数据统计
     */
    @Override
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsById(Long id)
    {
        return employeeSalesStatisticsMapper.selectEmployeeSalesStatisticsById(id);
    }

    /**
     * 查询员工销售数据统计列表
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 员工销售数据统计
     */
    @Override
    public List<EmployeeSalesStatistics> selectEmployeeSalesStatisticsList(EmployeeSalesStatistics employeeSalesStatistics)
    {
        return employeeSalesStatisticsMapper.selectEmployeeSalesStatisticsList(employeeSalesStatistics);
    }

    /**
     * 新增员工销售数据统计
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 结果
     */
    @Override
    public int insertEmployeeSalesStatistics(EmployeeSalesStatistics employeeSalesStatistics)
    {
        return employeeSalesStatisticsMapper.insertEmployeeSalesStatistics(employeeSalesStatistics);
    }

    /**
     * 修改员工销售数据统计
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 结果
     */
    @Override
    public int updateEmployeeSalesStatistics(EmployeeSalesStatistics employeeSalesStatistics)
    {
        return employeeSalesStatisticsMapper.updateEmployeeSalesStatistics(employeeSalesStatistics);
    }

    /**
     * 批量删除员工销售数据统计
     * 
     * @param ids 需要删除的员工销售数据统计主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeSalesStatisticsByIds(Long[] ids)
    {
        return employeeSalesStatisticsMapper.deleteEmployeeSalesStatisticsByIds(ids);
    }

    /**
     * 删除员工销售数据统计信息
     * 
     * @param id 员工销售数据统计主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeSalesStatisticsById(Long id)
    {
        return employeeSalesStatisticsMapper.deleteEmployeeSalesStatisticsById(id);
    }

    @Override
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsByUserId(Long userId,String statType) {
        return employeeSalesStatisticsMapper.selectEmployeeSalesStatisticsByUserId(userId,statType);
    }

    @Override
    public int queryHasTeam(Long userId) {
        return employeeSalesStatisticsMapper.queryHasTeam(userId);
    }

    @Override
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsByParentLeaderId(Long userId, String statType) {
        return employeeSalesStatisticsMapper.selectEmployeeSalesStatisticsByParentLeaderId(userId,statType);
    }

    @Override
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsByParentLeaderIdAndDeptId(Long userId, String statType) {
        return employeeSalesStatisticsMapper.selectEmployeeSalesStatisticsByParentLeaderIdAndDeptId(userId,statType);
    }

    @Override
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsByLeaderId(Long userId, String statType) {
        return employeeSalesStatisticsMapper.selectEmployeeSalesStatisticsByLeaderId(userId,statType);
    }

    @Override
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsByLeaderIdAndDeptId(Long userId, String statType) {
        return employeeSalesStatisticsMapper.selectEmployeeSalesStatisticsByLeaderIdAndDeptId(userId,statType);
    }
} 