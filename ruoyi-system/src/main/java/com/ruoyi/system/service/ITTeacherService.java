package com.ruoyi.system.service;


import com.ruoyi.system.domain.TTeacher;
import com.ruoyi.system.dto.PicDTO;
import com.ruoyi.system.dto.UEditorImageDTO;

import java.util.List;

/**
 * 老师Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-12
 */
public interface ITTeacherService 
{
    /**
     * 查询老师
     * 
     * @param teacherId 老师主键
     * @return 老师
     */
    public TTeacher selectTTeacherByTeacherId(Long teacherId);

    /**
     * 查询老师列表
     * 
     * @param tTeacher 老师
     * @return 老师集合
     */
    public List<TTeacher> selectTTeacherList(TTeacher tTeacher);

    /**
     * 新增老师
     * 
     * @param tTeacher 老师
     * @return 结果
     */
    public int insertTTeacher(TTeacher tTeacher);

    /**
     * 修改老师
     * 
     * @param tTeacher 老师
     * @return 结果
     */
    public int updateTTeacher(TTeacher tTeacher);

    /**
     * 批量删除老师
     * 
     * @param teacherIds 需要删除的老师主键集合
     * @return 结果
     */
    public int deleteTTeacherByTeacherIds(Long[] teacherIds);

    /**
     * 删除老师信息
     * 
     * @param teacherId 老师主键
     * @return 结果
     */
    public int deleteTTeacherByTeacherId(Long teacherId);

    List<UEditorImageDTO> selectTeacherPics(Long teacherId);

    int insertTPic(PicDTO tPic);
}
