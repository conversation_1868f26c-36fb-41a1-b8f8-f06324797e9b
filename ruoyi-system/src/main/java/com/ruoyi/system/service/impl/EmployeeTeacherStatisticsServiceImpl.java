package com.ruoyi.system.service.impl;

import java.math.BigDecimal;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.EmployeeTeacherStatisticsMapper;
import com.ruoyi.system.domain.EmployeeTeacherStatistics;
import com.ruoyi.system.service.IEmployeeTeacherStatisticsService;

/**
 * 员工客户老师数据统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class EmployeeTeacherStatisticsServiceImpl implements IEmployeeTeacherStatisticsService 
{
    @Autowired
    private EmployeeTeacherStatisticsMapper employeeTeacherStatisticsMapper;

    /**
     * 查询员工客户老师数据统计
     * 
     * @param id 员工客户老师数据统计主键
     * @return 员工客户老师数据统计
     */
    @Override
    public EmployeeTeacherStatistics selectEmployeeTeacherStatisticsById(Long id)
    {
        return employeeTeacherStatisticsMapper.selectEmployeeTeacherStatisticsById(id);
    }

    /**
     * 查询员工客户老师数据统计列表
     * 
     * @param employeeTeacherStatistics 员工客户老师数据统计
     * @return 员工客户老师数据统计
     */
    @Override
    public List<EmployeeTeacherStatistics> selectEmployeeTeacherStatisticsList(EmployeeTeacherStatistics employeeTeacherStatistics)
    {
        return employeeTeacherStatisticsMapper.selectEmployeeTeacherStatisticsList(employeeTeacherStatistics);
    }

    /**
     * 新增员工客户老师数据统计
     * 
     * @param employeeTeacherStatistics 员工客户老师数据统计
     * @return 结果
     */
    @Override
    public int insertEmployeeTeacherStatistics(EmployeeTeacherStatistics employeeTeacherStatistics)
    {
        return employeeTeacherStatisticsMapper.insertEmployeeTeacherStatistics(employeeTeacherStatistics);
    }

    /**
     * 修改员工客户老师数据统计
     * 
     * @param employeeTeacherStatistics 员工客户老师数据统计
     * @return 结果
     */
    @Override
    public int updateEmployeeTeacherStatistics(EmployeeTeacherStatistics employeeTeacherStatistics)
    {
        return employeeTeacherStatisticsMapper.updateEmployeeTeacherStatistics(employeeTeacherStatistics);
    }

    /**
     * 批量删除员工客户老师数据统计
     * 
     * @param ids 需要删除的员工客户老师数据统计主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeTeacherStatisticsByIds(Long[] ids)
    {
        return employeeTeacherStatisticsMapper.deleteEmployeeTeacherStatisticsByIds(ids);
    }

    /**
     * 删除员工客户老师数据统计信息
     * 
     * @param id 员工客户老师数据统计主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeTeacherStatisticsById(Long id)
    {
        return employeeTeacherStatisticsMapper.deleteEmployeeTeacherStatisticsById(id);
    }

    /**
     * 根据老师ID查询员工客户老师数据统计
     * 
     * @param teacherId 老师ID
     * @return 员工客户老师数据统计
     */
    @Override
    public EmployeeTeacherStatistics selectEmployeeTeacherStatisticsByTeacherId(Long teacherId)
    {
        return employeeTeacherStatisticsMapper.selectEmployeeTeacherStatisticsByTeacherId(teacherId);
    }

    /**
     * 根据员工ID查询员工客户老师数据统计列表
     * 
     * @param employeeId 员工ID
     * @return 员工客户老师数据统计集合
     */
    @Override
    public List<EmployeeTeacherStatistics> selectEmployeeTeacherStatisticsByEmployeeId(Long employeeId)
    {
        return employeeTeacherStatisticsMapper.selectEmployeeTeacherStatisticsByEmployeeId(employeeId);
    }

    /**
     * 根据直接主管ID查询员工客户老师数据统计列表
     * 
     * @param leaderId 直接主管ID
     * @return 员工客户老师数据统计集合
     */
    @Override
    public List<EmployeeTeacherStatistics> selectEmployeeTeacherStatisticsByLeaderId(Long leaderId)
    {
        return employeeTeacherStatisticsMapper.selectEmployeeTeacherStatisticsByLeaderId(leaderId);
    }

    /**
     * 根据上级主管ID查询员工客户老师数据统计列表
     * 
     * @param parentLeaderId 上级主管ID
     * @return 员工客户老师数据统计集合
     */
    @Override
    public List<EmployeeTeacherStatistics> selectEmployeeTeacherStatisticsByParentLeaderId(Long parentLeaderId)
    {
        return employeeTeacherStatisticsMapper.selectEmployeeTeacherStatisticsByParentLeaderId(parentLeaderId);
    }

    /**
     * 根据员工ID列表和时间查询区间查询，按交易金额倒序排列，取前20个
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 员工客户老师数据统计集合
     */
    @Override
    public List<EmployeeTeacherStatistics> selectTop20ByEmployeeIdsAndTimeQueryStr(List<Long> employeeIds, String timeQueryStr)
    {
        return employeeTeacherStatisticsMapper.selectTop20ByEmployeeIdsAndTimeQueryStr(employeeIds, timeQueryStr);
    }

    /**
     * 统计店铺总数
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 店铺总数
     */
    @Override
    public Integer countTotalShops(List<Long> employeeIds, String timeQueryStr)
    {
        return employeeTeacherStatisticsMapper.countTotalShops(employeeIds, timeQueryStr);
    }

    /**
     * 统计交易金额大于0的店铺数
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 有交易的店铺数
     */
    @Override
    public Integer countShopsWithDealAmount(List<Long> employeeIds, String timeQueryStr)
    {
        return employeeTeacherStatisticsMapper.countShopsWithDealAmount(employeeIds, timeQueryStr);
    }

    /**
     * 计算总销售额
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 总销售额
     */
    @Override
    public BigDecimal sumTotalDealAmount(List<Long> employeeIds, String timeQueryStr)
    {
        return employeeTeacherStatisticsMapper.sumTotalDealAmount(employeeIds, timeQueryStr);
    }

    /**
     * 计算平均销售额
     * 
     * @param employeeIds 员工ID列表
     * @param timeQueryStr 时间查询区间
     * @return 平均销售额
     */
    @Override
    public BigDecimal getAverageDealAmount(List<Long> employeeIds, String timeQueryStr)
    {
        return employeeTeacherStatisticsMapper.getAverageDealAmount(employeeIds, timeQueryStr);
    }

    /**
     * 根据老师ID列表和时间查询区间查询数据
     * 
     * @param teacherIds 老师ID列表
     * @param timeQueryStr 时间查询区间
     * @return 员工客户老师数据统计集合
     */
    @Override
    public List<EmployeeTeacherStatistics> selectByTeacherIdsAndTimeQueryStr(List<Long> teacherIds, String timeQueryStr)
    {
        return employeeTeacherStatisticsMapper.selectByTeacherIdsAndTimeQueryStr(teacherIds, timeQueryStr);
    }
} 