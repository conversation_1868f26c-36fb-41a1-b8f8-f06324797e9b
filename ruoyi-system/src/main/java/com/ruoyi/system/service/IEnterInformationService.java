package com.ruoyi.system.service;

import java.util.Date;
import java.util.List;

import com.github.pagehelper.PageInfo;
import com.ruoyi.system.domain.EnterInformation;
import com.ruoyi.system.domain.TeacherEnter;
import com.ruoyi.system.domain.TeacherResourcePackage;
import com.ruoyi.system.domain.dto.DyMiniClassDTO;
import com.ruoyi.system.domain.dto.EnterInformationStatDTO;
import com.ruoyi.system.domain.vo.EnterInformationVO;
import com.ruoyi.system.domain.vo.TeacherEnterStatusVO;
import com.ruoyi.system.domain.vo.TeacherEnterVO;
import com.ruoyi.system.dto.ChangeShopMobileDTO;
import com.ruoyi.system.dto.PasswordInfoDTO;
import com.ruoyi.system.dto.TeacherDTO;

import javax.validation.constraints.NotNull;

/**
 *  入驻信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface IEnterInformationService 
{
    /**
     * 查询 入驻信息
     * 
     * @param id  入驻信息主键
     * @return  入驻信息
     */
    public EnterInformation selectEnterInformationById(Long id);

    /**
     * 查询 入驻信息列表
     * 
     * @param enterInformation  入驻信息
     * @return  入驻信息集合
     */
    public List<EnterInformation> selectEnterInformationList(EnterInformation enterInformation);

    /**
     * 新增 入驻信息
     * 
     * @param enterInformation  入驻信息
     * @return 结果
     */
    public int insertEnterInformation(EnterInformation enterInformation);

    /**
     * 修改 入驻信息
     * 
     * @param enterInformation  入驻信息
     * @return 结果
     */
    public int updateEnterInformation(EnterInformation enterInformation);

    /**
     * 批量删除 入驻信息
     * 
     * @param ids 需要删除的 入驻信息主键集合
     * @return 结果
     */
    public int deleteEnterInformationByIds(Long[] ids);

    /**
     * 删除 入驻信息信息
     * 
     * @param id  入驻信息主键
     * @return 结果
     */
    public int deleteEnterInformationById(Long id);

    EnterInformation selectEnterInformationByShopId(Long teacherId);

    /**
     * 开户审核列表
     *
     * @param enterInformationVO
     * @return
     */
    PageInfo<EnterInformation> selectAll(EnterInformationVO enterInformationVO);

    /**
     * 根据条件查询
     *
     * @param enterInformation1
     * @return
     */
    List<EnterInformation> getEnterInformationList(EnterInformation enterInformation1);

    /**
     * 老师入驻申请列表
     *
     * @param teacherEnterVO
     * @return
     */
    PageInfo<TeacherEnter> getTeacherEnterList(TeacherEnterVO teacherEnterVO);

    /**
     * 分类列表
     *
     * @return
     */
    List<DyMiniClassDTO> selectDyMiniClassList();

    /**
     * 修改入驻申请状态
     */
    boolean updateTeacherEnterStatus(TeacherEnterStatusVO enterStatusVO);

    void updateTeacherPlatfrom(String platform, Integer version, Long teacherId, Integer giftCourse, Integer wapLiveOpen, String ddShopIds, Date endDate,Integer openPromoter,Integer promoterNum);

    PasswordInfoDTO selectTeacherInfoByAppNameTypeAndTelNum(EnterInformation enterInformation);

    int selectCountTeacherInfo(String telNum, Integer appNameType);

    int insertTTeacher(TeacherDTO tTeacher);

    int updateTeacherMobile(ChangeShopMobileDTO changeShopMobileDTO);

    int updateSysUserByNewMobile(ChangeShopMobileDTO changeShopMobileDTO);

    int updateEnterInformationTelNum(ChangeShopMobileDTO changeShopMobileDTO);

    int insertSmsRecord(String phoneNumber, String code, String uuid);

    int countSmsRecord(ChangeShopMobileDTO changeShopMobileDTO);

    Long selectSopIdForCheckSubAccount(String telNum, Integer appNameType);

    void updateTeacherFroKnowledgeStore(TeacherDTO tTeacher);

    TeacherDTO selectTeacherKnownledgeStoreDomain(Long shopId);

    void insertTeacherResourcePkg(TeacherResourcePackage teacherResourcePackage);

    /**
     * 根据时间范围、客户专员和审核类型查询入驻信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param accountSpecialist 客户专员
     * @param auditType 审核类型
     * @return 入驻信息列表
     */
    List<EnterInformationStatDTO> queryByTimeAndSpecialist(Date startTime, Date endTime, String accountSpecialist, Integer auditType);
}
