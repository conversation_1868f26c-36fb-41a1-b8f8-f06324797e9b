package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.PublicWendaoCertInfoAuthorizedMapper;
import com.ruoyi.system.domain.PublicWendaoCertInfoAuthorized;
import com.ruoyi.system.service.IPublicWendaoCertInfoAuthorizedService;

/**
 * 公共资质授权Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@Service
public class PublicWendaoCertInfoAuthorizedServiceImpl implements IPublicWendaoCertInfoAuthorizedService 
{
    @Autowired
    private PublicWendaoCertInfoAuthorizedMapper publicWendaoCertInfoAuthorizedMapper;

    /**
     * 查询公共资质授权
     * 
     * @param id 公共资质授权主键
     * @return 公共资质授权
     */
    @Override
    public PublicWendaoCertInfoAuthorized selectPublicWendaoCertInfoAuthorizedById(Long id)
    {
        return publicWendaoCertInfoAuthorizedMapper.selectPublicWendaoCertInfoAuthorizedById(id);
    }

    /**
     * 查询公共资质授权列表
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 公共资质授权
     */
    @Override
    public List<PublicWendaoCertInfoAuthorized> selectPublicWendaoCertInfoAuthorizedList(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized)
    {
        return publicWendaoCertInfoAuthorizedMapper.selectPublicWendaoCertInfoAuthorizedList(publicWendaoCertInfoAuthorized);
    }

    /**
     * 新增公共资质授权
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 结果
     */
    @Override
    public int insertPublicWendaoCertInfoAuthorized(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized)
    {
        publicWendaoCertInfoAuthorized.setCreateTime(DateUtils.getNowDate());
        return publicWendaoCertInfoAuthorizedMapper.insertPublicWendaoCertInfoAuthorized(publicWendaoCertInfoAuthorized);
    }

    /**
     * 修改公共资质授权
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 结果
     */
    @Override
    public int updatePublicWendaoCertInfoAuthorized(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized)
    {
        publicWendaoCertInfoAuthorized.setUpdateTime(DateUtils.getNowDate());
        return publicWendaoCertInfoAuthorizedMapper.updatePublicWendaoCertInfoAuthorized(publicWendaoCertInfoAuthorized);
    }

    /**
     * 批量删除公共资质授权
     * 
     * @param ids 需要删除的公共资质授权主键
     * @return 结果
     */
    @Override
    public int deletePublicWendaoCertInfoAuthorizedByIds(Long[] ids)
    {
        return publicWendaoCertInfoAuthorizedMapper.deletePublicWendaoCertInfoAuthorizedByIds(ids);
    }

    /**
     * 删除公共资质授权信息
     * 
     * @param id 公共资质授权主键
     * @return 结果
     */
    @Override
    public int deletePublicWendaoCertInfoAuthorizedById(Long id)
    {
        return publicWendaoCertInfoAuthorizedMapper.deletePublicWendaoCertInfoAuthorizedById(id);
    }
}
