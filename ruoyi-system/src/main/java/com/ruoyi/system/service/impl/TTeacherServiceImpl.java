package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.TTeacher;
import com.ruoyi.system.dto.PicDTO;
import com.ruoyi.system.dto.UEditorImageDTO;
import com.ruoyi.system.mapper.TTeacherMapper;
import com.ruoyi.system.service.ITTeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 老师Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-12
 */
@Service
public class TTeacherServiceImpl implements ITTeacherService
{
    @Autowired
    private TTeacherMapper tTeacherMapper;

    /**
     * 查询老师
     * 
     * @param teacherId 老师主键
     * @return 老师
     */
    @Override
    public TTeacher selectTTeacherByTeacherId(Long teacherId)
    {
        return tTeacherMapper.selectTTeacherByTeacherId(teacherId);
    }

    /**
     * 查询老师列表
     * 
     * @param tTeacher 老师
     * @return 老师
     */
    @Override
    public List<TTeacher> selectTTeacherList(TTeacher tTeacher)
    {
        return tTeacherMapper.selectTTeacherList(tTeacher);
    }

    /**
     * 新增老师
     * 
     * @param tTeacher 老师
     * @return 结果
     */
    @Override
    public int insertTTeacher(TTeacher tTeacher)
    {
        tTeacher.setCreateTime(DateUtils.getNowDate());
        return tTeacherMapper.insertTTeacher(tTeacher);
    }

    /**
     * 修改老师
     * 
     * @param tTeacher 老师
     * @return 结果
     */
    @Override
    public int updateTTeacher(TTeacher tTeacher)
    {
        tTeacher.setUpdateTime(DateUtils.getNowDate());
        return tTeacherMapper.updateTTeacher(tTeacher);
    }

    /**
     * 批量删除老师
     * 
     * @param teacherIds 需要删除的老师主键
     * @return 结果
     */
    @Override
    public int deleteTTeacherByTeacherIds(Long[] teacherIds)
    {
        return tTeacherMapper.deleteTTeacherByTeacherIds(teacherIds);
    }

    /**
     * 删除老师信息
     * 
     * @param teacherId 老师主键
     * @return 结果
     */
    @Override
    public int deleteTTeacherByTeacherId(Long teacherId)
    {
        return tTeacherMapper.deleteTTeacherByTeacherId(teacherId);
    }

    @Override
    public List<UEditorImageDTO> selectTeacherPics(Long teacherId) {
        return tTeacherMapper.selectTeacherPics(teacherId);
    }

    @Override
    public int insertTPic(PicDTO tPic) {
        return tTeacherMapper.insertTPic(tPic);
    }
}
