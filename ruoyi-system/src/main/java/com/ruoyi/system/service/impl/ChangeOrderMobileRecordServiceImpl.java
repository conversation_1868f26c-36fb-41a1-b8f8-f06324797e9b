package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ChangeOrderMobileRecordMapper;
import com.ruoyi.system.domain.ChangeOrderMobileRecord;
import com.ruoyi.system.service.IChangeOrderMobileRecordService;

/**
 * 修改订单手机号码记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
@Service
public class ChangeOrderMobileRecordServiceImpl implements IChangeOrderMobileRecordService 
{
    @Autowired
    private ChangeOrderMobileRecordMapper changeOrderMobileRecordMapper;

    /**
     * 查询修改订单手机号码记录
     * 
     * @param id 修改订单手机号码记录主键
     * @return 修改订单手机号码记录
     */
    @Override
    public ChangeOrderMobileRecord selectChangeOrderMobileRecordById(Long id)
    {
        return changeOrderMobileRecordMapper.selectChangeOrderMobileRecordById(id);
    }

    /**
     * 查询修改订单手机号码记录列表
     * 
     * @param changeOrderMobileRecord 修改订单手机号码记录
     * @return 修改订单手机号码记录
     */
    @Override
    public List<ChangeOrderMobileRecord> selectChangeOrderMobileRecordList(ChangeOrderMobileRecord changeOrderMobileRecord)
    {
        return changeOrderMobileRecordMapper.selectChangeOrderMobileRecordList(changeOrderMobileRecord);
    }

    /**
     * 新增修改订单手机号码记录
     * 
     * @param changeOrderMobileRecord 修改订单手机号码记录
     * @return 结果
     */
    @Override
    public int insertChangeOrderMobileRecord(ChangeOrderMobileRecord changeOrderMobileRecord)
    {
        changeOrderMobileRecord.setCreateTime(DateUtils.getNowDate());
        return changeOrderMobileRecordMapper.insertChangeOrderMobileRecord(changeOrderMobileRecord);
    }

    /**
     * 修改修改订单手机号码记录
     * 
     * @param changeOrderMobileRecord 修改订单手机号码记录
     * @return 结果
     */
    @Override
    public int updateChangeOrderMobileRecord(ChangeOrderMobileRecord changeOrderMobileRecord)
    {
        changeOrderMobileRecord.setUpdateTime(DateUtils.getNowDate());
        return changeOrderMobileRecordMapper.updateChangeOrderMobileRecord(changeOrderMobileRecord);
    }

    /**
     * 批量删除修改订单手机号码记录
     * 
     * @param ids 需要删除的修改订单手机号码记录主键
     * @return 结果
     */
    @Override
    public int deleteChangeOrderMobileRecordByIds(Long[] ids)
    {
        return changeOrderMobileRecordMapper.deleteChangeOrderMobileRecordByIds(ids);
    }

    /**
     * 删除修改订单手机号码记录信息
     * 
     * @param id 修改订单手机号码记录主键
     * @return 结果
     */
    @Override
    public int deleteChangeOrderMobileRecordById(Long id)
    {
        return changeOrderMobileRecordMapper.deleteChangeOrderMobileRecordById(id);
    }
}
