package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SaleVersionManage;

/**
 * 问到系统售卖版本管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ISaleVersionManageService 
{
    /**
     * 查询问到系统售卖版本管理
     * 
     * @param id 问到系统售卖版本管理主键
     * @return 问到系统售卖版本管理
     */
    public SaleVersionManage selectSaleVersionManageById(Long id);

    /**
     * 查询问到系统售卖版本管理列表
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 问到系统售卖版本管理集合
     */
    public List<SaleVersionManage> selectSaleVersionManageList(SaleVersionManage saleVersionManage);

    /**
     * 新增问到系统售卖版本管理
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 结果
     */
    public int insertSaleVersionManage(SaleVersionManage saleVersionManage);

    /**
     * 修改问到系统售卖版本管理
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 结果
     */
    public int updateSaleVersionManage(SaleVersionManage saleVersionManage);

    /**
     * 批量删除问到系统售卖版本管理
     * 
     * @param ids 需要删除的问到系统售卖版本管理主键集合
     * @return 结果
     */
    public int deleteSaleVersionManageByIds(Long[] ids);

    /**
     * 删除问到系统售卖版本管理信息
     * 
     * @param id 问到系统售卖版本管理主键
     * @return 结果
     */
    public int deleteSaleVersionManageById(Long id);

    Integer selectMaxVersionId();

}
