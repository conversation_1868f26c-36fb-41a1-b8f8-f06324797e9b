package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.PublicWendaoCertInfoAuthorized;

/**
 * 公共资质授权Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
public interface IPublicWendaoCertInfoAuthorizedService 
{
    /**
     * 查询公共资质授权
     * 
     * @param id 公共资质授权主键
     * @return 公共资质授权
     */
    public PublicWendaoCertInfoAuthorized selectPublicWendaoCertInfoAuthorizedById(Long id);

    /**
     * 查询公共资质授权列表
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 公共资质授权集合
     */
    public List<PublicWendaoCertInfoAuthorized> selectPublicWendaoCertInfoAuthorizedList(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized);

    /**
     * 新增公共资质授权
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 结果
     */
    public int insertPublicWendaoCertInfoAuthorized(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized);

    /**
     * 修改公共资质授权
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 结果
     */
    public int updatePublicWendaoCertInfoAuthorized(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized);

    /**
     * 批量删除公共资质授权
     * 
     * @param ids 需要删除的公共资质授权主键集合
     * @return 结果
     */
    public int deletePublicWendaoCertInfoAuthorizedByIds(Long[] ids);

    /**
     * 删除公共资质授权信息
     * 
     * @param id 公共资质授权主键
     * @return 结果
     */
    public int deletePublicWendaoCertInfoAuthorizedById(Long id);
}
