package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.WendaoTcplayerLicense;
import com.ruoyi.system.mapper.WendaoTcplayerLicenseMapper;
import com.ruoyi.system.service.IWendaoTcplayerLicenseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 腾讯播放器证书对应Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
public class WendaoTcplayerLicenseServiceImpl implements IWendaoTcplayerLicenseService
{
    @Autowired
    private WendaoTcplayerLicenseMapper wendaoTcplayerLicenseMapper;

    /**
     * 查询腾讯播放器证书对应
     * 
     * @param id 腾讯播放器证书对应主键
     * @return 腾讯播放器证书对应
     */
    @Override
    public WendaoTcplayerLicense selectWendaoTcplayerLicenseById(Long id)
    {
        return wendaoTcplayerLicenseMapper.selectWendaoTcplayerLicenseById(id);
    }

    /**
     * 查询腾讯播放器证书对应列表
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 腾讯播放器证书对应
     */
    @Override
    public List<WendaoTcplayerLicense> selectWendaoTcplayerLicenseList(WendaoTcplayerLicense wendaoTcplayerLicense)
    {
        return wendaoTcplayerLicenseMapper.selectWendaoTcplayerLicenseList(wendaoTcplayerLicense);
    }

    /**
     * 新增腾讯播放器证书对应
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 结果
     */
    @Override
    public int insertWendaoTcplayerLicense(WendaoTcplayerLicense wendaoTcplayerLicense)
    {
        wendaoTcplayerLicense.setCreateTime(DateUtils.getNowDate());
        return wendaoTcplayerLicenseMapper.insertWendaoTcplayerLicense(wendaoTcplayerLicense);
    }

    /**
     * 修改腾讯播放器证书对应
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 结果
     */
    @Override
    public int updateWendaoTcplayerLicense(WendaoTcplayerLicense wendaoTcplayerLicense)
    {
        wendaoTcplayerLicense.setUpdateTime(DateUtils.getNowDate());
        return wendaoTcplayerLicenseMapper.updateWendaoTcplayerLicense(wendaoTcplayerLicense);
    }

    /**
     * 批量删除腾讯播放器证书对应
     * 
     * @param ids 需要删除的腾讯播放器证书对应主键
     * @return 结果
     */
    @Override
    public int deleteWendaoTcplayerLicenseByIds(Long[] ids)
    {
        return wendaoTcplayerLicenseMapper.deleteWendaoTcplayerLicenseByIds(ids);
    }

    /**
     * 删除腾讯播放器证书对应信息
     * 
     * @param id 腾讯播放器证书对应主键
     * @return 结果
     */
    @Override
    public int deleteWendaoTcplayerLicenseById(Long id)
    {
        return wendaoTcplayerLicenseMapper.deleteWendaoTcplayerLicenseById(id);
    }

    @Override
    public WendaoTcplayerLicense selectNoUsedDomainRandom() {
        return wendaoTcplayerLicenseMapper.selectNoUsedDomainRandom();
    }

    @Override
    public WendaoTcplayerLicense selectByDomain(String knowledgeStoreDomain) {
        return wendaoTcplayerLicenseMapper.selectByDomain(knowledgeStoreDomain);
    }
}
