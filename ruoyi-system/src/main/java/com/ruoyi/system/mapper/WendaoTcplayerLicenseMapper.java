package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.WendaoTcplayerLicense;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 腾讯播放器证书对应Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface WendaoTcplayerLicenseMapper 
{
    /**
     * 查询腾讯播放器证书对应
     * 
     * @param id 腾讯播放器证书对应主键
     * @return 腾讯播放器证书对应
     */
    public WendaoTcplayerLicense selectWendaoTcplayerLicenseById(Long id);

    /**
     * 查询腾讯播放器证书对应列表
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 腾讯播放器证书对应集合
     */
    public List<WendaoTcplayerLicense> selectWendaoTcplayerLicenseList(WendaoTcplayerLicense wendaoTcplayerLicense);

    /**
     * 新增腾讯播放器证书对应
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 结果
     */
    public int insertWendaoTcplayerLicense(WendaoTcplayerLicense wendaoTcplayerLicense);

    /**
     * 修改腾讯播放器证书对应
     * 
     * @param wendaoTcplayerLicense 腾讯播放器证书对应
     * @return 结果
     */
    public int updateWendaoTcplayerLicense(WendaoTcplayerLicense wendaoTcplayerLicense);

    /**
     * 删除腾讯播放器证书对应
     * 
     * @param id 腾讯播放器证书对应主键
     * @return 结果
     */
    public int deleteWendaoTcplayerLicenseById(Long id);

    /**
     * 批量删除腾讯播放器证书对应
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoTcplayerLicenseByIds(Long[] ids);

    WendaoTcplayerLicense selectNoUsedDomainRandom();

    WendaoTcplayerLicense selectByDomain(@Param("knowledgeStoreDomain")String knowledgeStoreDomain);

}
