package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.PublicWendaoCertInfoAuthorized;

/**
 * 公共资质授权Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
public interface PublicWendaoCertInfoAuthorizedMapper 
{
    /**
     * 查询公共资质授权
     * 
     * @param id 公共资质授权主键
     * @return 公共资质授权
     */
    public PublicWendaoCertInfoAuthorized selectPublicWendaoCertInfoAuthorizedById(Long id);

    /**
     * 查询公共资质授权列表
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 公共资质授权集合
     */
    public List<PublicWendaoCertInfoAuthorized> selectPublicWendaoCertInfoAuthorizedList(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized);

    /**
     * 新增公共资质授权
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 结果
     */
    public int insertPublicWendaoCertInfoAuthorized(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized);

    /**
     * 修改公共资质授权
     * 
     * @param publicWendaoCertInfoAuthorized 公共资质授权
     * @return 结果
     */
    public int updatePublicWendaoCertInfoAuthorized(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized);

    /**
     * 删除公共资质授权
     * 
     * @param id 公共资质授权主键
     * @return 结果
     */
    public int deletePublicWendaoCertInfoAuthorizedById(Long id);

    /**
     * 批量删除公共资质授权
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePublicWendaoCertInfoAuthorizedByIds(Long[] ids);
}
