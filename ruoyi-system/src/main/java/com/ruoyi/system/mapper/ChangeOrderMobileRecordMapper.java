package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ChangeOrderMobileRecord;

/**
 * 修改订单手机号码记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface ChangeOrderMobileRecordMapper 
{
    /**
     * 查询修改订单手机号码记录
     * 
     * @param id 修改订单手机号码记录主键
     * @return 修改订单手机号码记录
     */
    public ChangeOrderMobileRecord selectChangeOrderMobileRecordById(Long id);

    /**
     * 查询修改订单手机号码记录列表
     * 
     * @param changeOrderMobileRecord 修改订单手机号码记录
     * @return 修改订单手机号码记录集合
     */
    public List<ChangeOrderMobileRecord> selectChangeOrderMobileRecordList(ChangeOrderMobileRecord changeOrderMobileRecord);

    /**
     * 新增修改订单手机号码记录
     * 
     * @param changeOrderMobileRecord 修改订单手机号码记录
     * @return 结果
     */
    public int insertChangeOrderMobileRecord(ChangeOrderMobileRecord changeOrderMobileRecord);

    /**
     * 修改修改订单手机号码记录
     * 
     * @param changeOrderMobileRecord 修改订单手机号码记录
     * @return 结果
     */
    public int updateChangeOrderMobileRecord(ChangeOrderMobileRecord changeOrderMobileRecord);

    /**
     * 删除修改订单手机号码记录
     * 
     * @param id 修改订单手机号码记录主键
     * @return 结果
     */
    public int deleteChangeOrderMobileRecordById(Long id);

    /**
     * 批量删除修改订单手机号码记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChangeOrderMobileRecordByIds(Long[] ids);
}
