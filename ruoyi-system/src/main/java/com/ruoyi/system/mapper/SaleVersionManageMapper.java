package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SaleVersionManage;

/**
 * 问到系统售卖版本管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface SaleVersionManageMapper 
{
    /**
     * 查询问到系统售卖版本管理
     * 
     * @param id 问到系统售卖版本管理主键
     * @return 问到系统售卖版本管理
     */
    public SaleVersionManage selectSaleVersionManageById(Long id);

    /**
     * 查询问到系统售卖版本管理列表
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 问到系统售卖版本管理集合
     */
    public List<SaleVersionManage> selectSaleVersionManageList(SaleVersionManage saleVersionManage);

    /**
     * 新增问到系统售卖版本管理
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 结果
     */
    public int insertSaleVersionManage(SaleVersionManage saleVersionManage);

    /**
     * 修改问到系统售卖版本管理
     * 
     * @param saleVersionManage 问到系统售卖版本管理
     * @return 结果
     */
    public int updateSaleVersionManage(SaleVersionManage saleVersionManage);

    /**
     * 删除问到系统售卖版本管理
     * 
     * @param id 问到系统售卖版本管理主键
     * @return 结果
     */
    public int deleteSaleVersionManageById(Long id);

    /**
     * 批量删除问到系统售卖版本管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSaleVersionManageByIds(Long[] ids);

    Integer selectMaxVersionId();

}
