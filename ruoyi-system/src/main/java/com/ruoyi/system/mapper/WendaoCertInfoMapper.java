package com.ruoyi.system.mapper;


import com.ruoyi.system.domain.WendaoCertInfo;

import java.util.List;

/**
 * 资质中心信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-02
 */
public interface WendaoCertInfoMapper 
{
    /**
     * 查询资质中心信息
     * 
     * @param id 资质中心信息主键
     * @return 资质中心信息
     */
    public WendaoCertInfo selectWendaoCertInfoById(Long id);

    /**
     * 查询资质中心信息列表
     * 
     * @param wendaoCertInfo 资质中心信息
     * @return 资质中心信息集合
     */
    public List<WendaoCertInfo> selectWendaoCertInfoList(WendaoCertInfo wendaoCertInfo);

    /**
     * 新增资质中心信息
     * 
     * @param wendaoCertInfo 资质中心信息
     * @return 结果
     */
    public int insertWendaoCertInfo(WendaoCertInfo wendaoCertInfo);

    /**
     * 修改资质中心信息
     * 
     * @param wendaoCertInfo 资质中心信息
     * @return 结果
     */
    public int updateWendaoCertInfo(WendaoCertInfo wendaoCertInfo);

    public int updateWendaoCertInfo1(WendaoCertInfo wendaoCertInfo);

    /**
     * 删除资质中心信息
     * 
     * @param id 资质中心信息主键
     * @return 结果
     */
    public int deleteWendaoCertInfoById(Long id);

    /**
     * 批量删除资质中心信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoCertInfoByIds(Long[] ids);

    List<WendaoCertInfo> selectWendaoCertInfoBySearchValue(WendaoCertInfo wendaoCertInfo);
}
