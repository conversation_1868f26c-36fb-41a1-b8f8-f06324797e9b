package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.DouyinClass;

/**
 * 抖音分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface DouyinClassMapper 
{
    /**
     * 查询抖音分类
     * 
     * @param id 抖音分类主键
     * @return 抖音分类
     */
    public DouyinClass selectDouyinClassById(Long id);

    /**
     * 查询抖音分类列表
     * 
     * @param douyinClass 抖音分类
     * @return 抖音分类集合
     */
    public List<DouyinClass> selectDouyinClassList(DouyinClass douyinClass);

    /**
     * 新增抖音分类
     * 
     * @param douyinClass 抖音分类
     * @return 结果
     */
    public int insertDouyinClass(DouyinClass douyinClass);

    /**
     * 修改抖音分类
     * 
     * @param douyinClass 抖音分类
     * @return 结果
     */
    public int updateDouyinClass(DouyinClass douyinClass);

    /**
     * 删除抖音分类
     * 
     * @param id 抖音分类主键
     * @return 结果
     */
    public int deleteDouyinClassById(Long id);

    /**
     * 批量删除抖音分类
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDouyinClassByIds(Long[] ids);
}
