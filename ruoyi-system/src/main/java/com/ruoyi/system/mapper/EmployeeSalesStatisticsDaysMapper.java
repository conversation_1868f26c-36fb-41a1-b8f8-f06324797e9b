package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.EmployeeSalesStatisticsDays;

/**
 * 员工销售数据统计按日期Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface EmployeeSalesStatisticsDaysMapper 
{
    /**
     * 查询员工销售数据统计按日期
     * 
     * @param id 员工销售数据统计按日期主键
     * @return 员工销售数据统计按日期
     */
    public EmployeeSalesStatisticsDays selectEmployeeSalesStatisticsDaysById(Long id);

    /**
     * 查询员工销售数据统计按日期列表
     * 
     * @param employeeSalesStatisticsDays 员工销售数据统计按日期
     * @return 员工销售数据统计按日期集合
     */
    public List<EmployeeSalesStatisticsDays> selectEmployeeSalesStatisticsDaysList(EmployeeSalesStatisticsDays employeeSalesStatisticsDays);

    /**
     * 新增员工销售数据统计按日期
     * 
     * @param employeeSalesStatisticsDays 员工销售数据统计按日期
     * @return 结果
     */
    public int insertEmployeeSalesStatisticsDays(EmployeeSalesStatisticsDays employeeSalesStatisticsDays);

    /**
     * 修改员工销售数据统计按日期
     * 
     * @param employeeSalesStatisticsDays 员工销售数据统计按日期
     * @return 结果
     */
    public int updateEmployeeSalesStatisticsDays(EmployeeSalesStatisticsDays employeeSalesStatisticsDays);

    /**
     * 删除员工销售数据统计按日期
     * 
     * @param id 员工销售数据统计按日期主键
     * @return 结果
     */
    public int deleteEmployeeSalesStatisticsDaysById(Long id);

    /**
     * 批量删除员工销售数据统计按日期
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmployeeSalesStatisticsDaysByIds(Long[] ids);

    List<EmployeeSalesStatisticsDays> selectEmployeeSalesStatisticsDaysListSumParentLeaderId(EmployeeSalesStatisticsDays employeeSalesStatisticsDays);

    List<EmployeeSalesStatisticsDays> selectEmployeeSalesStatisticsDaysListSumLeaderId(EmployeeSalesStatisticsDays employeeSalesStatisticsDays);
}