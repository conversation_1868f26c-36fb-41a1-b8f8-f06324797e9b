package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.EnterInformationBackup;
import org.apache.ibatis.annotations.Param;

/**
 *  入驻信息备份Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
public interface EnterInformationBackupMapper 
{
    /**
     * 查询入驻信息备份
     * 
     * @param id 入驻信息备份主键
     * @return 入驻信息备份
     */
    public EnterInformationBackup selectEnterInformationBackupById(Long id);

    /**
     * 查询入驻信息备份列表
     * 
     * @param enterInformationBackup 入驻信息备份
     * @return 入驻信息备份集合
     */
    public List<EnterInformationBackup> selectEnterInformationBackupList(EnterInformationBackup enterInformationBackup);

    /**
     * 新增入驻信息备份
     * 
     * @param enterInformationBackup 入驻信息备份
     * @return 结果
     */
    public int insertEnterInformationBackup(EnterInformationBackup enterInformationBackup);

    /**
     * 修改入驻信息备份
     * 
     * @param enterInformationBackup 入驻信息备份
     * @return 结果
     */
    public int updateEnterInformationBackup(EnterInformationBackup enterInformationBackup);

    /**
     * 删除入驻信息备份
     * 
     * @param id 入驻信息备份主键
     * @return 结果
     */
    public int deleteEnterInformationBackupById(Long id);

    /**
     * 批量删除入驻信息备份
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEnterInformationBackupByIds(Long[] ids);
    
    /**
     * 根据客户端入驻信息ID查询备份信息
     *
     * @param clientEnterInformationId 客户端入驻信息ID
     * @return 入驻信息备份
     */
    EnterInformationBackup selectEnterInformationBackupByClientId(@Param("clientEnterInformationId") Long clientEnterInformationId);
} 