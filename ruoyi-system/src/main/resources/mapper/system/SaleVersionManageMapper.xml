<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SaleVersionManageMapper">
    
    <resultMap type="SaleVersionManage" id="SaleVersionManageResult">
        <result property="id"    column="id"    />
        <result property="versionName"    column="version_name"    />
        <result property="versionImage"    column="version_image"    />
        <result property="versionId"    column="version_id"    />
        <result property="versionDesc"    column="version_desc"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="versionPrice"    column="version_price"    />
        <result property="marginMargin"    column="margin_margin"    />
        <result property="gmvGuarantee"    column="gmv_guarantee"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="versionPurchaseCycle"    column="version_purchase_cycle"    />
        <result property="rate"    column="rate"    />
    </resultMap>

    <sql id="selectSaleVersionManageVo">
        select id, version_name, version_image, version_id, version_desc,
               is_enabled, version_price, margin_margin, gmv_guarantee, create_time, update_time, version_purchase_cycle, rate from sale_version_manage
    </sql>

    <select id="selectSaleVersionManageList" parameterType="SaleVersionManage" resultMap="SaleVersionManageResult">
        <include refid="selectSaleVersionManageVo"/>
        <where>  
            <if test="versionName != null  and versionName != ''"> and version_name like concat('%', #{versionName}, '%')</if>
            <if test="versionImage != null  and versionImage != ''"> and version_image = #{versionImage}</if>
            <if test="versionId != null "> and version_id = #{versionId}</if>
            <if test="versionDesc != null  and versionDesc != ''"> and version_desc = #{versionDesc}</if>
            <if test="isEnabled != null "> and is_enabled = #{isEnabled}</if>
            <if test="versionPrice != null "> and version_price = #{versionPrice}</if>
            <if test="marginMargin != null "> and margin_margin = #{marginMargin}</if>
            <if test="gmvGuarantee != null "> and gmv_guarantee = #{gmvGuarantee}</if>
            <if test="versionPurchaseCycle != null  and versionPurchaseCycle != ''"> and version_purchase_cycle = #{versionPurchaseCycle}</if>
            <if test="rate != null"> and rate = #{rate}</if>
        </where>
    </select>
    
    <select id="selectSaleVersionManageById" parameterType="Long" resultMap="SaleVersionManageResult">
        <include refid="selectSaleVersionManageVo"/>
        where id = #{id}
    </select>
    <select id="selectMaxVersionId" resultType="java.lang.Integer">
        select max(version_id) from sale_version_manage
    </select>

    <insert id="insertSaleVersionManage" parameterType="SaleVersionManage" useGeneratedKeys="true" keyProperty="id">
        insert into sale_version_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="versionName != null">version_name,</if>
            <if test="versionImage != null">version_image,</if>
            <if test="versionId != null">version_id,</if>
            <if test="versionDesc != null">version_desc,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="versionPrice != null">version_price,</if>
            <if test="marginMargin != null">margin_margin,</if>
            <if test="gmvGuarantee != null">gmv_guarantee,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="versionPurchaseCycle != null">version_purchase_cycle,</if>
            <if test="rate != null">rate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="versionName != null">#{versionName},</if>
            <if test="versionImage != null">#{versionImage},</if>
            <if test="versionId != null">#{versionId},</if>
            <if test="versionDesc != null">#{versionDesc},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="versionPrice != null">#{versionPrice},</if>
            <if test="marginMargin != null">#{marginMargin},</if>
            <if test="gmvGuarantee != null">#{gmvGuarantee},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="versionPurchaseCycle != null">#{versionPurchaseCycle},</if>
            <if test="rate != null">#{rate},</if>
         </trim>
    </insert>

    <update id="updateSaleVersionManage" parameterType="SaleVersionManage">
        update sale_version_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="versionName != null">version_name = #{versionName},</if>
            <if test="versionImage != null">version_image = #{versionImage},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="versionDesc != null">version_desc = #{versionDesc},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="versionPrice != null">version_price = #{versionPrice},</if>
            <if test="marginMargin != null">margin_margin = #{marginMargin},</if>
            <if test="gmvGuarantee != null">gmv_guarantee = #{gmvGuarantee},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="versionPurchaseCycle != null">version_purchase_cycle = #{versionPurchaseCycle},</if>
            <if test="rate != null">rate = #{rate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSaleVersionManageById" parameterType="Long">
        delete from sale_version_manage where id = #{id}
    </delete>

    <delete id="deleteSaleVersionManageByIds" parameterType="String">
        delete from sale_version_manage where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>