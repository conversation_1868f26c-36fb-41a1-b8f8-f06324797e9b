<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.PassedPublicClassMapper">
    
    <resultMap type="PassedPublicClass" id="PassedPublicClassResult">
        <result property="id"    column="id"    />
        <result property="entityId"    column="entity_id"    />
        <result property="firstClassId"    column="first_class_id"    />
        <result property="firstClassPid"    column="first_class_pid"    />
        <result property="firstClassTitle"    column="first_class_title"    />
        <result property="firstClassDouyinClassId"    column="first_class_douyin_class_id"    />
        <result property="secondClassId"    column="second_class_id"    />
        <result property="secondClassPid"    column="second_class_pid"    />
        <result property="secondClassTitle"    column="second_class_title"    />
        <result property="secondClassDouyinClassId"    column="second_class_douyin_class_id"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="certPublicName"    column="cert_public_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPassedPublicClassVo">
        select id, entity_id, first_class_id, first_class_pid, first_class_title, first_class_douyin_class_id, second_class_id, second_class_pid, second_class_title, second_class_douyin_class_id, audit_status, certificate_type, cert_public_name, create_time, update_time from passed_public_class
    </sql>

    <select id="selectPassedPublicClassList" parameterType="PassedPublicClass" resultMap="PassedPublicClassResult">
        <include refid="selectPassedPublicClassVo"/>
        <where>  
            <if test="entityId != null  and entityId != ''"> and entity_id = #{entityId}</if>
            <if test="firstClassId != null "> and first_class_id = #{firstClassId}</if>
            <if test="firstClassPid != null "> and first_class_pid = #{firstClassPid}</if>
            <if test="firstClassTitle != null  and firstClassTitle != ''"> and first_class_title = #{firstClassTitle}</if>
            <if test="firstClassDouyinClassId != null "> and first_class_douyin_class_id = #{firstClassDouyinClassId}</if>
            <if test="secondClassId != null "> and second_class_id = #{secondClassId}</if>
            <if test="secondClassPid != null "> and second_class_pid = #{secondClassPid}</if>
            <if test="secondClassTitle != null  and secondClassTitle != ''"> and second_class_title = #{secondClassTitle}</if>
            <if test="secondClassDouyinClassId != null "> and second_class_douyin_class_id = #{secondClassDouyinClassId}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="certificateType != null "> and certificate_type = #{certificateType}</if>
            <if test="certPublicName != null  and certPublicName != ''"> and cert_public_name like concat('%', #{certPublicName}, '%')</if>
        </where>
    </select>
    
    <select id="selectPassedPublicClassById" parameterType="Long" resultMap="PassedPublicClassResult">
        <include refid="selectPassedPublicClassVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPassedPublicClass" parameterType="PassedPublicClass" useGeneratedKeys="true" keyProperty="id">
        insert into passed_public_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityId != null">entity_id,</if>
            <if test="firstClassId != null">first_class_id,</if>
            <if test="firstClassPid != null">first_class_pid,</if>
            <if test="firstClassTitle != null">first_class_title,</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id,</if>
            <if test="secondClassId != null">second_class_id,</if>
            <if test="secondClassPid != null">second_class_pid,</if>
            <if test="secondClassTitle != null">second_class_title,</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="certificateType != null">certificate_type,</if>
            <if test="certPublicName != null">cert_public_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityId != null">#{entityId},</if>
            <if test="firstClassId != null">#{firstClassId},</if>
            <if test="firstClassPid != null">#{firstClassPid},</if>
            <if test="firstClassTitle != null">#{firstClassTitle},</if>
            <if test="firstClassDouyinClassId != null">#{firstClassDouyinClassId},</if>
            <if test="secondClassId != null">#{secondClassId},</if>
            <if test="secondClassPid != null">#{secondClassPid},</if>
            <if test="secondClassTitle != null">#{secondClassTitle},</if>
            <if test="secondClassDouyinClassId != null">#{secondClassDouyinClassId},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="certificateType != null">#{certificateType},</if>
            <if test="certPublicName != null">#{certPublicName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePassedPublicClass" parameterType="PassedPublicClass">
        update passed_public_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="entityId != null">entity_id = #{entityId},</if>
            <if test="firstClassId != null">first_class_id = #{firstClassId},</if>
            <if test="firstClassPid != null">first_class_pid = #{firstClassPid},</if>
            <if test="firstClassTitle != null">first_class_title = #{firstClassTitle},</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id = #{firstClassDouyinClassId},</if>
            <if test="secondClassId != null">second_class_id = #{secondClassId},</if>
            <if test="secondClassPid != null">second_class_pid = #{secondClassPid},</if>
            <if test="secondClassTitle != null">second_class_title = #{secondClassTitle},</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id = #{secondClassDouyinClassId},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="certificateType != null">certificate_type = #{certificateType},</if>
            <if test="certPublicName != null">cert_public_name = #{certPublicName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePassedPublicClassById" parameterType="Long">
        delete from passed_public_class where id = #{id}
    </delete>

    <delete id="deletePassedPublicClassByIds" parameterType="String">
        delete from passed_public_class where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>