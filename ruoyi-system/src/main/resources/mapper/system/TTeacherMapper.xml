<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TTeacherMapper">
    
    <resultMap type="TTeacher" id="TTeacherResult">
        <result property="teacherId"    column="teacher_id"    />
        <result property="unionId"    column="union_id"    />
        <result property="openId"    column="open_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="mobile"    column="mobile"    />
        <result property="loginNum"    column="login_num"    />
        <result property="password"    column="password"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="teacherDesc"    column="teacher_desc"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="platform"    column="platform"    />
        <result property="hot"    column="hot"    />
        <result property="loginIp"    column="login_ip"    />
        <result property="loginDate"    column="login_date"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="appNameType"    column="app_name_type"    />
    </resultMap>

    <sql id="selectTTeacherVo">
        select teacher_id, union_id, open_id, shop_name, mobile, login_num, password, teacher_name, teacher_desc, avatar_url, platform, hot, login_ip, login_date, status, create_time, update_time,app_name_type from `wendao101-teacher`.t_teacher
    </sql>

    <select id="selectTTeacherList" parameterType="TTeacher" resultMap="TTeacherResult">
        <include refid="selectTTeacherVo"/>
        <where>  
            <if test="unionId != null  and unionId != ''"> and union_id = #{unionId}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="loginNum != null "> and login_num = #{loginNum}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="teacherDesc != null  and teacherDesc != ''"> and teacher_desc = #{teacherDesc}</if>
            <if test="avatarUrl != null  and avatarUrl != ''"> and avatar_url = #{avatarUrl}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="hot != null "> and hot = #{hot}</if>
            <if test="loginIp != null  and loginIp != ''"> and login_ip = #{loginIp}</if>
            <if test="loginDate != null "> and login_date = #{loginDate}</if>
            <if test="status != null "> and status = #{status}</if>

            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>

            <if test="searchValue != null and searchValue!=''">
                and (
                shop_name like concat('%', #{searchValue}, '%')
                or mobile like concat('%', #{searchValue}, '%')
                or teacher_id like concat('%', #{searchValue}, '%')
                )

             </if>
        </where>
    </select>
    
    <select id="selectTTeacherByTeacherId" parameterType="Long" resultMap="TTeacherResult">
        <include refid="selectTTeacherVo"/>
        where teacher_id = #{teacherId}
    </select>
    <select id="selectTeacherPics" resultType="com.ruoyi.system.dto.UEditorImageDTO">
        select path_url url from `wendao101-teacher`.t_pic where teacher_id = #{teacherId} and is_delete=0 order by create_time desc
    </select>

    <insert id="insertTTeacher" parameterType="TTeacher" useGeneratedKeys="true" keyProperty="teacherId">
        insert into `wendao101-teacher`.t_teacher
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unionId != null">union_id,</if>
            <if test="openId != null">open_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="mobile != null">mobile,</if>
            <if test="loginNum != null">login_num,</if>
            <if test="password != null">password,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="teacherDesc != null">teacher_desc,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="platform != null">platform,</if>
            <if test="hot != null">hot,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unionId != null">#{unionId},</if>
            <if test="openId != null">#{openId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="loginNum != null">#{loginNum},</if>
            <if test="password != null">#{password},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="teacherDesc != null">#{teacherDesc},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="platform != null">#{platform},</if>
            <if test="hot != null">#{hot},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="insertTPic" parameterType="com.ruoyi.system.dto.PicDTO" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-teacher`.t_pic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="pathUrl != null">path_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileAfterTransSize != null">file_after_trans_size,</if>
            <if test="fileOriginalSize != null">file_original_size,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="transcodeStatus != null">transcode_status,</if>
            <if test="tcvodFileId != null">tcvod_file_id,</if>
            <if test="tcvodMediaUrlBeforeTrans != null">tcvod_media_url_before_trans,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="procedureErrorMsg != null">procedure_error_msg,</if>
            <if test="transcodeErrorMsg != null">transcode_error_msg,</if>
            <if test="sourceMaterialId != null">source_material_id,</if>
            <if test="appNameType != null">app_name_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="pathUrl != null">#{pathUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileAfterTransSize != null">#{fileAfterTransSize},</if>
            <if test="fileOriginalSize != null">#{fileOriginalSize},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="transcodeStatus != null">#{transcodeStatus},</if>
            <if test="tcvodFileId != null">#{tcvodFileId},</if>
            <if test="tcvodMediaUrlBeforeTrans != null">#{tcvodMediaUrlBeforeTrans},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="procedureErrorMsg != null">#{procedureErrorMsg},</if>
            <if test="transcodeErrorMsg != null">#{transcodeErrorMsg},</if>
            <if test="sourceMaterialId != null">#{sourceMaterialId},</if>
            <if test="appNameType != null">#{appNameType},</if>
        </trim>
    </insert>

    <update id="updateTTeacher" parameterType="TTeacher">
        update `wendao101-teacher`.t_teacher
        <trim prefix="SET" suffixOverrides=",">
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="loginNum != null">login_num = #{loginNum},</if>
            <if test="password != null">password = #{password},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="teacherDesc != null">teacher_desc = #{teacherDesc},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="hot != null">hot = #{hot},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where teacher_id = #{teacherId}
    </update>

    <delete id="deleteTTeacherByTeacherId" parameterType="Long">
        delete from `wendao101-teacher`.t_teacher where teacher_id = #{teacherId}
    </delete>

    <delete id="deleteTTeacherByTeacherIds" parameterType="String">
        delete from `wendao101-teacher`.t_teacher where teacher_id in
        <foreach item="teacherId" collection="array" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
    </delete>
</mapper>