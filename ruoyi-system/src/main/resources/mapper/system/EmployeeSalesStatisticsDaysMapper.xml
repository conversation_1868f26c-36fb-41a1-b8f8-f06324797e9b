<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.EmployeeSalesStatisticsDaysMapper">
    
    <resultMap type="EmployeeSalesStatisticsDays" id="EmployeeSalesStatisticsDaysResult">
        <result property="id"    column="id"    />
        <result property="statDate"    column="stat_date"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="totalSales"    column="total_sales"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="leaderId"    column="leader_id"    />
        <result property="leaderNickName"    column="leader_nick_name"    />
        <result property="parentLeaderId"    column="parent_leader_id"    />
        <result property="parentLeaderNickName"    column="parent_leader_nick_name"    />
    </resultMap>

    <sql id="selectEmployeeSalesStatisticsDaysVo">
        select id, stat_date, dept_id, user_id, total_sales, create_time, update_time, leader_id, leader_nick_name, parent_leader_id, parent_leader_nick_name from `wendao101-order`.employee_sales_statistics_days
    </sql>

    <select id="selectEmployeeSalesStatisticsDaysList" parameterType="EmployeeSalesStatisticsDays" resultMap="EmployeeSalesStatisticsDaysResult">
        <include refid="selectEmployeeSalesStatisticsDaysVo"/>
        <where>  
            <if test="statDate != null "> and stat_date = #{statDate}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="totalSales != null "> and total_sales = #{totalSales}</if>
            <if test="leaderId != null "> and leader_id = #{leaderId}</if>
            <if test="leaderNickName != null  and leaderNickName != ''"> and leader_nick_name like concat('%', #{leaderNickName}, '%')</if>
            <if test="parentLeaderId != null "> and parent_leader_id = #{parentLeaderId}</if>
            <if test="parentLeaderNickName != null  and parentLeaderNickName != ''"> and parent_leader_nick_name like concat('%', #{parentLeaderNickName}, '%')</if>
        </where>
        order by id desc limit 7
    </select>
    
    <select id="selectEmployeeSalesStatisticsDaysById" parameterType="Long" resultMap="EmployeeSalesStatisticsDaysResult">
        <include refid="selectEmployeeSalesStatisticsDaysVo"/>
        where id = #{id}
    </select>
    <select id="selectEmployeeSalesStatisticsDaysListSumParentLeaderId" parameterType="EmployeeSalesStatisticsDays" resultMap="EmployeeSalesStatisticsDaysResult">
        select stat_date, sum(total_sales) as total_sales from `wendao101-order`.employee_sales_statistics_days where parent_leader_id = #{userId} group by stat_date order by stat_date
    </select>
    <select id="selectEmployeeSalesStatisticsDaysListSumLeaderId" parameterType="EmployeeSalesStatisticsDays" resultMap="EmployeeSalesStatisticsDaysResult">
        select stat_date, sum(total_sales) as total_sales from `wendao101-order`.employee_sales_statistics_days where leader_id = #{userId} group by stat_date order by stat_date
    </select>

    <insert id="insertEmployeeSalesStatisticsDays" parameterType="EmployeeSalesStatisticsDays" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.employee_sales_statistics_days
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statDate != null">stat_date,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="totalSales != null">total_sales,</if>
            <if test="leaderId != null">leader_id,</if>
            <if test="leaderNickName != null">leader_nick_name,</if>
            <if test="parentLeaderId != null">parent_leader_id,</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statDate != null">#{statDate},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="totalSales != null">#{totalSales},</if>
            <if test="leaderId != null">#{leaderId},</if>
            <if test="leaderNickName != null">#{leaderNickName},</if>
            <if test="parentLeaderId != null">#{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">#{parentLeaderNickName},</if>
         </trim>
    </insert>

    <update id="updateEmployeeSalesStatisticsDays" parameterType="EmployeeSalesStatisticsDays">
        update `wendao101-order`.employee_sales_statistics_days
        <trim prefix="SET" suffixOverrides=",">
            <if test="statDate != null">stat_date = #{statDate},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="totalSales != null">total_sales = #{totalSales},</if>
            <if test="leaderId != null">leader_id = #{leaderId},</if>
            <if test="leaderNickName != null">leader_nick_name = #{leaderNickName},</if>
            <if test="parentLeaderId != null">parent_leader_id = #{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name = #{parentLeaderNickName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeSalesStatisticsDaysById" parameterType="Long">
        delete from `wendao101-order`.employee_sales_statistics_days where id = #{id}
    </delete>

    <delete id="deleteEmployeeSalesStatisticsDaysByIds" parameterType="String">
        delete from `wendao101-order`.employee_sales_statistics_days where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 