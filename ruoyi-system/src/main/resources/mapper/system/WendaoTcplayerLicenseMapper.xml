<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.WendaoTcplayerLicenseMapper">
    
    <resultMap type="WendaoTcplayerLicense" id="WendaoTcplayerLicenseResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="licenseUrl"    column="license_url"    />
        <result property="licenseKey"    column="license_key"    />
        <result property="knowledgeStoreDomain"    column="knowledge_store_domain"    />
        <result property="isUsed"    column="is_used"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="validStartTime"    column="valid_start_time"    />
        <result property="validEndTime"    column="valid_end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWendaoTcplayerLicenseVo">
        select id, teacher_id, license_url, license_key, knowledge_store_domain, is_used, is_delete, valid_start_time, valid_end_time, create_time, update_time from `wendao101-teacher`.wendao_tcplayer_license
    </sql>

    <select id="selectWendaoTcplayerLicenseList" parameterType="WendaoTcplayerLicense" resultMap="WendaoTcplayerLicenseResult">
        <include refid="selectWendaoTcplayerLicenseVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="licenseUrl != null  and licenseUrl != ''"> and license_url = #{licenseUrl}</if>
            <if test="licenseKey != null  and licenseKey != ''"> and license_key = #{licenseKey}</if>
            <if test="knowledgeStoreDomain != null  and knowledgeStoreDomain != ''"> and knowledge_store_domain = #{knowledgeStoreDomain}</if>
            <if test="isUsed != null "> and is_used = #{isUsed}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="validStartTime != null "> and valid_start_time = #{validStartTime}</if>
            <if test="validEndTime != null "> and valid_end_time = #{validEndTime}</if>
        </where>
    </select>
    
    <select id="selectWendaoTcplayerLicenseById" parameterType="Long" resultMap="WendaoTcplayerLicenseResult">
        <include refid="selectWendaoTcplayerLicenseVo"/>
        where id = #{id}
    </select>
    <select id="selectNoUsedDomainRandom" resultMap="WendaoTcplayerLicenseResult">
        <include refid="selectWendaoTcplayerLicenseVo"/>
        where teacher_id is null and is_used = 0 and is_delete = 0 limit 1
    </select>
    <select id="selectByDomain" resultMap="WendaoTcplayerLicenseResult">
        <include refid="selectWendaoTcplayerLicenseVo"/>
        where knowledge_store_domain=#{knowledgeStoreDomain} and teacher_id is null and is_used = 1 and is_delete = 0 limit 1
    </select>

    <insert id="insertWendaoTcplayerLicense" parameterType="WendaoTcplayerLicense" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-teacher`.wendao_tcplayer_license
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="licenseUrl != null">license_url,</if>
            <if test="licenseKey != null">license_key,</if>
            <if test="knowledgeStoreDomain != null">knowledge_store_domain,</if>
            <if test="isUsed != null">is_used,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="validStartTime != null">valid_start_time,</if>
            <if test="validEndTime != null">valid_end_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="licenseUrl != null">#{licenseUrl},</if>
            <if test="licenseKey != null">#{licenseKey},</if>
            <if test="knowledgeStoreDomain != null">#{knowledgeStoreDomain},</if>
            <if test="isUsed != null">#{isUsed},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="validStartTime != null">#{validStartTime},</if>
            <if test="validEndTime != null">#{validEndTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWendaoTcplayerLicense" parameterType="WendaoTcplayerLicense">
        update `wendao101-teacher`.wendao_tcplayer_license
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="licenseUrl != null">license_url = #{licenseUrl},</if>
            <if test="licenseKey != null">license_key = #{licenseKey},</if>
            <if test="knowledgeStoreDomain != null">knowledge_store_domain = #{knowledgeStoreDomain},</if>
            <if test="isUsed != null">is_used = #{isUsed},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="validStartTime != null">valid_start_time = #{validStartTime},</if>
            <if test="validEndTime != null">valid_end_time = #{validEndTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWendaoTcplayerLicenseById" parameterType="Long">
        delete from `wendao101-teacher`.wendao_tcplayer_license where id = #{id}
    </delete>

    <delete id="deleteWendaoTcplayerLicenseByIds" parameterType="String">
        delete from `wendao101-teacher`.wendao_tcplayer_license where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>