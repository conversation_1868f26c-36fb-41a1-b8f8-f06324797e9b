<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DouyinClassMapper">
    
    <resultMap type="DouyinClass" id="DouyinClassResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="pid"    column="pid"    />
        <result property="douyinClassId"    column="douyin_class_id"    />
        <result property="needCertIds"    column="need_cert_ids"    />
        <result property="scope"    column="scope"    />
        <result property="content"    column="content"    />
    </resultMap>

    <sql id="selectDouyinClassVo">
        select id, title, pid, douyin_class_id, need_cert_ids, scope, content from douyin_class
    </sql>

    <select id="selectDouyinClassList" parameterType="DouyinClass" resultMap="DouyinClassResult">
        <include refid="selectDouyinClassVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="douyinClassId != null "> and douyin_class_id = #{douyinClassId}</if>
            <if test="needCertIds != null  and needCertIds != ''"> and need_cert_ids = #{needCertIds}</if>
            <if test="scope != null  and scope != ''"> and scope = #{scope}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>
    
    <select id="selectDouyinClassById" parameterType="Long" resultMap="DouyinClassResult">
        <include refid="selectDouyinClassVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDouyinClass" parameterType="DouyinClass" useGeneratedKeys="true" keyProperty="id">
        insert into douyin_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="pid != null">pid,</if>
            <if test="douyinClassId != null">douyin_class_id,</if>
            <if test="needCertIds != null">need_cert_ids,</if>
            <if test="scope != null">scope,</if>
            <if test="content != null">content,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="pid != null">#{pid},</if>
            <if test="douyinClassId != null">#{douyinClassId},</if>
            <if test="needCertIds != null">#{needCertIds},</if>
            <if test="scope != null">#{scope},</if>
            <if test="content != null">#{content},</if>
         </trim>
    </insert>

    <update id="updateDouyinClass" parameterType="DouyinClass">
        update douyin_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="douyinClassId != null">douyin_class_id = #{douyinClassId},</if>
            <if test="needCertIds != null">need_cert_ids = #{needCertIds},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="content != null">content = #{content},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDouyinClassById" parameterType="Long">
        delete from douyin_class where id = #{id}
    </delete>

    <delete id="deleteDouyinClassByIds" parameterType="String">
        delete from douyin_class where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>