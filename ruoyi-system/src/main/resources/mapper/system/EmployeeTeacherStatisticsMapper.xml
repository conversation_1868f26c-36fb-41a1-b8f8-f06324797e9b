<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.EmployeeTeacherStatisticsMapper">
    
    <resultMap type="EmployeeTeacherStatistics" id="EmployeeTeacherStatisticsResult">
        <result property="id"    column="id"    />
        <result property="statisticsDate"    column="statistics_date"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="mobile"    column="mobile"    />
        <result property="shopName"    column="shop_name"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="dealAmount"    column="deal_amount"    />
        <result property="withdrawnAmount"    column="withdrawn_amount"    />
        <result property="moneyInTransit"    column="money_in_transit"    />
        <result property="withdrawableAmount"    column="withdrawable_amount"    />
        <result property="serviceFee"    column="service_fee"    />
        <result property="serviceFeeRate"    column="service_fee_rate"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="timeQueryStr"    column="time_query_str"    />
        <result property="withdrawnAmountFee"    column="withdrawn_amount_fee"    />
        <result property="notWithdrawnFee"    column="not_withdrawn_fee"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="leaderId"    column="leader_id"    />
        <result property="leaderNickName"    column="leader_nick_name"    />
        <result property="parentLeaderId"    column="parent_leader_id"    />
        <result property="parentLeaderNickName"    column="parent_leader_nick_name"    />
        <result property="orderNum"    column="order_num"    />
        <result property="growRatio"    column="grow_ratio"    />
    </resultMap>

    <sql id="selectEmployeeTeacherStatisticsVo">
        select id, statistics_date, teacher_id, mobile, shop_name, app_name_type, deal_amount, withdrawn_amount, money_in_transit, withdrawable_amount, service_fee, service_fee_rate, create_time, update_time, time_query_str, withdrawn_amount_fee, not_withdrawn_fee, employee_id, nick_name, leader_id, leader_nick_name, parent_leader_id, parent_leader_nick_name, order_num from `wendao101-order`.employee_teacher_statistics
    </sql>

    <select id="selectEmployeeTeacherStatisticsList" parameterType="EmployeeTeacherStatistics" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        <where>  
            <if test="statisticsDate != null "> and statistics_date = #{statisticsDate}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="mobile != null  and mobile != ''"> and mobile like concat('%', #{mobile}, '%')</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="dealAmount != null "> and deal_amount = #{dealAmount}</if>
            <if test="withdrawnAmount != null "> and withdrawn_amount = #{withdrawnAmount}</if>
            <if test="moneyInTransit != null "> and money_in_transit = #{moneyInTransit}</if>
            <if test="withdrawableAmount != null "> and withdrawable_amount = #{withdrawableAmount}</if>
            <if test="serviceFee != null "> and service_fee = #{serviceFee}</if>
            <if test="serviceFeeRate != null "> and service_fee_rate = #{serviceFeeRate}</if>
            <if test="timeQueryStr != null  and timeQueryStr != ''"> and time_query_str like concat('%', #{timeQueryStr}, '%')</if>
            <if test="withdrawnAmountFee != null "> and withdrawn_amount_fee = #{withdrawnAmountFee}</if>
            <if test="notWithdrawnFee != null "> and not_withdrawn_fee = #{notWithdrawnFee}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="leaderId != null "> and leader_id = #{leaderId}</if>
            <if test="leaderNickName != null  and leaderNickName != ''"> and leader_nick_name like concat('%', #{leaderNickName}, '%')</if>
            <if test="parentLeaderId != null "> and parent_leader_id = #{parentLeaderId}</if>
            <if test="parentLeaderNickName != null  and parentLeaderNickName != ''"> and parent_leader_nick_name like concat('%', #{parentLeaderNickName}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectEmployeeTeacherStatisticsById" parameterType="Long" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        where id = #{id}
    </select>

    <select id="selectEmployeeTeacherStatisticsByTeacherId" parameterType="Long" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        where teacher_id = #{teacherId}
        order by id desc limit 1
    </select>

    <select id="selectEmployeeTeacherStatisticsByEmployeeId" parameterType="Long" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        where employee_id = #{employeeId}
        order by id desc
    </select>

    <select id="selectEmployeeTeacherStatisticsByLeaderId" parameterType="Long" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        where leader_id = #{leaderId}
        order by id desc
    </select>

    <select id="selectEmployeeTeacherStatisticsByParentLeaderId" parameterType="Long" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        where parent_leader_id = #{parentLeaderId}
        order by id desc
    </select>

    <!-- 根据员工ID列表和时间查询区间查询，按交易金额倒序排列，取前20个 -->
    <select id="selectTop20ByEmployeeIdsAndTimeQueryStr" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        <where>
            <if test="employeeIds != null and employeeIds.size() > 0">
                and employee_id in
                <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
            <if test="timeQueryStr != null and timeQueryStr != ''">
                and time_query_str = #{timeQueryStr}
            </if>
        </where>
        order by deal_amount desc
        limit 20
    </select>

    <!-- 统计店铺总数 -->
    <select id="countTotalShops" resultType="java.lang.Integer">
        select count(distinct teacher_id) from `wendao101-order`.employee_teacher_statistics
        <where>
            <if test="employeeIds != null and employeeIds.size() > 0">
                and employee_id in
                <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
            <if test="timeQueryStr != null and timeQueryStr != ''">
                and time_query_str = #{timeQueryStr}
            </if>
        </where>
    </select>

    <!-- 统计交易金额大于0的店铺数 -->
    <select id="countShopsWithDealAmount" resultType="java.lang.Integer">
        select count(distinct teacher_id) from `wendao101-order`.employee_teacher_statistics
        <where>
            and deal_amount > 0
            <if test="employeeIds != null and employeeIds.size() > 0">
                and employee_id in
                <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
            <if test="timeQueryStr != null and timeQueryStr != ''">
                and time_query_str = #{timeQueryStr}
            </if>
        </where>
    </select>

    <!-- 计算总销售额 -->
    <select id="sumTotalDealAmount" resultType="java.math.BigDecimal">
        select COALESCE(sum(deal_amount), 0) from `wendao101-order`.employee_teacher_statistics
        <where>
            <if test="employeeIds != null and employeeIds.size() > 0">
                and employee_id in
                <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
            <if test="timeQueryStr != null and timeQueryStr != ''">
                and time_query_str = #{timeQueryStr}
            </if>
        </where>
    </select>

    <!-- 计算平均销售额 -->
    <select id="getAverageDealAmount" resultType="java.math.BigDecimal">
        select COALESCE(avg(deal_amount), 0) from `wendao101-order`.employee_teacher_statistics
        <where>
            <if test="employeeIds != null and employeeIds.size() > 0">
                and employee_id in
                <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
            <if test="timeQueryStr != null and timeQueryStr != ''">
                and time_query_str = #{timeQueryStr}
            </if>
        </where>
    </select>

    <!-- 根据老师ID列表和时间查询区间查询数据 -->
    <select id="selectByTeacherIdsAndTimeQueryStr" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        <where>
            <if test="teacherIds != null and teacherIds.size() > 0">
                and teacher_id in
                <foreach item="teacherId" collection="teacherIds" open="(" separator="," close=")">
                    #{teacherId}
                </foreach>
            </if>
            <if test="timeQueryStr != null and timeQueryStr != ''">
                and time_query_str = #{timeQueryStr}
            </if>
        </where>
    </select>

    <insert id="insertEmployeeTeacherStatistics" parameterType="EmployeeTeacherStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.employee_teacher_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statisticsDate != null">statistics_date,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="mobile != null">mobile,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="dealAmount != null">deal_amount,</if>
            <if test="withdrawnAmount != null">withdrawn_amount,</if>
            <if test="moneyInTransit != null">money_in_transit,</if>
            <if test="withdrawableAmount != null">withdrawable_amount,</if>
            <if test="serviceFee != null">service_fee,</if>
            <if test="serviceFeeRate != null">service_fee_rate,</if>
            <if test="timeQueryStr != null">time_query_str,</if>
            <if test="withdrawnAmountFee != null">withdrawn_amount_fee,</if>
            <if test="notWithdrawnFee != null">not_withdrawn_fee,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="leaderId != null">leader_id,</if>
            <if test="leaderNickName != null">leader_nick_name,</if>
            <if test="parentLeaderId != null">parent_leader_id,</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name,</if>
            <if test="orderNum != null">order_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statisticsDate != null">#{statisticsDate},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="dealAmount != null">#{dealAmount},</if>
            <if test="withdrawnAmount != null">#{withdrawnAmount},</if>
            <if test="moneyInTransit != null">#{moneyInTransit},</if>
            <if test="withdrawableAmount != null">#{withdrawableAmount},</if>
            <if test="serviceFee != null">#{serviceFee},</if>
            <if test="serviceFeeRate != null">#{serviceFeeRate},</if>
            <if test="timeQueryStr != null">#{timeQueryStr},</if>
            <if test="withdrawnAmountFee != null">#{withdrawnAmountFee},</if>
            <if test="notWithdrawnFee != null">#{notWithdrawnFee},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="leaderId != null">#{leaderId},</if>
            <if test="leaderNickName != null">#{leaderNickName},</if>
            <if test="parentLeaderId != null">#{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">#{parentLeaderNickName},</if>
            <if test="orderNum != null">#{orderNum},</if>
         </trim>
    </insert>

    <update id="updateEmployeeTeacherStatistics" parameterType="EmployeeTeacherStatistics">
        update `wendao101-order`.employee_teacher_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="statisticsDate != null">statistics_date = #{statisticsDate},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="dealAmount != null">deal_amount = #{dealAmount},</if>
            <if test="withdrawnAmount != null">withdrawn_amount = #{withdrawnAmount},</if>
            <if test="moneyInTransit != null">money_in_transit = #{moneyInTransit},</if>
            <if test="withdrawableAmount != null">withdrawable_amount = #{withdrawableAmount},</if>
            <if test="serviceFee != null">service_fee = #{serviceFee},</if>
            <if test="serviceFeeRate != null">service_fee_rate = #{serviceFeeRate},</if>
            <if test="timeQueryStr != null">time_query_str = #{timeQueryStr},</if>
            <if test="withdrawnAmountFee != null">withdrawn_amount_fee = #{withdrawnAmountFee},</if>
            <if test="notWithdrawnFee != null">not_withdrawn_fee = #{notWithdrawnFee},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="leaderId != null">leader_id = #{leaderId},</if>
            <if test="leaderNickName != null">leader_nick_name = #{leaderNickName},</if>
            <if test="parentLeaderId != null">parent_leader_id = #{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name = #{parentLeaderNickName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeTeacherStatisticsById" parameterType="Long">
        delete from `wendao101-order`.employee_teacher_statistics where id = #{id}
    </delete>

    <delete id="deleteEmployeeTeacherStatisticsByIds" parameterType="String">
        delete from `wendao101-order`.employee_teacher_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 