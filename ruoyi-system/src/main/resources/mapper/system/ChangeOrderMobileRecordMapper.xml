<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ChangeOrderMobileRecordMapper">
    
    <resultMap type="ChangeOrderMobileRecord" id="ChangeOrderMobileRecordResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="oldBuyerUserMobile"    column="old_buyer_user_mobile"    />
        <result property="newBuyerUserMobile"    column="new_buyer_user_mobile"    />
        <result property="operatorMobile"    column="operator_mobile"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectChangeOrderMobileRecordVo">
        select id, order_id, old_buyer_user_mobile, new_buyer_user_mobile, operator_mobile, create_time, update_time from change_order_mobile_record
    </sql>

    <select id="selectChangeOrderMobileRecordList" parameterType="ChangeOrderMobileRecord" resultMap="ChangeOrderMobileRecordResult">
        <include refid="selectChangeOrderMobileRecordVo"/>
        <where>  
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="oldBuyerUserMobile != null  and oldBuyerUserMobile != ''"> and old_buyer_user_mobile = #{oldBuyerUserMobile}</if>
            <if test="newBuyerUserMobile != null  and newBuyerUserMobile != ''"> and new_buyer_user_mobile = #{newBuyerUserMobile}</if>
            <if test="operatorMobile != null  and operatorMobile != ''"> and operator_mobile = #{operatorMobile}</if>
        </where>
    </select>
    
    <select id="selectChangeOrderMobileRecordById" parameterType="Long" resultMap="ChangeOrderMobileRecordResult">
        <include refid="selectChangeOrderMobileRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertChangeOrderMobileRecord" parameterType="ChangeOrderMobileRecord" useGeneratedKeys="true" keyProperty="id">
        insert into change_order_mobile_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="oldBuyerUserMobile != null">old_buyer_user_mobile,</if>
            <if test="newBuyerUserMobile != null">new_buyer_user_mobile,</if>
            <if test="operatorMobile != null">operator_mobile,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="oldBuyerUserMobile != null">#{oldBuyerUserMobile},</if>
            <if test="newBuyerUserMobile != null">#{newBuyerUserMobile},</if>
            <if test="operatorMobile != null">#{operatorMobile},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateChangeOrderMobileRecord" parameterType="ChangeOrderMobileRecord">
        update change_order_mobile_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="oldBuyerUserMobile != null">old_buyer_user_mobile = #{oldBuyerUserMobile},</if>
            <if test="newBuyerUserMobile != null">new_buyer_user_mobile = #{newBuyerUserMobile},</if>
            <if test="operatorMobile != null">operator_mobile = #{operatorMobile},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteChangeOrderMobileRecordById" parameterType="Long">
        delete from change_order_mobile_record where id = #{id}
    </delete>

    <delete id="deleteChangeOrderMobileRecordByIds" parameterType="String">
        delete from change_order_mobile_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>