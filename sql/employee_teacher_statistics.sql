-- 员工客户老师数据统计表
CREATE TABLE `employee_teacher_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `statistics_date` date DEFAULT NULL COMMENT ' 统计日期',
  `teacher_id` bigint(20) DEFAULT NULL COMMENT '老师id',
  `mobile` varchar(20) DEFAULT NULL COMMENT '老师手机号',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '店铺名称',
  `app_name_type` tinyint(1) DEFAULT NULL COMMENT '店铺所属APP，1问到好课，2问到课堂',
  `deal_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金额',
  `withdrawn_amount` decimal(10,2) DEFAULT NULL COMMENT '已提现金额',
  `money_in_transit` decimal(10,2) DEFAULT NULL COMMENT '在途资金',
  `withdrawable_amount` decimal(10,2) DEFAULT NULL COMMENT '可提现金额',
  `service_fee` decimal(10,2) DEFAULT NULL COMMENT '服务费(废弃)',
  `service_fee_rate` decimal(10,2) DEFAULT NULL COMMENT '服务费率(废弃)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `time_query_str` varchar(255) DEFAULT NULL COMMENT '时间查询区间2021-01-01 12:12:12~2024-01-01 12:12:12',
  `withdrawn_amount_fee` decimal(10,2) DEFAULT NULL COMMENT '已提现金额收取的抽佣费(新)',
  `not_withdrawn_fee` decimal(10,2) DEFAULT NULL COMMENT '未提现金额将来要抽的费用(新)',
  `employee_id` bigint(20) DEFAULT NULL COMMENT '老师所属员工id',
  `nick_name` varchar(255) DEFAULT NULL COMMENT '员工昵称姓名',
  `leader_id` bigint(20) DEFAULT NULL COMMENT '直接主管id',
  `leader_nick_name` varchar(255) DEFAULT NULL COMMENT '直接主管昵称姓名',
  `parent_leader_id` bigint(20) DEFAULT '111' COMMENT '上级主管id',
  `parent_leader_nick_name` varchar(255) DEFAULT '吴汪豪' COMMENT '上级主管昵称姓名',
  `order_num` int(10) DEFAULT NULL COMMENT '订单数量',
  PRIMARY KEY (`id`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_shop_name` (`shop_name`),
  KEY `idx_app_name_type` (`app_name_type`),
  KEY `idx_time_query_str` (`time_query_str`),
  KEY `employee_id_idx` (`employee_id`),
  KEY `nick_name_idx` (`nick_name`)
) ENGINE=InnoDB AUTO_INCREMENT=804680 DEFAULT CHARSET=utf8 COMMENT='员工客户老师数据统计表'; 