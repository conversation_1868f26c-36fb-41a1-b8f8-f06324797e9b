package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CourseOrderDTO {

@JsonFormat(pattern = "yy-MM-dd HH:mm:ss")
    private Date orderTime;
    private Integer courseDuration;
    private Integer studyDuration;
    private Long buyerUserId;
    private String orderId;
    private String refundPrice;
}
