package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

@Data
public class TelNumberImportDTO {
    @Excel(name = "手机号码")
    private String telNumber;
    @Excel(name = "备用列不需要填写，占位用")
    //让springboot的json不输出
    @JsonIgnore
    private String placeholder;

    private Boolean isCorrect = Boolean.FALSE;
    private String errorMessage;
}
