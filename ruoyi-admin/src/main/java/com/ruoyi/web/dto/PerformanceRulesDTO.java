package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 奖金池规则对象 performance_rules
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
public class PerformanceRulesDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规则月份
     */
    @Excel(name = "规则月份")
    private String ruleMonth;

    /**
     * 部门名称
     */
    @Excel(name = "部门名称")
    private String deptName;

    /**
     * 类型,1员工,2部门
     */
    @Excel(name = "类型,1员工,2部门")
    private Integer itemType;

    /**
     * 员工列表,多个逗号分隔,如果是部门,所有员工加入
     */
    @Excel(name = "员工列表,多个逗号分隔,如果是部门,所有员工加入")
    private String employees;

    /**
     * 起提公司/部门销售额
     */
    @Excel(name = "起提公司/部门销售额")
    private BigDecimal companyTotalGoal;

    /**
     * 提成
     */
    @Excel(name = "提成")
    private BigDecimal commissions;

    /**
     * 达成条件,大于起提额,个人业绩
     */
    @Excel(name = "达成条件,大于起提额,个人业绩")
    private String conditions;

    /**
     * 起提个人销售额
     */
    @Excel(name = "起提个人销售额")
    private BigDecimal personTotalGoal;

    /**
     * 计算方法
     */
    @Excel(name = "计算方法")
    private String calculationMethod;

    /**
     * 公司整体销售额是否达成,0未达成,1已经达成
     */
    @Excel(name = "公司整体销售额是否达成,0未达成,1已经达成")
    private Integer companyTotalGoalReached;

    /**
     * 个人整体销售额是否达成,0未达成,1已经达成
     */
    @Excel(name = "个人整体销售额是否达成,0未达成,1已经达成")
    private Integer personTotalGoalReached;

    /**
     * 设置的月份总销售额(真实数据,当月的动态计算)
     */
    @Excel(name = "设置的月份总销售额(真实数据,当月的动态计算)")
    private BigDecimal totalSaleMoney;

    /**
     * 付出的总的技术服务费(真实数据,当月的动态计算)
     */
    @Excel(name = " 付出的总的技术服务费(真实数据,当月的动态计算)")
    private BigDecimal totalServiceMoney;

    /**
     * 毛利润,收到老师扣点的钱(真实数据,当月的动态计算)
     */
    @Excel(name = "毛利润,收到老师扣点的钱(真实数据,当月的动态计算)")
    private BigDecimal grossProfit;

    /**
     * 净利润,毛利润-技术服务费(抖音,快手,微信和视频号收取的服务费)(真实数据,当月的动态计算)
     */
    @Excel(name = "净利润,毛利润-技术服务费(抖音,快手,微信和视频号收取的服务费)(真实数据,当月的动态计算)")
    private BigDecimal netProfit;

    /**
     * 绩效金额(total_sale_money-company_total_goal),总销售额-起提销售额
     */
    @Excel(name = "绩效金额(total_sale_money-company_total_goal),总销售额-起提销售额")
    private BigDecimal performanceAmount;

    /**
     * 当月综合抽佣比,默认为10,可设置调整
     */
    @Excel(name = "当月综合抽佣比,默认为10,可设置调整")
    private BigDecimal rakesRate;

    private BigDecimal personTotalSaleMoney;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
