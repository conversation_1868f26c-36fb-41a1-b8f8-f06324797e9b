package com.ruoyi.web.dto;



import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoursePromoterDTO {
    /** 店铺app所属 */
    private Integer appNameType;
    /** 搜索值 */
    private String searchValue;
    /** 是否按照推广员数量排序 0降序 1升序 */
    private Integer isPromoterNum;
    /** 是否按照推广员推广收入排序 0降序 1升序 */
    private Integer isPromoternIcome;

    private Integer pageNum;

    private Integer pageSize;
}
