package com.ruoyi.web.dto;


import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClockInQuestSearchDTO {

    /** 搜索值 */
    @Excel(name = "搜索值")
    private String searchValue;

    private Integer pageNum;

    private Integer pageSize;

    /** 是否按照活动数量排序 0降序 1升序 */
    @Excel(name = "是否按照活动数量排序 0降序 1升序")
    private Integer isSort;

    /** 所属平台，入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开 */
    @Excel(name = "所属平台，入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开")
    private  String platform;
}
