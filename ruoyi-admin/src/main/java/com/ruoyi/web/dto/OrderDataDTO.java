package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class OrderDataDTO {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 平台
     */
    @Excel(name = "平台")
    private String platform;

    /**
     * 搜索值
     */
    @Excel(name = "搜索值")
    private String searchValue;

    /**
     * 最近时间
     */
    @Excel(name = "最近时间")
    private Integer latestTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
