package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DouyinClassDTO {
        private Long id;
        private Long pid;
        private String title;
        private List<DouyinClassDTO> child;
        private List<Integer> needCertIds;
        private Long douyinClassId;
        private String scope;
        private String content;

        public Long getId() {
                return id;
        }

        public void setId(Long id) {
                this.id = id;
        }

        public Long getPid() {
                return pid;
        }

        public void setPid(Long pid) {
                this.pid = pid;
        }

        public String getTitle() {
                return title;
        }

        public void setTitle(String title) {
                this.title = title;
        }

        public List<DouyinClassDTO> getChild() {
                return child;
        }

        public void setChild(List<DouyinClassDTO> child) {
                this.child = child;
        }

        public List<Integer> getNeedCertIds() {
                return needCertIds;
        }

        public void setNeedCertIds(List<Integer> needCertIds) {
                this.needCertIds = needCertIds;
        }

        public Long getDouyinClassId() {
                return douyinClassId;
        }

        public void setDouyinClassId(Long douyinClassId) {
                this.douyinClassId = douyinClassId;
        }

        public String getScope() {
                return scope;
        }

        public void setScope(String scope) {
                this.scope = scope;
        }

        public String getContent() {
                return content;
        }

        public void setContent(String content) {
                this.content = content;
        }
}
