package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class FundIncomeDTO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 老师id
     */
    //@Excel(name = "老师id")
    private Long teacherId;

    /**
     * 推广员id
     */
    //@Excel(name = "推广员id")
    private Long promoterId;

    /**
     * 订单id
     */
    @Excel(name = "订单号")
    private String orderId;

    /**
     * 收入来源 0抖音 1微信 2快速 3视频号
     */
    @Excel(name = "收入来源", readConverterExp = "0=抖音,1=微信,2=快手,3=视频号,5=H5端,7=小红书,8=知识店铺,9=PC端,11=抖店")
    private Integer incomePlatform;

    /**
     * 资金类型 0 直推收入 1 分销收入 2推广收入 3平台提现 4退款 5问到精选收入
     */
    @Excel(name = "资金类型", readConverterExp = "0=直推收入,1=分销收入,2=推广收入,3=平台提现,4=退款")
    private Integer incomeType;

    /**
     * 订单数量
     */
    //@Excel(name = "订单数量")
    private Integer orderNum;

    /**
     * 资金状态 0 在途 1已入账
     */
    @Excel(name = "资金状态", readConverterExp = "0=待结算,1=已结算")
    private Integer fundsType;

    /**
     * 收入金额
     */
    @Excel(name = "收入金额")
    private BigDecimal incomePrice;

    /**
     * 账号金额是否分账 0否 1是
     */
    //@Excel(name = "分账状态",readConverterExp="0=未分账,1=已分账")
    private Integer accountPriceType;

    /**
     * 店铺收益金额
     */
    //@Excel(name = "店铺收益金额")
    private BigDecimal storeIncomePrice;

    /**
     * 店铺名称
     */
    //@Excel(name = "店铺名称")
    private String storeName;

    /**
     * 服务费
     */
    @Excel(name = "服务费")
    private BigDecimal servicePrice;

    /**
     * 服务比率
     */
    @Excel(name = "服务费比率")
    private Integer servicePriceRatio;

    /**
     * 真实收入（扣除服务费）
     */
    @Excel(name = "结算金额")
    private BigDecimal realIncomePrice;

    /**
     * 出版物扣点（默认2%）
     */
    @Excel(name = "出版物扣点")
    private Integer publicationRate;

    /**
     * 出版物扣费金额
     */
    @Excel(name = "出版物扣费金额")
    private BigDecimal publicationFee;

    /**
     * 物流费用扣减金额
     */
    @Excel(name = "物流费用")
    private BigDecimal logisticsFee;

    /**
     * 直播带货佣金比例
     */
    @Excel(name = "带货佣金比例")
    private Integer liveCommerceRate;

    /**
     * 直播带货佣金金额
     */
    @Excel(name = "带货佣金金额")
    private BigDecimal liveCommerceFee;

    /**
     * 订单用户实付金额
     */
    @Excel(name = "订单实付金额")
    private BigDecimal payPrice;

    /**
     * 备注信息
     */
    @Excel(name = "备注信息")
    private String remark;

    /**
     * 订单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /**
     * 入账时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date incomePriceTime;

    /**
     * 是否删除 0否 1是
     */
    //@Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderStartTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderEndTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settlementStartTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settlementEndTime;

    private Long operatorId;
    private String operatorName;
    private String operatorPhone;
}
