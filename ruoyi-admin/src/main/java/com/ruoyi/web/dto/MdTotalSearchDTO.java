package com.ruoyi.web.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MdTotalSearchDTO {

    /** 搜索值 */
    @Excel(name = "搜索值")
    private String searchValue;

    private Integer pageNum;

    private Integer pageSize;

    /** 是否按照推广员数量排序 0降序 1升序 */
    @Excel(name = "是否按照推广员数量排序 0降序 1升序")
    private Integer isSort;

    private Integer appNameType;
}
