package com.ruoyi.web.dto;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.web.domain.Chapter;
import com.ruoyi.web.domain.CourseDirectory;
import com.ruoyi.web.domain.CourseDyAudit;
import com.ruoyi.web.domain.StudyData;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程对象 course
 * 
 * <AUTHOR>
 * @date 2023-08-19
 */
@Data
public class CourseAndWendaoDTO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private List<Long> deleteChapterIdList;
    private List<Long> deleteCourseDirectoryIdList;

    private CourseDyAudit courseDyAudit;

    private List<Chapter> chapterList;
    private List<CourseDirectory> allCourseDirectoryList;
    //private List<CourseDirectory> notInChapterCourseDirectoryList;

    private List<StudyData> studyDataList;

    private String className;

    /** 主键 */
    private Long id;

    /** 课程id，由系统随机生成 */
    @Excel(name = "课程id，由系统随机生成")
    private Long courseIdNumber;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程上传分享标题 */
    @Excel(name = "课程上传分享标题")
    private String courseUploadTitle;

    /** 课程上传分享封面图 */
    @Excel(name = "课程上传分享封面图")
    private String courseUploadUrl;

    /** 课程封面图片url */
    @Excel(name = "课程封面图片url")
    private String coverPicUrl;

    /** 轮播图片url，多个，以逗号分隔 */
    @Excel(name = "轮播图片url，多个，以逗号分隔")
    private String carouselPicUrls;

    /** 序号 */
    @Excel(name = "序号")
    private Integer serialNumber;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String title;

    /** 课程副标题 */
    @Excel(name = "课程副标题")
    private String subTitle;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 划线价 */
    @Excel(name = "划线价")
    private BigDecimal originalPrice;

    /** 课程类型,0单课，1专栏 枚举值 */
    @Excel(name = "课程类型,0单课，1专栏 枚举值")
    private Integer courseType;

    /** 详情上传类型，0图片上传1模板上传 */
    @Excel(name = "详情上传类型，0图片上传1模板上传")
    private Integer detailType;

    /** 详情图片，针对图片上传，多个图片逗号分隔 */
    @Excel(name = "详情图片，针对图片上传，多个图片逗号分隔")
    private String detailPicUrls;

    /** 详情，针对模板上传 */
    @Excel(name = "详情，针对模板上传")
    private String detail;

    /** 跑马灯是否开启，0关闭，1开启 */
    @Excel(name = "跑马灯是否开启，0关闭，1开启")
    private Integer marqueeIsOpen;

    /** 课程推广是否开启 */
    @Excel(name = "课程推广是否开启")
    private Integer spreadIsOpen;

    /** 推广比例 */
    @Excel(name = "推广比例")
    private Long spreadRate;

    /** 有效期，永久999999,30天，60天，90天，180天，360天 */
    @Excel(name = "有效期，永久999999,30天，60天，90天，180天，360天")
    private Long expirationDay;

    /** 发布平台，0抖音 1微信 2快手 3视频号 */
    @Excel(name = "发布平台，0抖音 1微信 2快手 3视频号")
    private String publishPlatform;

    /** 观看平台，0抖音小程序，1微信小程序，2快手小程序，3微信公众号，4电脑端 */
    @Excel(name = "观看平台，0抖音小程序，1微信小程序，2快手小程序，3微信公众号，4电脑端")
    private String watchPlatform;

    /** 虚拟购买轮播是否开启，0关闭，1开启 */
    @Excel(name = "虚拟购买轮播是否开启，0关闭，1开启")
    private Integer visualBuyCarousel;

    /** 是否开启虚拟购买人数 0否 1是 */
    @Excel(name = "是否开启虚拟购买人数 0否 1是")
    private Integer visualLearnNumType;

    /** 虚拟已学习人数 */
    @Excel(name = "虚拟已学习人数")
    private Long visualLearnNum;

    /** 锚点信息 */
    @Excel(name = "锚点信息")
    private String anchorInfo;

    /** 选择的资质 */
    @Excel(name = "选择的资质")
    private String qualification;

    /** 0未提审 1审核中 2审核通过 3审核驳回 4图片上传完成 5视频音频上传完成  */
    @Excel(name = "0未提审 1审核中 2审核通过 3审核驳回 4图片上传完成 5视频音频上传完成")
    private Integer dyAuditStatus;

    /** 问到的审核，针对微信快手，0审核中，1审核通过,2审核驳回 */
    @Excel(name = "问到的审核，针对微信快手，0审核中，1审核通过,2审核驳回")
    private Integer auditStatus;

    /** 课程浏览量 三平台浏览量 用这个字段 */
    @Excel(name = "课程浏览量 三平台浏览量 用这个字段")
    private Long viewsNum;

    /** 评论量 三平台评论量 用这个字段 */
    @Excel(name = "评论量 三平台评论量 用这个字段")
    private Long commentsNum;

    /** 参与活动，多个活动逗号分隔，备用，关联活动表 */
    @Excel(name = "参与活动，多个活动逗号分隔，备用，关联活动表")
    private String participateInActivities;

    /** 抖音二维码 */
    @Excel(name = "抖音二维码")
    private String dyQrCodeUrl;

    /** 快手二维码 */
    @Excel(name = "快手二维码")
    private String ksQrCodeUrl;

    /** 微信二维码 */
    @Excel(name = "微信二维码")
    private String wxQrCodeUrl;

    /** 学员发送邮箱0否 1是 */
    @Excel(name = "学员发送邮箱0否 1是")
    private Integer sendEmail;

    /** 学习后解锁0否 1是 */
    @Excel(name = "学习后解锁0否 1是")
    private Integer studyUnlock;

    /** 添加老师企业微信0否 1是 */
    @Excel(name = "添加老师企业微信0否 1是")
    private Integer increaseTeacherWecom;

    /** 添加老师微信或手机号0否 1是 */
    @Excel(name = "添加老师微信或手机号0否 1是")
    private Integer increaseTeacherWxphone;

    /** 分类id */
    @Excel(name = "分类id")
    private Long classId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseClassName;

    /** 销量 三平台评论量 用这个字段 */
    @Excel(name = "销量 三平台评论量 用这个字段")
    private Long salesVolume;

    /** 课程删除0未删除，1已删除 */
    @Excel(name = "课程删除0未删除，1已删除")
    private Integer isDelete;

    /** 课程状态（0下架状态，1上架状态） */
    @Excel(name = "课程状态", readConverterExp = "0=下架状态，1上架状态")
    private Integer courseOnShelfStatus;

    /** 审核驳回原因 */
    @Excel(name = "审核驳回原因")
    private String auditRejectReason;

    /** 总课时，是几节课 */
    @Excel(name = "总课时，是几节课")
    private Long totalCourseCount;

    /** 评论数 */
    @Excel(name = "评论数")
    private Long commentCount;

    /** 是否是草稿箱  0否 1是 */
    @Excel(name = "是否是草稿箱  0否 1是")
    private Integer courseDraftType;

    /** 浏览数 */
    @Excel(name = "浏览数")
    private Long viewCount;

    /** 快手 0未提审 1审核中 2审核通过 3审核驳回  */
    @Excel(name = "快手 0未提审 1审核中 2审核通过 3审核驳回 ")
    private Integer ksAuditStatus;

    /** 微信 0未提审 1审核中 2审核通过 3审核驳回  */
    @Excel(name = "微信 0未提审 1审核中 2审核通过 3审核驳回 ")
    private Integer wxAuditStatus;

    /** 抖音审核通过的抖音课程id */
    @Excel(name = "抖音审核通过的抖音课程id")
    private String productId;

    /** 课程视频合计时长 */
    @Excel(name = "课程视频合计时长")
    private Integer courseDuration;

    /** 开启添加老师企业微信后添加的二维码url */
    @Excel(name = "开启添加老师企业微信后添加的二维码url")
    private String teacherWecomQrcodeUrl;

    /** 开启添加老师微信或手机号码后填写的内容 */
    @Excel(name = "开启添加老师微信或手机号码后填写的内容")
    private String teacherWxphoneContent;

    /** 类目选择一级id */
    @Excel(name = "类目选择一级id")
    private Long firstClassId;

    /** 类目选择一级pid */
    @Excel(name = "类目选择一级pid")
    private Long firstClassPid;

    /** 类目选择一级名称 */
    @Excel(name = "类目选择一级名称")
    private String firstClassTitle;

    /** 类目选择一级抖音类目id */
    @Excel(name = "类目选择一级抖音类目id")
    private Long firstClassDouyinClassId;

    /** 类目选择二级id */
    @Excel(name = "类目选择二级id")
    private Long secondClassId;

    /** 类目选择二级pid */
    @Excel(name = "类目选择二级pid")
    private Long secondClassPid;

    /** 类目选择二级名称 */
    @Excel(name = "类目选择二级名称")
    private String secondClassTitle;

    /** 类目选择二级抖音类目id */
    @Excel(name = "类目选择二级抖音类目id")
    private Long secondClassDouyinClassId;

    /** 资质id */
    @Excel(name = "资质id")
    private Long certificationId;



    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺头像
     */
    private String avatarUrl;

    /**
     * 店铺账号/手机号
     */
    private String mobile;

    /**
     * 自己后台调用类型   1自己调用
     */
    private Integer type;

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCourseIdNumber(Long courseIdNumber) 
    {
        this.courseIdNumber = courseIdNumber;
    }

    public Long getCourseIdNumber() 
    {
        return courseIdNumber;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setCourseUploadTitle(String courseUploadTitle) 
    {
        this.courseUploadTitle = courseUploadTitle;
    }

    public String getCourseUploadTitle() 
    {
        return courseUploadTitle;
    }
    public void setCourseUploadUrl(String courseUploadUrl) 
    {
        this.courseUploadUrl = courseUploadUrl;
    }

    public String getCourseUploadUrl() 
    {
        return courseUploadUrl;
    }
    public void setCoverPicUrl(String coverPicUrl) 
    {
        this.coverPicUrl = coverPicUrl;
    }

    public String getCoverPicUrl() 
    {
        return coverPicUrl;
    }
    public void setCarouselPicUrls(String carouselPicUrls) 
    {
        this.carouselPicUrls = carouselPicUrls;
    }

    public String getCarouselPicUrls() 
    {
        return carouselPicUrls;
    }
    public void setSerialNumber(Integer serialNumber)
    {
        this.serialNumber = serialNumber;
    }

    public Integer getSerialNumber()
    {
        return serialNumber;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setSubTitle(String subTitle) 
    {
        this.subTitle = subTitle;
    }

    public String getSubTitle() 
    {
        return subTitle;
    }
    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }
    public void setOriginalPrice(BigDecimal originalPrice) 
    {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getOriginalPrice() 
    {
        return originalPrice;
    }
    public void setCourseType(Integer courseType) 
    {
        this.courseType = courseType;
    }

    public Integer getCourseType() 
    {
        return courseType;
    }
    public void setDetailType(Integer detailType) 
    {
        this.detailType = detailType;
    }

    public Integer getDetailType() 
    {
        return detailType;
    }
    public void setDetailPicUrls(String detailPicUrls) 
    {
        this.detailPicUrls = detailPicUrls;
    }

    public String getDetailPicUrls() 
    {
        return detailPicUrls;
    }
    public void setDetail(String detail) 
    {
        this.detail = detail;
    }

    public String getDetail() 
    {
        return detail;
    }
    public void setMarqueeIsOpen(Integer marqueeIsOpen) 
    {
        this.marqueeIsOpen = marqueeIsOpen;
    }

    public Integer getMarqueeIsOpen() 
    {
        return marqueeIsOpen;
    }
    public void setSpreadIsOpen(Integer spreadIsOpen) 
    {
        this.spreadIsOpen = spreadIsOpen;
    }

    public Integer getSpreadIsOpen() 
    {
        return spreadIsOpen;
    }
    public void setSpreadRate(Long spreadRate) 
    {
        this.spreadRate = spreadRate;
    }

    public Long getSpreadRate() 
    {
        return spreadRate;
    }
    public void setExpirationDay(Long expirationDay) 
    {
        this.expirationDay = expirationDay;
    }

    public Long getExpirationDay() 
    {
        return expirationDay;
    }
    public void setPublishPlatform(String publishPlatform) 
    {
        this.publishPlatform = publishPlatform;
    }

    public String getPublishPlatform() 
    {
        return publishPlatform;
    }
    public void setWatchPlatform(String watchPlatform) 
    {
        this.watchPlatform = watchPlatform;
    }

    public String getWatchPlatform() 
    {
        return watchPlatform;
    }
    public void setVisualBuyCarousel(Integer visualBuyCarousel) 
    {
        this.visualBuyCarousel = visualBuyCarousel;
    }

    public Integer getVisualBuyCarousel() 
    {
        return visualBuyCarousel;
    }
    public void setVisualLearnNumType(Integer visualLearnNumType) 
    {
        this.visualLearnNumType = visualLearnNumType;
    }

    public Integer getVisualLearnNumType() 
    {
        return visualLearnNumType;
    }
    public void setVisualLearnNum(Long visualLearnNum) 
    {
        this.visualLearnNum = visualLearnNum;
    }

    public Long getVisualLearnNum() 
    {
        return visualLearnNum;
    }
    public void setAnchorInfo(String anchorInfo) 
    {
        this.anchorInfo = anchorInfo;
    }

    public String getAnchorInfo() 
    {
        return anchorInfo;
    }
    public void setQualification(String qualification) 
    {
        this.qualification = qualification;
    }

    public String getQualification() 
    {
        return qualification;
    }
    public void setDyAuditStatus(Integer dyAuditStatus) 
    {
        this.dyAuditStatus = dyAuditStatus;
    }

    public Integer getDyAuditStatus() 
    {
        return dyAuditStatus;
    }
    public void setAuditStatus(Integer auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Integer getAuditStatus() 
    {
        return auditStatus;
    }
    public void setViewsNum(Long viewsNum) 
    {
        this.viewsNum = viewsNum;
    }

    public Long getViewsNum() 
    {
        return viewsNum;
    }
    public void setCommentsNum(Long commentsNum) 
    {
        this.commentsNum = commentsNum;
    }

    public Long getCommentsNum() 
    {
        return commentsNum;
    }
    public void setParticipateInActivities(String participateInActivities) 
    {
        this.participateInActivities = participateInActivities;
    }

    public String getParticipateInActivities() 
    {
        return participateInActivities;
    }
    public void setDyQrCodeUrl(String dyQrCodeUrl) 
    {
        this.dyQrCodeUrl = dyQrCodeUrl;
    }

    public String getDyQrCodeUrl() 
    {
        return dyQrCodeUrl;
    }
    public void setKsQrCodeUrl(String ksQrCodeUrl) 
    {
        this.ksQrCodeUrl = ksQrCodeUrl;
    }

    public String getKsQrCodeUrl() 
    {
        return ksQrCodeUrl;
    }
    public void setWxQrCodeUrl(String wxQrCodeUrl) 
    {
        this.wxQrCodeUrl = wxQrCodeUrl;
    }

    public String getWxQrCodeUrl() 
    {
        return wxQrCodeUrl;
    }
    public void setSendEmail(Integer sendEmail) 
    {
        this.sendEmail = sendEmail;
    }

    public Integer getSendEmail() 
    {
        return sendEmail;
    }
    public void setStudyUnlock(Integer studyUnlock) 
    {
        this.studyUnlock = studyUnlock;
    }

    public Integer getStudyUnlock() 
    {
        return studyUnlock;
    }
    public void setIncreaseTeacherWecom(Integer increaseTeacherWecom) 
    {
        this.increaseTeacherWecom = increaseTeacherWecom;
    }

    public Integer getIncreaseTeacherWecom() 
    {
        return increaseTeacherWecom;
    }
    public void setIncreaseTeacherWxphone(Integer increaseTeacherWxphone) 
    {
        this.increaseTeacherWxphone = increaseTeacherWxphone;
    }

    public Integer getIncreaseTeacherWxphone() 
    {
        return increaseTeacherWxphone;
    }
    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }
    public void setCourseClassName(String courseClassName) 
    {
        this.courseClassName = courseClassName;
    }

    public String getCourseClassName() 
    {
        return courseClassName;
    }
    public void setSalesVolume(Long salesVolume) 
    {
        this.salesVolume = salesVolume;
    }

    public Long getSalesVolume() 
    {
        return salesVolume;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }
    public void setCourseOnShelfStatus(Integer courseOnShelfStatus) 
    {
        this.courseOnShelfStatus = courseOnShelfStatus;
    }

    public Integer getCourseOnShelfStatus() 
    {
        return courseOnShelfStatus;
    }
    public void setAuditRejectReason(String auditRejectReason) 
    {
        this.auditRejectReason = auditRejectReason;
    }

    public String getAuditRejectReason() 
    {
        return auditRejectReason;
    }
    public void setTotalCourseCount(Long totalCourseCount) 
    {
        this.totalCourseCount = totalCourseCount;
    }

    public Long getTotalCourseCount() 
    {
        return totalCourseCount;
    }
    public void setCommentCount(Long commentCount) 
    {
        this.commentCount = commentCount;
    }

    public Long getCommentCount() 
    {
        return commentCount;
    }
    public void setCourseDraftType(Integer courseDraftType) 
    {
        this.courseDraftType = courseDraftType;
    }

    public Integer getCourseDraftType() 
    {
        return courseDraftType;
    }
    public void setViewCount(Long viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Long getViewCount() 
    {
        return viewCount;
    }
    public void setKsAuditStatus(Integer ksAuditStatus) 
    {
        this.ksAuditStatus = ksAuditStatus;
    }

    public Integer getKsAuditStatus() 
    {
        return ksAuditStatus;
    }
    public void setWxAuditStatus(Integer wxAuditStatus) 
    {
        this.wxAuditStatus = wxAuditStatus;
    }

    public Integer getWxAuditStatus() 
    {
        return wxAuditStatus;
    }
    public void setProductId(String productId) 
    {
        this.productId = productId;
    }

    public String getProductId() 
    {
        return productId;
    }
    public void setCourseDuration(Integer courseDuration)
    {
        this.courseDuration = courseDuration;
    }

    public Integer getCourseDuration()
    {
        return courseDuration;
    }
    public void setTeacherWecomQrcodeUrl(String teacherWecomQrcodeUrl) 
    {
        this.teacherWecomQrcodeUrl = teacherWecomQrcodeUrl;
    }

    public String getTeacherWecomQrcodeUrl() 
    {
        return teacherWecomQrcodeUrl;
    }
    public void setTeacherWxphoneContent(String teacherWxphoneContent) 
    {
        this.teacherWxphoneContent = teacherWxphoneContent;
    }

    public String getTeacherWxphoneContent() 
    {
        return teacherWxphoneContent;
    }
    public void setFirstClassId(Long firstClassId) 
    {
        this.firstClassId = firstClassId;
    }

    public Long getFirstClassId() 
    {
        return firstClassId;
    }
    public void setFirstClassPid(Long firstClassPid) 
    {
        this.firstClassPid = firstClassPid;
    }

    public Long getFirstClassPid() 
    {
        return firstClassPid;
    }
    public void setFirstClassTitle(String firstClassTitle) 
    {
        this.firstClassTitle = firstClassTitle;
    }

    public String getFirstClassTitle() 
    {
        return firstClassTitle;
    }
    public void setFirstClassDouyinClassId(Long firstClassDouyinClassId) 
    {
        this.firstClassDouyinClassId = firstClassDouyinClassId;
    }

    public Long getFirstClassDouyinClassId() 
    {
        return firstClassDouyinClassId;
    }
    public void setSecondClassId(Long secondClassId) 
    {
        this.secondClassId = secondClassId;
    }

    public Long getSecondClassId() 
    {
        return secondClassId;
    }
    public void setSecondClassPid(Long secondClassPid) 
    {
        this.secondClassPid = secondClassPid;
    }

    public Long getSecondClassPid() 
    {
        return secondClassPid;
    }
    public void setSecondClassTitle(String secondClassTitle) 
    {
        this.secondClassTitle = secondClassTitle;
    }

    public String getSecondClassTitle() 
    {
        return secondClassTitle;
    }
    public void setSecondClassDouyinClassId(Long secondClassDouyinClassId) 
    {
        this.secondClassDouyinClassId = secondClassDouyinClassId;
    }

    public Long getSecondClassDouyinClassId() 
    {
        return secondClassDouyinClassId;
    }
    public void setCertificationId(Long certificationId) 
    {
        this.certificationId = certificationId;
    }

    public Long getCertificationId() 
    {
        return certificationId;
    }

    public List<Long> getDeleteChapterIdList() {
        return deleteChapterIdList;
    }

    public void setDeleteChapterIdList(List<Long> deleteChapterIdList) {
        this.deleteChapterIdList = deleteChapterIdList;
    }

    public List<Long> getDeleteCourseDirectoryIdList() {
        return deleteCourseDirectoryIdList;
    }

    public void setDeleteCourseDirectoryIdList(List<Long> deleteCourseDirectoryIdList) {
        this.deleteCourseDirectoryIdList = deleteCourseDirectoryIdList;
    }

    public CourseDyAudit getCourseDyAudit() {
        return courseDyAudit;
    }

    public void setCourseDyAudit(CourseDyAudit courseDyAudit) {
        this.courseDyAudit = courseDyAudit;
    }

    public List<Chapter> getChapterList() {
        return chapterList;
    }

    public void setChapterList(List<Chapter> chapterList) {
        this.chapterList = chapterList;
    }

    public List<CourseDirectory> getAllCourseDirectoryList() {
        return allCourseDirectoryList;
    }

    public void setAllCourseDirectoryList(List<CourseDirectory> allCourseDirectoryList) {
        this.allCourseDirectoryList = allCourseDirectoryList;
    }

    public List<StudyData> getStudyDataList() {
        return studyDataList;
    }

    public void setStudyDataList(List<StudyData> studyDataList) {
        this.studyDataList = studyDataList;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }
}
