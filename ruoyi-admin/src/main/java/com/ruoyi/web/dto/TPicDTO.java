package com.ruoyi.web.dto;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 素材视频对象 t_pic
 * 
 * <AUTHOR>
 * @date 2023-07-27
 */
@Data
public class TPicDTO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 正式使用的路径 */
    @Excel(name = "正式使用的路径")
    private String pathUrl;

    /** 视频文件名称 */
    @Excel(name = "视频文件名称")
    private String fileName;

    /** 视频文件转码后大小单位是Bytes */
    @Excel(name = "视频文件转码后大小单位是Bytes")
    private Long fileAfterTransSize;

    /** 视频文件原始大小单位是Bytes */
    @Excel(name = "视频文件原始大小单位是Bytes")
    private Long fileOriginalSize;

    /** 视频是否删除0未删除，1已删除 */
    @Excel(name = "视频是否删除0未删除，1已删除")
    private Integer isDelete;

    /** 转码状态，0转码中，1转码完毕，-1转码失败 */
    @Excel(name = "转码状态，0转码中，1转码完毕，-1转码失败")
    private Integer transcodeStatus;

    /** 腾讯云点播文件id */
    @Excel(name = "腾讯云点播文件id")
    private String tcvodFileId;

    /** 腾讯云点播视频转码前的地址 */
    @Excel(name = "腾讯云点播视频转码前的地址")
    private String tcvodMediaUrlBeforeTrans;

    /** 云点播执行任务流的错误消息，没有错误则为空 */
    @Excel(name = "云点播执行任务流的错误消息，没有错误则为空")
    private String procedureErrorMsg;

    /** 云点播转码的错误消息，没有错误则为空 */
    @Excel(name = "云点播转码的错误消息，没有错误则为空")
    private String transcodeErrorMsg;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setPathUrl(String pathUrl) 
    {
        this.pathUrl = pathUrl;
    }

    public String getPathUrl() 
    {
        return pathUrl;
    }
    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }
    public void setFileAfterTransSize(Long fileAfterTransSize) 
    {
        this.fileAfterTransSize = fileAfterTransSize;
    }

    public Long getFileAfterTransSize() 
    {
        return fileAfterTransSize;
    }
    public void setFileOriginalSize(Long fileOriginalSize) 
    {
        this.fileOriginalSize = fileOriginalSize;
    }

    public Long getFileOriginalSize() 
    {
        return fileOriginalSize;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }
    public void setTranscodeStatus(Integer transcodeStatus) 
    {
        this.transcodeStatus = transcodeStatus;
    }

    public Integer getTranscodeStatus() 
    {
        return transcodeStatus;
    }
    public void setTcvodFileId(String tcvodFileId) 
    {
        this.tcvodFileId = tcvodFileId;
    }

    public String getTcvodFileId() 
    {
        return tcvodFileId;
    }
    public void setTcvodMediaUrlBeforeTrans(String tcvodMediaUrlBeforeTrans) 
    {
        this.tcvodMediaUrlBeforeTrans = tcvodMediaUrlBeforeTrans;
    }

    public String getTcvodMediaUrlBeforeTrans() 
    {
        return tcvodMediaUrlBeforeTrans;
    }
    public void setProcedureErrorMsg(String procedureErrorMsg) 
    {
        this.procedureErrorMsg = procedureErrorMsg;
    }

    public String getProcedureErrorMsg() 
    {
        return procedureErrorMsg;
    }
    public void setTranscodeErrorMsg(String transcodeErrorMsg) 
    {
        this.transcodeErrorMsg = transcodeErrorMsg;
    }

    public String getTranscodeErrorMsg() 
    {
        return transcodeErrorMsg;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("pathUrl", getPathUrl())
            .append("fileName", getFileName())
            .append("fileAfterTransSize", getFileAfterTransSize())
            .append("fileOriginalSize", getFileOriginalSize())
            .append("isDelete", getIsDelete())
            .append("transcodeStatus", getTranscodeStatus())
            .append("tcvodFileId", getTcvodFileId())
            .append("tcvodMediaUrlBeforeTrans", getTcvodMediaUrlBeforeTrans())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("procedureErrorMsg", getProcedureErrorMsg())
            .append("transcodeErrorMsg", getTranscodeErrorMsg())
            .toString();
    }
}
