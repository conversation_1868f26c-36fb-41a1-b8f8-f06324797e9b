package com.ruoyi.web.dto;


import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

@Data
public class StudyDataDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 学习文件名称 */
    @Excel(name = "学习文件名称")
    private String studyFileName;

    /** 学习文件类型 */
    @Excel(name = "学习文件类型")
    private String studyFileType;

    /** 文件url地址 */
    @Excel(name = "文件url地址")
    private String studyFileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    private Long studyFileSize;

    /** 审核状态 0未审核 1审核 */
    @Excel(name = "审核状态 0未审核 1审核")
    private Integer status;

    /** 是否删除 0否 1是 */
    @Excel(name = "是否删除 0否 1是")
    private Integer delType;

    /** 课程集合 */
    @Excel(name = "课程集合")
    private List<Long> courseIds;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTeacherId(Long teacherId)
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId()
    {
        return teacherId;
    }
    public void setCourseId(Long courseId)
    {
        this.courseId = courseId;
    }

    public Long getCourseId()
    {
        return courseId;
    }
    public void setStudyFileName(String studyFileName)
    {
        this.studyFileName = studyFileName;
    }

    public String getStudyFileName()
    {
        return studyFileName;
    }
    public void setStudyFileType(String studyFileType)
    {
        this.studyFileType = studyFileType;
    }

    public String getStudyFileType()
    {
        return studyFileType;
    }
    public void setStudyFileUrl(String studyFileUrl)
    {
        this.studyFileUrl = studyFileUrl;
    }

    public String getStudyFileUrl()
    {
        return studyFileUrl;
    }
    public void setStudyFileSize(Long studyFileSize)
    {
        this.studyFileSize = studyFileSize;
    }

    public Long getStudyFileSize()
    {
        return studyFileSize;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setDelType(Integer delType)
    {
        this.delType = delType;
    }

    public Integer getDelType()
    {
        return delType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("teacherId", getTeacherId())
                .append("courseId", getCourseId())
                .append("studyFileName", getStudyFileName())
                .append("studyFileType", getStudyFileType())
                .append("studyFileUrl", getStudyFileUrl())
                .append("studyFileSize", getStudyFileSize())
                .append("status", getStatus())
                .append("delType", getDelType())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
