package com.ruoyi.web.controller.system;


import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.WithdrawRecord;
import com.ruoyi.web.vo.WithdrawRecordPromoterVO;
import com.ruoyi.web.vo.WithdrawRecordVO;
import com.ruoyi.web.vo.WithdrawRecordWdVO;
import com.ruoyi.web.vo.WithdrawWdVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

/**
 * 提现记录Controller
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@RestController
@RequestMapping("/withdrawRecord")
public class WithdrawRecordController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;

    /**
     * 获取提现记录详细信息
     */
    @GetMapping(value = "/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id) {
        String uri = "/withdrawRecord/" + id;
        String url = path + uri;
        String s = HttpUtil.get(url);
        return JSONObject.parseObject(s);
    }

    /**
     * 总后台讲师导出提现记录列表
     */
    @PostMapping("/export")
    public JSONObject export(@RequestBody WithdrawRecordWdVO withdrawRecordWdVO) {
        String uri = "/withdrawRecord/export";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecordWdVO);
        String post = HttpUtil.post(url, jsonString);
        return JSONObject.parseObject(post);
    }


    /**
     * 总后台推广员导出提现记录列表
     */
    @PostMapping("/promoterExport")
    public JSONObject promoterExport(@RequestBody WithdrawRecordPromoterVO withdrawRecordVO) {
        String uri = "/withdrawRecord/promoterExport";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecordVO);
        String post = HttpUtil.post(url, jsonString);
        return JSONObject.parseObject(post);
    }


    /**
     * 查询提现记录列表 -- 问到后台
     */
    @PostMapping("/withdraw_list")
    public JSONObject withdrawList(@RequestBody WithdrawRecordWdVO withdrawRecordWdVO) {
        String uri = "/withdrawRecord/withdraw_list";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecordWdVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 提现 -- 问到后台
     */
    @PostMapping("/withdraw")
    public JSONObject withdraw(@RequestBody WithdrawWdVO withdrawWdVO) {
        String uri = "/withdrawRecord/withdraw";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawWdVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 提现 -- 问到后台
     */
    @PostMapping("/testWithdraw")
    public JSONObject testWithdraw(@RequestBody WithdrawWdVO withdrawWdVO) {
        String uri = "/withdrawRecord/testWithdraw";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawWdVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


    //query_account_money

    @PostMapping("/query_account_money")
    public JSONObject queryAccountMoney(@RequestBody WithdrawRecord withdrawRecord) {
        String uri = "/fundIncome/selectTotalIncome";
        if(withdrawRecord.getPromoterId()!=null&&withdrawRecord.getPromoterId()>0L){
            uri = "/fundIncome/selectPromoterTotalIncome";
        }
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecord);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,
                withdrawRecord.getPromoterId()!=null&&withdrawRecord.getPromoterId()>0L?
                        String.valueOf(withdrawRecord.getPromoterId()):
                        String.valueOf(withdrawRecord.getTeacherId()));
    }

    /**
     * 暂不处理 -- 问到后台
     */
    @PostMapping("/pause_processing_withdraw")
    public JSONObject pauseProcessingWithdraw(@RequestBody WithdrawRecord withdrawRecord) {
        String uri = "/withdrawRecord/pause_processing_withdraw";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecord);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


    /**
     * 驳回 -- 问到后台
     */
    @PostMapping("/turn_down_withdraw")
    public JSONObject withdraw(@RequestBody WithdrawRecord withdrawRecord) {

        String uri = "/withdrawRecord/turn_down_withdraw";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecord);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 上传凭证
     */
    @PostMapping("/updateEvidence")
    public JSONObject updateEvidence(@RequestBody WithdrawRecord withdrawRecord) {

        String uri = "/withdrawRecord/updateEvidence";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecord);
        String post = HttpUtil.post(url, jsonString);
        return JSONObject.parseObject(post);
    }



    /**
     * 导出提现记录列表 -- 问到后台
     */
    @PostMapping("/wExport")
    public JSONObject wExport(@RequestBody WithdrawRecordWdVO withdrawRecordWdVO) {
        String uri = "/withdrawRecord/wExport";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecordWdVO);
        String post = HttpUtil.post(url, jsonString);
        return JSONObject.parseObject(post);
    }


    /**
     * 查询推广员提现记录列表 -- 问到后台
     */
    @PostMapping("/getPromoterWithdrawList")
    public JSONObject getPromoterWithdrawList(@RequestBody WithdrawRecordPromoterVO withdrawRecordPromoterVO) {

        String uri = "/withdrawRecord/getPromoterWithdrawList";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecordPromoterVO);

        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);

    }

    /**
     * 获取推广员提现记录详细信息
     */
    @PostMapping(value = "/derivedRecord")
    public JSONObject derivedRecord(@RequestBody WithdrawRecordVO withdrawRecordVO) {

        String uri = "/withdrawRecord/derivedRecord";

        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawRecordVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);

    }

    /**
     * 获取推广员提现记录详细信息
     */
    @GetMapping(value = "/promoter/{id}")
    public JSONObject getPromoterInfo(@PathVariable("id") Long id) {
        String uri = "/withdrawRecord/promoter/" + id;
        String url = path + uri;
        String s = HttpUtil.get(url);
        return JSONObject.parseObject(s);
    }

}
