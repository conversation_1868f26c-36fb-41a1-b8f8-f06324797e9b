package com.ruoyi.web.controller.system;


import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.ClientPasswordConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.tcvod.Signature;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/sign")
public class GetSignatureController extends BaseController {

    @Value("${wendao.video.vodsubAppid}")
    private String vodsubAppid;
    @Value("${wendao.video.secretId}")
    private String secretId;
    @Value("${wendao.video.secretKey}")
    private String secretKey;
    @GetMapping("/pic_sign")
    public AjaxResult picSign() {
        try {
            String signature = Signature.getUploadSignature(new Signature(secretId, secretKey), null, vodsubAppid, null);
            return AjaxResult.success("签名成功", signature);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("获取签名失败");
        }
    }

    @GetMapping("/client_pic_sign")
    @Anonymous
    public AjaxResult clientPicSign(@RequestParam("requestKey")String requestKey) {
        if(!StringUtils.equals(requestKey, ClientPasswordConstants.CLIENT_REQUEST_PASSWORD)){
            return AjaxResult.error("非法请求");
        }
        try {
            String signature = Signature.getUploadSignature(new Signature(secretId, secretKey), null, vodsubAppid, null);
            return AjaxResult.success("签名成功", signature);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("获取签名失败");
        }
    }



}
