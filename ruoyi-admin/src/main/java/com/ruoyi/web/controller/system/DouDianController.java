package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.TTeacher;
import com.ruoyi.system.dto.ChangeOrderMobileDTO;
import com.ruoyi.system.dto.ChangeShopMobileDTO;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.ITTeacherService;
import com.wendao101.system.api.model.LoginUser;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/doudian_controller")
public class DouDianController {

    @Value("${wendao.teacher.path}")
    private String path;

    public final static String SECRET = "qrlwznhlwewrwqtsfurhygjzhq";
    public static final String baseUrl = "https://goodteacher.wendao101.com/prod-api";


    public final static String back_teacher_key = "back_teacher_key:";
    @Autowired
    private ITTeacherService teacherService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 白名单详情
     */
    @GetMapping("/teacher_white_list/getInfo/{id}")
    public JSONObject teacherWhiteList(@PathVariable("id") Long id,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/order/dd_hexiao_whitelist_controller/"+id;
        String token = getTeacherToken(teacherId);
        Map<String,String> map = new HashMap<>();
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }
    /**
     * 核销白名单列表
     * @param map
     * @param request
     * @return
     */
    @GetMapping("/teacher_white_list/list")
    public JSONObject teacherWhiteList(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = "3";
        String url = baseUrl + "/order/dd_hexiao_whitelist_controller/list";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }
    /**
     * 添加白名单
     *
     * @return
     */
    @PostMapping("/teacher_white_list/add")
    public JSONObject teacherWhiteListAdd(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/order/dd_hexiao_whitelist_controller";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }
    /**
     * 修改白名单
     *
     * @return
     */
    @PutMapping("/teacher_white_list/edit")
    public JSONObject teacherWhiteListEdit(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/order/dd_hexiao_whitelist_controller";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.putRequestBodyWithToken(url, jsonString, token);
    }

    /**
     * 删除白名单
     *
     * @return
     */
    @DeleteMapping("/teacher_white_list/del/{ids}")
    public JSONObject teacherWhiteListEdit(@PathVariable String ids) {
        String teacherId = "3";
        String url = baseUrl + "/order/dd_hexiao_whitelist_controller/" + ids;
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.deleteRequestBodyWithToken(url, token);
    }

    /**
     * 总后台手动核销
     * @param changeOrderMobileDTO
     * @return
     */
    @PostMapping("/doudianHexiaoByBackEnd")
    public JSONObject doudianHexiaoByBackEnd(@RequestBody ChangeOrderMobileDTO changeOrderMobileDTO) {
//        ChangeShopMobileDTO changeShopMobileDTO = new ChangeShopMobileDTO();
//        changeShopMobileDTO.setMasterPhoneNumber(changeOrderMobileDTO.getOperatorMobile());
//        changeShopMobileDTO.setCode(changeOrderMobileDTO.getCode());
//        changeShopMobileDTO.setUuid(changeOrderMobileDTO.getUuid());
//        int count = enterInformationService.countSmsRecord(changeShopMobileDTO);
//        if(count<1){
//            AjaxResult result  = AjaxResult.error("验证码错误");
//            return JSON.parseObject(JSON.toJSONString(result));
//        }
        String url = baseUrl + "/order/doudian_hexiao_controller/doudianHexiaoByBackEnd";
        String token = getTeacherToken(String.valueOf(changeOrderMobileDTO.getTeacherId()));
        Map<String, String> map = new HashMap<>();
        map.put("orderId",changeOrderMobileDTO.getOrderId());
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    /**
     * 总后台手动核销
     * @param changeOrderMobileDTO
     * @return
     */
    @PostMapping("/doudianFanHexiaoByBackEnd")
    public JSONObject doudianFanHexiaoByBackEnd(@RequestBody ChangeOrderMobileDTO changeOrderMobileDTO) {
        String url = baseUrl + "/order/doudian_hexiao_controller/doudianFanHexiaoByBackEnd";
        String token = getTeacherToken(String.valueOf(changeOrderMobileDTO.getTeacherId()));
        Map<String, String> map = new HashMap<>();
        map.put("orderId",changeOrderMobileDTO.getOrderId());
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }


    /**
     * 获取免登录链接token
     * @param teacherId
     * @return
     */
    @GetMapping("/getNoLoginJumpToTeacherBackendToken")
    public AjaxResult queryCourseAuditPaged(@RequestParam("teacherId") Long teacherId) {
        if(teacherId<=0L){
            return AjaxResult.warn("teacherId参数错误！");
        }
        TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(teacherId);
        String token = getTeacherToken(String.valueOf(teacherId));
        Map<String,Object> map = new HashMap<>();
        map.put("token",token);
        map.put("teacherId",teacherId);
        map.put("appNameType",tTeacher.getAppNameType());
        return AjaxResult.success("免登录token获取成功！",map);
    }


     /**
     * 分页查询查询课程列表
     * 需要合并审核通过的表course_dy
     */
     @GetMapping("/query_course_audit_paged")
     public JSONObject queryCourseAuditPaged(@RequestParam Map<String, String> map, HttpServletRequest request) {
         String teacherId = request.getHeader("Teacherid");
         String url = baseUrl + "/teacher/course_controller/query_course_audit_paged";
         String token = getTeacherToken(teacherId);
         String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
         return JSON.parseObject(s);
     }

    @PostMapping("/addMultipleSellSpecs")
    public JSONObject addMultipleSellSpecs(@RequestBody JSONObject param1, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/addMultipleSellSpecs";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }
    /**
     * 查询所有抖店配置列表
     */
    @GetMapping("/queryAllDoudian")
    public JSONObject queryAllDoudian(HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/queryAllDoudian";
        String token = getTeacherToken(teacherId);
        Map<String, String> map = new HashMap<>();
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    /**
     * 删除商品
     *
     * @return
     */
    @GetMapping("/delProduct")
    public JSONObject delProduct(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/delProduct";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }


    @GetMapping("/queryTeacherDoudianDetail")
    public JSONObject queryTeacherDoudianDetail(HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/queryTeacherDoudianDetail";
        String token = getTeacherToken(teacherId);
        Map<String, String> map = new HashMap<>();
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/list";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    @GetMapping("/listFromBackend")
    public JSONObject listFromBackend(@RequestParam Map<String, String> map) {
        String url = path + "/doudian_controller/listFromBackend";
        return JSON.parseObject(HttpClientPostFormUtil.sendGetMap(url, map, null));
    }


    //channelIsOk
    @GetMapping("/channelIsOk")
    public JSONObject channelIsOk(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/channelIsOk";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    /**
     * 获取抖店课程详细信息
     */
    @GetMapping(value = "/detail/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/"+id;
        String token = getTeacherToken(teacherId);
        Map<String, String> map = new HashMap<>();
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    @GetMapping("/getCateProperty")
    public JSONObject getCateProperty(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/getCateProperty";
         String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    @PostMapping("/editPrice")
    public JSONObject editPrice(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/editPrice";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }

    @GetMapping("/launchProduct")
    public JSONObject launchProduct(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/launchProduct";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    @GetMapping("/setOffline")
    public JSONObject setOffline(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/setOffline";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    /**
     * 编辑商品，20241119新增,此为免审接口
     *
     * @param param1
     * @return
     */
    @PostMapping("/editProductNoAudit")
    public JSONObject editProductNoAudit(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/editProductNoAudit";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }

    /**
     * 总后台编辑商品，审核过程中
     *
     * @param param1
     * @return
     */
    @PostMapping("/editProduct")
    public JSONObject editProduct(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/editProduct";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }

    /**
     * 添加商品，20241119新增,此为免审接口
     *
     * @return
     */
    @PostMapping("/addProductNoAudit")
    public JSONObject addProductNoAudit(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/addProductNoAudit";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }


    @PostMapping("/auditProductFromBackend")
    public JSONObject channelIsOk(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/auditProductFromBackend";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }
    /**
     * 设置可提交的一级分类!
     *
     * @return
     */
    @GetMapping("/query_can_submit_cat_new")
    public JSONObject query_can_submit_cat_new(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/query_can_submit_cat_new";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    /**
     * 查询分类的售后政策，必须是叶子结点
     */
    @GetMapping("/queryCategoryAfterSaleRule")
    public JSONObject queryCategoryAfterSaleRule(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/queryCategoryAfterSaleRule";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    /**
     * 查询分类
     * 传0查询顶级分类,传
     */
    @GetMapping("/listCategory")
    public JSONObject listCategory(@RequestParam Map<String, String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/listCategory";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    /**
     * 上传素材
     *
     * @param param1
     * @return
     */
    @PostMapping("/uploadImageSync")
    public JSONObject uploadImageSync(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/uploadImageSync";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }

    /**
     * 上传素材
     *
     * @param param1
     * @return
     */
    @PostMapping("/queryMaterialDetail")
    public JSONObject queryMaterialDetail(@RequestBody JSONObject param1,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = baseUrl + "/teacher/doudian_controller/queryMaterialDetail";
        String jsonString = JSON.toJSONString(param1);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(url, jsonString, token);
    }


    private String getTeacherToken(String teacherId) {
        TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(Long.valueOf(teacherId));
        String token = redisCache.getCacheObject(back_teacher_key + teacherId);
        if (StringUtils.isBlank(token)) {
            token = createToken(tTeacher.getTeacherId(), tTeacher.getMobile());
            redisCache.setCacheObject(back_teacher_key + teacherId, token, 23, TimeUnit.HOURS);
        }
        return token;
    }


    public String createToken(Long userId, String userName) {
        LoginUser loginUser = new LoginUser();
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        loginUser.setUserid(userId);
        loginUser.setUsername(userName);
        refreshToken(loginUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<>();
        claimsMap.put("user_key", token);
        claimsMap.put("user_id", userId);
        claimsMap.put("username", userName);

        return createToken(claimsMap);
    }

    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + 24L * 3600L * 1000L);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisCache.setCacheObject(userKey, loginUser, 24, TimeUnit.HOURS);
    }

    private String getTokenKey(String token) {
        return "login_tokens:" + token;
    }

    public static String createToken(Map<String, Object> claims) {
        return Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, SECRET).compact();
    }
}
