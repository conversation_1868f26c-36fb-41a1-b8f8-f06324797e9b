package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/teacher")
public class GetSignatureWendaoController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    @GetMapping("/sign")
    public JSONObject sign(HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/teacher/sign";
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }

    @GetMapping("/audio_sign_no_transcode")
    public JSONObject audioSignNoTranscode(HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/teacher/audio_sign_no_transcode";
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }

    @GetMapping("/audio_sign_need_transcode")
    public JSONObject audioSignNeedTranscode(HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/teacher/audio_sign_need_transcode";
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }

    @GetMapping("/pic_sign")
    public JSONObject picSign(HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/teacher/pic_sign";
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }

    //获取cos临时授权
    @GetMapping("/cos_credential")
    public JSONObject cosSign(HttpServletRequest request){
        String teacherId = request.getHeader("Teacherid");
        String uri = "/teacher/cos_credential";
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }

}
