package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.PerformanceRulesDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * 奖金池规则Controller
 * 
 * <AUTHOR>
 * @date 2024-04-01
 */
@RestController
@RequestMapping("/performance_rules")
public class PerformanceRulesController extends BaseController
{
    @Value("${wendao.teacher.path}")
    private String path;
    /**
     * 查询奖金池规则列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map)
    {
        String url = path + "/performance_rules/list";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, null);
        return JSON.parseObject(s);
    }

    //performance_rules/poolDeptList
    @GetMapping("/poolDeptList")
    public JSONObject poolDeptList(@RequestParam Map<String,String> map)
    {
        String url = path + "/performance_rules/poolDeptList";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, null);
        return JSON.parseObject(s);
    }

    //performance_rules/poolDeptEmpList
    @GetMapping("/poolDeptEmpList")
    public JSONObject poolDeptEmpList(@RequestParam Map<String,String> map)
    {
        String url = path + "/performance_rules/poolDeptEmpList";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, null);
        return JSON.parseObject(s);
    }

    /**
     * 获取奖金池规则详细信息
     */
    @GetMapping(value = "/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id)
    {
        String url = path + "/performance_rules/"+id;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,-1L));
    }


    /**
     * 获取杭州总公司下面的部门列表
     * @return
     */
    @GetMapping("/getDeptList")
    public JSONObject getDeptList()
    {
        String url = path + "/performance_rules/getDeptList";
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,-1L));
    }

    @GetMapping("/getDeptEmpList")
    public JSONObject getDeptEmpList(@RequestParam Map<String,String> map)
    {
        String url = path + "/performance_rules/getDeptEmpList";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, null);
        return JSON.parseObject(s);
    }

    /**
     * 新增奖金池规则
     */
    @PostMapping
    public JSONObject add(@RequestBody PerformanceRulesDTO performanceRulesDTO)
    {
        String url = path + "/performance_rules";
        String jsonString = JSON.toJSONString(performanceRulesDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 修改奖金池规则
     */
    @PutMapping
    public JSONObject edit(@RequestBody PerformanceRulesDTO performanceRulesDTO)
    {
        String url = path + "/performance_rules";
        String jsonString = JSON.toJSONString(performanceRulesDTO);
        return HttpClientPostFormUtil.putRequestBody(url, jsonString, null);
    }

    /**
     * 删除奖金池规则
     */
	@DeleteMapping("/{ids}")
    public JSONObject remove(@PathVariable String ids)
    {
        String url = path + "/performance_rules/"+ ids;
        return HttpClientPostFormUtil.deleteRequestBody(url,"-1");
    }
}
