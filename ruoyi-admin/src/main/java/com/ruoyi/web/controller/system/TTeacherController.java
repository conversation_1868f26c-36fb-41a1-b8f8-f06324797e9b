package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.TTeacher;
import com.ruoyi.system.service.ITTeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 老师Controller
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/system/teacher")
public class TTeacherController extends BaseController
{
    @Autowired
    private ITTeacherService tTeacherService;
    @Value("${wendao.order.path}")
    private String path;

    /**
     * 查询按时间区间的任务列表
     */
    @GetMapping("/saleStatistics/queryTaskList")
    public JSONObject queryTaskList(@RequestParam Map<String, String> map) {
        String url = path + "/teacher/saleStatistics/queryTaskList";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, 3L);
        return JSON.parseObject(s);
    }

    /**
     * 查询按时间区间的员工任务列表
     */
    @GetMapping("/saleStatistics/queryEmployeeTaskList")
    public JSONObject queryEmployeeTaskList(@RequestParam Map<String, String> map) {
        String url = path + "/teacher/saleStatistics/queryEmployeeTaskList";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, 3L);
        return JSON.parseObject(s);
    }

    /**
     * 查询员工的老师销售数据,提现数据等
     */
    @GetMapping("/saleStatistics/employeeList")
    public JSONObject employeeList(@RequestParam Map<String, String> map) {
        String url = path + "/teacher/saleStatistics/employeeList";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, 3L);
        return JSON.parseObject(s);
    }

    /**
     * 查询老师销售数据,提现数据等
     */
    @GetMapping("/saleStatistics/list")
    public JSONObject statisticsList(@RequestParam Map<String, String> map) {
        String url = path + "/teacher/saleStatistics/list";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, 3L);
        return JSON.parseObject(s);
    }

    @PostMapping("/saleStatistics/export")
    public JSONObject statisticsList(@RequestBody JSONObject jsonObject) {
        String url = path + "/teacher/saleStatistics/export";
        String jsonString = JSON.toJSONString(jsonObject);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, "3");
    }

    /**
     * 查询老师销售数据,提现数据等
     */
    @GetMapping("/saleStatistics/withdrawRecordList")
    public JSONObject withdrawRecordList(@RequestParam Map<String, String> map) {
        String url = path + "/teacher/saleStatistics/withdrawRecordList";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, 3L);
        return JSON.parseObject(s);
    }

    /**
     * 查询老师列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TTeacher tTeacher)
    {
        startPage();
        List<TTeacher> list = tTeacherService.selectTTeacherList(tTeacher);
        return getDataTable(list);
    }

    /**
     * 导出老师列表
     */
    @Log(title = "老师", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TTeacher tTeacher)
    {
        List<TTeacher> list = tTeacherService.selectTTeacherList(tTeacher);
        ExcelUtil<TTeacher> util = new ExcelUtil<TTeacher>(TTeacher.class);
        util.exportExcel(response, list, "老师数据");
    }

    /**
     * 获取老师详细信息
     */
    @GetMapping(value = "/{teacherId}")
    public AjaxResult getInfo(@PathVariable("teacherId") Long teacherId)
    {
        return success(tTeacherService.selectTTeacherByTeacherId(teacherId));
    }

    /**
     * 新增老师
     */
    @Log(title = "老师", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TTeacher tTeacher)
    {
        return toAjax(tTeacherService.insertTTeacher(tTeacher));
    }

    /**
     * 修改老师
     */
    @Log(title = "老师", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TTeacher tTeacher)
    {
        return toAjax(tTeacherService.updateTTeacher(tTeacher));
    }

    /**
     * 删除老师
     */
    @Log(title = "老师", businessType = BusinessType.DELETE)
	@DeleteMapping("/{teacherIds}")
    public AjaxResult remove(@PathVariable Long[] teacherIds)
    {
        return toAjax(tTeacherService.deleteTTeacherByTeacherIds(teacherIds));
    }

}
