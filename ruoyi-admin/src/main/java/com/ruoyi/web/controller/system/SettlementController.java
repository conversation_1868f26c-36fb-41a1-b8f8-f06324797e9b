package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.FundIncomeDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/order_settlement")
public class SettlementController {
    @Value("${wendao.order.path}")
    private String path;

    /**
     * 查询结算列表
     * 从2025年5月1日0点开始计算
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String, String> map) {
        String url = path + "/order_settlement/listFromBackend20250506";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, null);
        return JSON.parseObject(s);
    }

    /**
     * 导出结算列表
     * 从2025年5月1日0点开始计算
     */
    @PostMapping("/export")
    public JSONObject export(@RequestBody FundIncomeDTO fundIncomeDTO, HttpServletRequest request) {
        String url = path + "/order_settlement/exportFromBackend20250506";
        String jsonString = JSON.toJSONString(fundIncomeDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 按订单id查询订单详情
     * @param map
     * @return
     */
    @GetMapping("/getOrderByOrderId")
    public JSONObject getOrderByOrderId(@RequestParam Map<String, String> map) {
        String url = path + "/order_settlement/getOrderByOrderIdFromBackend20250506";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, null);
        return JSON.parseObject(s);
    }
}
