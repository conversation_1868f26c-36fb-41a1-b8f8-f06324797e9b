package com.ruoyi.web.controller.system;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.dto.DyMiniClassDTO;
import com.ruoyi.system.domain.vo.EnterInformationVO;
import com.ruoyi.system.domain.vo.TeacherEnterStatusVO;
import com.ruoyi.system.domain.vo.TeacherEnterVO;
import com.ruoyi.system.dto.ChangeShopMobileDTO;
import com.ruoyi.system.dto.PasswordInfoDTO;
import com.ruoyi.system.dto.SendSmsForChangeShopMobileDTO;
import com.ruoyi.system.dto.TeacherDTO;
import com.ruoyi.system.service.*;
import com.ruoyi.web.dto.DouyinClassDTO;
import com.ruoyi.web.vo.CourseOrderVO;
import com.ruoyi.web.vo.SendSmsVO;
import com.ruoyi.web.vo.SmsOrderVO;
import com.ruoyi.web.vo.ValidateSmsVO;
import com.wendao101.system.api.model.LoginUser;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 入驻信息Controller
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@RestController
@RequestMapping("/system/enter_information")
public class EnterInformationController extends BaseController {
    @Autowired
    private IEnterInformationService enterInformationService;
    @Autowired
    private IEnterInformationBackupService enterInformationBackupService;
    @Autowired
    private IDouyinClassService douyinClassService;
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IWendaoTcplayerLicenseService wendaoTcplayerLicenseService;

    @Autowired
    private IPublicWendaoCertInfoService publicWendaoCertInfoService;

    @Autowired
    private SmsService smsService;

    @Value("${wendao.order.path}")
    private String path;

    @Value("${wendao.teacher.path}")
    private String teacherPath;

    @Autowired
    private ITTeacherService teacherService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 新增代运营服务商帮老师或代运营模式机构入驻
     */
    @PostMapping("/query_class_info")
    @Anonymous
    public AjaxResult queryClassInfo() {
        List<DouyinClass> douyinClassList = douyinClassService.selectDouyinClassList(null);
        Map<Long, DouyinClassDTO> map = new LinkedHashMap<>();
        for (DouyinClass dyClass : douyinClassList) {
            if (dyClass.getPid().intValue() == 0) {
                DouyinClassDTO dto = new DouyinClassDTO();
                dto.setId(dyClass.getId());
                dto.setPid(dyClass.getPid());
                dto.setTitle(dyClass.getTitle());
                dto.setDouyinClassId(dyClass.getDouyinClassId());
                dto.setScope(dyClass.getScope());
                dto.setContent(dyClass.getContent());
                ArrayList<DouyinClassDTO> objects = new ArrayList<>();
                dto.setChild(objects);
                map.put(dyClass.getId(), dto);
            }
        }
        for (DouyinClass dyClass : douyinClassList) {
            if (dyClass.getPid().intValue() > 0) {
                Long pid = dyClass.getPid();
                DouyinClassDTO douyinClassDTO = map.get(pid);
                List<DouyinClassDTO> child = douyinClassDTO.getChild();
                DouyinClassDTO dto = new DouyinClassDTO();
                dto.setId(dyClass.getId());
                dto.setPid(pid);
                dto.setTitle(dyClass.getTitle());
                dto.setDouyinClassId(dyClass.getDouyinClassId());
                dto.setScope(dyClass.getScope());
                dto.setContent(dyClass.getContent());
                List<Integer> intArr = JSONArray.parseArray(dyClass.getNeedCertIds(), Integer.class);
                dto.setNeedCertIds(intArr);
                child.add(dto);
            }
        }
        return AjaxResult.success(map.values());
    }

    /**
     * 查询 入驻信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EnterInformation enterInformation) {
        enterInformation.setAuditType(0);
        //等于3是查问到课堂和问到好课的所有数据
        if(enterInformation.getAppNameType()!=null&&enterInformation.getAppNameType()==3){
            enterInformation.setAppNameType(null);
        }
        startPage();
        List<EnterInformation> list = enterInformationService.selectEnterInformationList(enterInformation);
        return getDataTable(list);
    }

    @GetMapping("/modify_list")
    public TableDataInfo modify_list(EnterInformationBackup enterInformation) {
        startPage();
        List<EnterInformationBackup> list = enterInformationBackupService.selectEnterInformationBackupList(enterInformation);
        return getDataTable(list);
    }


    @Anonymous
    @PostMapping("/order/selectAll")
    public JSONObject orderSelectAll(@RequestBody CourseOrderVO courseOrderVO) {
        EnterInformation enterInformation = new EnterInformation();
        enterInformation.setDeptId(courseOrderVO.getDeptId());
        enterInformation.setUserId(courseOrderVO.getUserId());
        List<EnterInformation> enterInformations = enterInformationService.selectEnterInformationList(enterInformation);
        //去除无shopId或shopId<=0L的数据
        List<EnterInformation> elist = enterInformations.stream().filter(ei -> ei.getShopId() != null && ei.getShopId() > 0L).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(elist)){
            String empty = "{\n" +
                    "    \"total\": 0,\n" +
                    "    \"list\": [\n" +
                    "        {\n" +
                    "            \"id\": null,\n" +
                    "            \"orderId\": null,\n" +
                    "            \"teacherId\": null,\n" +
                    "            \"buyerUserId\": null,\n" +
                    "            \"buyerUserName\": null,\n" +
                    "            \"buyerUserImg\": null,\n" +
                    "            \"buyerUserMobile\": null,\n" +
                    "            \"courseDuration\": null,\n" +
                    "            \"studyDuration\": null,\n" +
                    "            \"validity\": null,\n" +
                    "            \"promotionRatio\": null,\n" +
                    "            \"promoterId\": null,\n" +
                    "            \"isPromoter\": null,\n" +
                    "            \"promoterName\": null,\n" +
                    "            \"promoterMobile\": null,\n" +
                    "            \"myEarningsPrice\": null,\n" +
                    "            \"promoterEarningsPrice\": null,\n" +
                    "            \"courseId\": null,\n" +
                    "            \"courseTitle\": null,\n" +
                    "            \"coursePrice\": null,\n" +
                    "            \"isCourse\": null,\n" +
                    "            \"originalPrice\": null,\n" +
                    "            \"orderStatus\": null,\n" +
                    "            \"orderType\": null,\n" +
                    "            \"orderPlatform\": null,\n" +
                    "            \"orderTime\": null,\n" +
                    "            \"refundTime\": null,\n" +
                    "            \"fundsType\": null,\n" +
                    "            \"payWay\": null,\n" +
                    "            \"payTime\": null,\n" +
                    "            \"outOrderNumber\": null,\n" +
                    "            \"tradingOrderNumber\": null,\n" +
                    "            \"deliverGoodsOrderId\": null,\n" +
                    "            \"consigneeMobile\": null,\n" +
                    "            \"consigneeName\": null,\n" +
                    "            \"consigneeAddr\": null,\n" +
                    "            \"courseImgUrl\": null,\n" +
                    "            \"beginTime\": null,\n" +
                    "            \"endTime\": null,\n" +
                    "            \"amountPaidNum\": 0,\n" +
                    "            \"unpaidAmountNum\": 0,\n" +
                    "            \"refundedNum\": 0,\n" +
                    "            \"refusalRefundedNum\": 0,\n" +
                    "            \"cancelNum\": 0,\n" +
                    "            \"complaintNum\": 0,\n" +
                    "            \"orderRefundReviewNum\": 0,\n" +
                    "            \"orderRefundTouShuNum\": 0,\n" +
                    "            \"payPrice\": null,\n" +
                    "            \"shopName\": null,\n" +
                    "            \"mobile\": null,\n" +
                    "            \"createTime\": null,\n" +
                    "            \"updateTime\": null,\n" +
                    "            \"appNameType\": null,\n" +
                    "            \"settleStatus\": null,\n" +
                    "            \"secondKill\": false,\n" +
                    "            \"courseIdNumber\": null,\n" +
                    "            \"ddShopId\": null,\n" +
                    "            \"ddSmsStatus\": null,\n" +
                    "            \"ddSmsId\": null,\n" +
                    "            \"ddHexiaoStatus\": null,\n" +
                    "            \"kankeUrl\": null\n" +
                    "        }\n" +
                    "    ],\n" +
                    "    \"pageNum\": 1,\n" +
                    "    \"pageSize\": 10,\n" +
                    "    \"size\": 1,\n" +
                    "    \"startRow\": 1,\n" +
                    "    \"endRow\": 1,\n" +
                    "    \"pages\": 0,\n" +
                    "    \"prePage\": 0,\n" +
                    "    \"nextPage\": 0,\n" +
                    "    \"isFirstPage\": true,\n" +
                    "    \"isLastPage\": true,\n" +
                    "    \"hasPreviousPage\": false,\n" +
                    "    \"hasNextPage\": false,\n" +
                    "    \"navigatePages\": 8,\n" +
                    "    \"navigatepageNums\": [],\n" +
                    "    \"navigateFirstPage\": 0,\n" +
                    "    \"navigateLastPage\": 0,\n" +
                    "    \"totalActualAmount\": 0\n" +
                    "}";
            return JSON.parseObject(empty);
        }
        List<Long> teacherIds = new ArrayList<>();
        for(EnterInformation ei:elist){
            if(ei.getShopId()!=null&&ei.getShopId()>0L){
                teacherIds.add(ei.getShopId());
            }
        }
        courseOrderVO.setTeacherIds(teacherIds);

        if (Objects.nonNull(courseOrderVO.getTeacherId())) {
            Long teacherId = courseOrderVO.getTeacherId();
            String uri = "/course_order/selectAll";
            String url = path + uri;
            String jsonString = JSON.toJSONString(courseOrderVO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
        }else {
            String uri = "/course_order/selectAll";
            String url = path + uri;
            String jsonString = JSON.toJSONString(courseOrderVO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, "0");
        }
    }

    /**
     * 获取代理商免登老师token
     */
    @GetMapping("/client_login_teacher_token")
    @Anonymous
    public AjaxResult clientLoginTeacherToken(@RequestParam("shopId") Long shopId, @RequestParam("userId") Long userId,@RequestParam("deptId") Long deptId) {
        if(shopId <=0L){
            return AjaxResult.error("shopId参数错误！");
        }
        if(userId<=0L){
            return AjaxResult.error("userId参数错误！");
        }
        EnterInformation enterInformation = new EnterInformation();
        enterInformation.setDeptId(deptId);
        enterInformation.setShopId(shopId);
        enterInformation.setUserId(userId);
        List<EnterInformation> enterInformations = enterInformationService.selectEnterInformationList(enterInformation);
        if(CollectionUtils.isEmpty(enterInformations)){
            return AjaxResult.error("没有找到店铺或无权限获取!");
        }
        TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(shopId);
        String token = getTeacherToken(String.valueOf(shopId));
        Map<String,Object> map = new HashMap<>();
        map.put("token",token);
        map.put("teacherId", shopId);
        map.put("appNameType",tTeacher.getAppNameType());
        return AjaxResult.success("免登录token获取成功！",map);
    }
    private String getTeacherToken(String teacherId) {
        TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(Long.valueOf(teacherId));
        String token = redisCache.getCacheObject(DouDianController.back_teacher_key + teacherId);
        if (org.apache.commons.lang3.StringUtils.isBlank(token)) {
            token = createToken(tTeacher.getTeacherId(), tTeacher.getMobile());
            redisCache.setCacheObject(DouDianController.back_teacher_key + teacherId, token, 23, TimeUnit.HOURS);
        }
        return token;
    }


    public String createToken(Long userId, String userName) {
        LoginUser loginUser = new LoginUser();
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        loginUser.setUserid(userId);
        loginUser.setUsername(userName);
        refreshToken(loginUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<>();
        claimsMap.put("user_key", token);
        claimsMap.put("user_id", userId);
        claimsMap.put("username", userName);

        return createToken(claimsMap);
    }

    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + 24L * 3600L * 1000L);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisCache.setCacheObject(userKey, loginUser, 24, TimeUnit.HOURS);
    }

    private String getTokenKey(String token) {
        return "login_tokens:" + token;
    }

    public static String createToken(Map<String, Object> claims) {
        return Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, DouDianController.SECRET).compact();
    }

    /**
     * 查询 入驻信息列表
     */
    @GetMapping("/client_enter_info_list")
    @Anonymous
    public TableDataInfo clientEnterInfoList(EnterInformation enterInformation) {
        //enterInformation.setAuditType(0);
        //等于3是查问到课堂和问到好课的所有数据
        if(enterInformation.getAppNameType()!=null&&enterInformation.getAppNameType()==3){
            enterInformation.setAppNameType(null);
        }
        startPage();
        List<EnterInformation> list = enterInformationService.selectEnterInformationList(enterInformation);
        return getDataTable(list);
    }

    /**
     * 导出 入驻信息列表
     */
    @Log(title = " 入驻信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EnterInformation enterInformation) {
        List<EnterInformation> list = enterInformationService.selectEnterInformationList(enterInformation);
        ExcelUtil<EnterInformation> util = new ExcelUtil<EnterInformation>(EnterInformation.class);
        util.exportExcel(response, list, " 入驻信息数据");
    }

    /**
     * 获取 入驻信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(enterInformationService.selectEnterInformationById(id));
    }

    /**
     * 新增 入驻信息
     */
    @Log(title = " 入驻信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EnterInformation enterInformation) {
        enterInformation.setRateType(2);
        String realName = enterInformation.getRealName();
        if (StringUtils.isBlank(enterInformation.getTeacherName())) {
            enterInformation.setTeacherName(realName);
        }
        if (enterInformation.getEntityType() == 1) {
            if (StringUtils.isBlank(enterInformation.getIdNumber())) {
                //身份证号码错误
                return AjaxResult.error("身份证号码为空");
            }
            enterInformation.setIdNumber(enterInformation.getIdNumber().trim());
            if (enterInformation.getIdNumber().length() != 18 && enterInformation.getIdNumber().length() != 15) {
                return AjaxResult.error("身份证号码长度错误,请填写15位或18位身份证号码!");
            }

            boolean validCard = false;
            if (enterInformation.getIdNumber().length() == 18) {
                validCard = IdcardUtil.isValidCard18(enterInformation.getIdNumber());
            } else {
                validCard = IdcardUtil.isValidCard15(enterInformation.getIdNumber());
            }
            if (!validCard) {
                return AjaxResult.error("身份证号码填写错误,合法性校验失败,请重新核对身份证号码!");
            }
        }

        EnterInformation enterInformation1 = new EnterInformation();
        enterInformation1.setTelNum(enterInformation.getTelNum());
        enterInformation1.setAppNameType(enterInformation.getAppNameType());
        List<EnterInformation> enterInformations = enterInformationService.getEnterInformationList(enterInformation1);
        if (CollectionUtils.isNotEmpty(enterInformations)) {
            return AjaxResult.error("手机号码已存在!请检查您的输入.");
        }
        if (enterInformation.getEntityType() == 1) {
            if (StringUtils.isNotEmpty(enterInformation.getIdNumber())) {
                EnterInformation enterInformation2 = new EnterInformation();
                enterInformation2.setIdNumber(enterInformation.getIdNumber());
                enterInformation2.setAppNameType(enterInformation.getAppNameType());
                List<EnterInformation> enterInformationList = enterInformationService.getEnterInformationList(enterInformation2);
                if (CollectionUtils.isNotEmpty(enterInformationList)) {
                    return AjaxResult.error("身份证号码已存在!请检查您的输入.");
                }
            }
        }
        if (enterInformation.getRateType() == 2) {
            if(enterInformation.getRate()==null){
                enterInformation.setRate(10L);
            }
        }
        enterInformation.setAuditType(0);
        //如果没设置平台则设置为问到好课
        if (enterInformation.getAppNameType() == null) {
            enterInformation.setAppNameType(1);
        }
        SysUser info = sysUserService.checkUserNameUniqueByWendao(enterInformation.getTelNum(), enterInformation.getAppNameType());
        if (Objects.nonNull(info)) {
            return AjaxResult.error("相同手机号码的账号已经存在!");
        }
        int count = enterInformationService.selectCountTeacherInfo(enterInformation.getTelNum(), enterInformation.getAppNameType());
        if (count > 0) {
            return AjaxResult.error("相同手机号码的店铺信息已经存在!");
        }
        int row = enterInformationService.insertEnterInformation(enterInformation);
        try {
            String username = enterInformation.getTelNum();
            Integer appNameType = enterInformation.getAppNameType();
            String password = RandomStringUtils.randomNumeric(6);
            SysUser sysUser = new SysUser();
            sysUser.setUserName(username);
            sysUser.setNickName(username);
            sysUser.setPassword(SecurityUtils.encryptPassword(password));
            sysUser.setAppNameType(appNameType);
            boolean result = sysUserService.registerUserWendao(sysUser);
            if (result) {
                Long userId = sysUser.getUserId();
                TeacherDTO tTeacher = new TeacherDTO();
                tTeacher.setAvatarUrl(enterInformation.getShopAvatarUrl());
                tTeacher.setPassword(password);
                tTeacher.setMobile(enterInformation.getTelNum());
                tTeacher.setTeacherName(enterInformation.getTeacherName());
                tTeacher.setPlatform(enterInformation.getPlatform());
                tTeacher.setShopName(enterInformation.getShopNickname());
                tTeacher.setTeacherDesc(enterInformation.getShopDesc());
                tTeacher.setStatus(0);
                tTeacher.setTeacherId(userId);
                tTeacher.setOpenPromoter(enterInformation.getOpenPromoter());
                tTeacher.setPromoterNum(enterInformation.getPromoterNum());
                tTeacher.setDyUid(enterInformation.getDyUid());
                //身份证
                if(enterInformation.getEntityType() ==1){
                    String idNumber = enterInformation.getIdNumber();
                    IdcardUtil.Idcard idcardInfo = IdcardUtil.getIdcardInfo(idNumber);
                    //加性别
                    tTeacher.setGender(idcardInfo.getGender());
                }
                tTeacher.setVersion(enterInformation.getVersion());
                tTeacher.setEndDate(enterInformation.getServiceEndTime());
                tTeacher.setAppNameType(appNameType);
                tTeacher.setCreateTime(new Date());
                tTeacher.setUpdateTime(new Date());
                //设置赠送课程功能
                tTeacher.setGiftCourse(enterInformation.getGiftCourse());
                //设置直播开关
                tTeacher.setWapLiveOpen(enterInformation.getWapLiveOpen());
                tTeacher.setDdShopIds(enterInformation.getDdShopIds());
                tTeacher.setDeptId(enterInformation.getDeptId());
                tTeacher.setUserId(enterInformation.getUserId());
                //如果开通了知识店铺则设置店铺信息 begin
                if(StringUtils.isNotBlank(tTeacher.getPlatform())&&tTeacher.getPlatform().contains("8")){
                    WendaoTcplayerLicense license = wendaoTcplayerLicenseService.selectNoUsedDomainRandom();
                    if (license != null) {
                        //直接更新为已使用
                        license.setIsUsed(1);
                        license.setTeacherId(tTeacher.getTeacherId());
                        wendaoTcplayerLicenseService.updateWendaoTcplayerLicense(license);
                        tTeacher.setKnowledgeStoreDomain(license.getKnowledgeStoreDomain());
                        tTeacher.setKnowledgeStoreName(tTeacher.getShopName());
                        tTeacher.setKnowledgeStoreDesc(tTeacher.getTeacherDesc());
                        tTeacher.setKnowledgeStoreAvatarUrl(tTeacher.getAvatarUrl());
                    } else {
                        System.out.println("域名已使用完!请等待新域名!");
                    }
                }
                //设置知识店铺结束 end
                int i = enterInformationService.insertTTeacher(tTeacher);
                //发送短信
                smsService.sendCreateUserAccount(enterInformation.getTelNum(), username, password,appNameType);
                EnterInformation enterInformationUpdate = new EnterInformation();
                enterInformationUpdate.setShopAccount(enterInformation.getTelNum());
                enterInformationUpdate.setShopId(tTeacher.getTeacherId());
                enterInformationUpdate.setId(enterInformation.getId());
                enterInformationUpdate.setAppNameType(enterInformation.getAppNameType());
                enterInformationService.updateEnterInformation(enterInformationUpdate);
                if(enterInformation.getGiftCourse()!=null&&enterInformation.getGiftCourse()==1){
                    TeacherResourcePackage teacherResourcePackage = new TeacherResourcePackage();
                    //调用order服务创建包
                    teacherResourcePackage.setResourcePkgId(56847L);
                    teacherResourcePackage.setTeacherId(tTeacher.getTeacherId());
                    teacherResourcePackage.setResourcePkgName("2999年费版赠课资源包");
                    teacherResourcePackage.setPkgPrice(new BigDecimal(2999));
                    teacherResourcePackage.setValidityPeriod(365);
                    teacherResourcePackage.setResourceCount(360);
                    teacherResourcePackage.setRemainingCount(360);
                    teacherResourcePackage.setIsEnable(1);
                    teacherResourcePackage.setFeatureCode("gift_course");
                    teacherResourcePackage.setStartTime(enterInformation.getServiceBeginTime());
                    teacherResourcePackage.setEndTime(DateUtils.addYears(enterInformation.getServiceBeginTime(), 1));
                    enterInformationService.insertTeacherResourcePkg(teacherResourcePackage);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return toAjax(row);
    }

    /**
     * 修改 入驻信息
     */
    @Log(title = " 入驻信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EnterInformation enterInformation) {
        enterInformation.setRateType(2);
        //判断是否要开通知识店铺
        boolean isOpenKnowledgeStore = false;
        EnterInformation enterInformation1 = enterInformationService.selectEnterInformationById(enterInformation.getId());
        if(StringUtils.isBlank(enterInformation1.getPlatform())){
            enterInformation1.setPlatform("");
        }
        if(StringUtils.isBlank(enterInformation.getPlatform())){
            enterInformation.setPlatform("");
        }
        // 检查抽佣比例是否发生变化，如果有变化则备份原数据
        if (isRateChanged(enterInformation1, enterInformation)) {
            EnterInformationBackup enterInformationBackup = new EnterInformationBackup();
            enterInformationBackup.setShopId(enterInformation1.getShopId());
            List<EnterInformationBackup> enterInformationBackups = enterInformationBackupService.selectEnterInformationBackupList(enterInformationBackup);
            if(CollectionUtils.isNotEmpty(enterInformationBackups)){
                EnterInformationBackup enterInformationBackup1 = enterInformationBackups.get(0);
                //Date now = new Date();
                //enterInformationBackup1.getCreateTime()和now间隔小于10天,不能修改,限制频繁修改!
                //if(enterInformationBackup1.getCreateTime()!=null&&now.getTime()-enterInformationBackup1.getCreateTime().getTime()<10*24*60*60*1000){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String lastModifyTime = sdf.format(enterInformationBackup1.getCreateTime());
                    return error("检测到您修改了抽佣比例,不支持此操作!上次比例修改时间"+lastModifyTime+",如确实要修改请寻求技术支持!");
                //}
            }
            backupEnterInformation(enterInformation1);
        }
        if(enterInformation1.getGiftCourse()==null||enterInformation1.getGiftCourse()==0){
            if(enterInformation.getGiftCourse()!=null&&enterInformation.getGiftCourse()==1){
                TeacherResourcePackage teacherResourcePackage = new TeacherResourcePackage();
                //调用order服务创建包
                teacherResourcePackage.setResourcePkgId(56847L);
                teacherResourcePackage.setTeacherId(enterInformation1.getShopId());
                teacherResourcePackage.setResourcePkgName("2999年费版赠课资源包");
                teacherResourcePackage.setPkgPrice(new BigDecimal(2999));
                teacherResourcePackage.setValidityPeriod(365);
                teacherResourcePackage.setResourceCount(360);
                teacherResourcePackage.setRemainingCount(360);
                teacherResourcePackage.setIsEnable(1);
                teacherResourcePackage.setFeatureCode("gift_course");
                teacherResourcePackage.setStartTime(enterInformation.getServiceBeginTime());
                teacherResourcePackage.setEndTime(DateUtils.addYears(enterInformation.getServiceBeginTime(), 1));
                enterInformationService.insertTeacherResourcePkg(teacherResourcePackage);
            }
        }
        if(!enterInformation1.getPlatform().contains("8")&&enterInformation.getPlatform().contains("8")){
            isOpenKnowledgeStore = true;
        }
        

        
        if (enterInformation.getEntityType() == 1) {
            if (StringUtils.isBlank(enterInformation.getIdNumber())) {
                //身份证号码错误
                return AjaxResult.error("身份证号码为空");
            }
            enterInformation.setIdNumber(enterInformation.getIdNumber().trim());
            if (enterInformation.getIdNumber().length() != 18 && enterInformation.getIdNumber().length() != 15) {
                return AjaxResult.error("身份证号码长度错误,请填写15位或18位身份证号码!");
            }

            boolean validCard = false;
            if (enterInformation.getIdNumber().length() == 18) {
                validCard = IdcardUtil.isValidCard18(enterInformation.getIdNumber());
            } else {
                validCard = IdcardUtil.isValidCard15(enterInformation.getIdNumber());
            }
            if (!validCard) {
                return AjaxResult.error("身份证号码填写错误,合法性校验失败,请重新核对身份证号码!");
            }
        }
        int i = enterInformationService.updateEnterInformation(enterInformation);
        //如果开通过了,又取消了,又开通了
        TeacherDTO tTeacherQuery = enterInformationService.selectTeacherKnownledgeStoreDomain(enterInformation.getShopId());
        if (isOpenKnowledgeStore && tTeacherQuery != null && StringUtils.isBlank(tTeacherQuery.getKnowledgeStoreDomain())) {
            WendaoTcplayerLicense license = wendaoTcplayerLicenseService.selectNoUsedDomainRandom();
            if (license != null) {
                EnterInformation updated = enterInformationService.selectEnterInformationById(enterInformation.getId());
                //直接更新为已使用
                license.setIsUsed(1);
                license.setTeacherId(updated.getShopId());
                wendaoTcplayerLicenseService.updateWendaoTcplayerLicense(license);
                TeacherDTO tTeacher = new TeacherDTO();
                tTeacher.setTeacherId(updated.getShopId());
                tTeacher.setKnowledgeStoreDomain(license.getKnowledgeStoreDomain());
                tTeacher.setKnowledgeStoreName(updated.getShopNickname());
                tTeacher.setKnowledgeStoreDesc(updated.getShopDesc());
                tTeacher.setKnowledgeStoreAvatarUrl(updated.getShopAvatarUrl());
                tTeacher.setPlatform(enterInformation.getPlatform());
                tTeacher.setMobile(enterInformation.getTelNum());
                //修改版本
                if(enterInformation.getVersion()!=null){
                    tTeacher.setVersion(enterInformation.getVersion());
                }else{
                    tTeacher.setVersion(1);
                }
                tTeacher.setGiftCourse(enterInformation.getGiftCourse() == null ? 0 : enterInformation.getGiftCourse());
                tTeacher.setWapLiveOpen(enterInformation.getWapLiveOpen() == null ? 0 : enterInformation.getWapLiveOpen());
                tTeacher.setDdShopIds(enterInformation.getDdShopIds());
                if(enterInformation.getServiceEndTime()!=null){
                    tTeacher.setEndDate(enterInformation.getServiceEndTime());
                }
                tTeacher.setOpenPromoter(enterInformation.getOpenPromoter());
                tTeacher.setPromoterNum(enterInformation.getPromoterNum());
                enterInformationService.updateTeacherFroKnowledgeStore(tTeacher);
            } else {
                System.out.println("域名已使用完!请等待新域名!");
            }
        } else {
            enterInformationService.updateTeacherPlatfrom(enterInformation.getPlatform()
                    ,enterInformation.getVersion()
                    ,enterInformation1.getShopId()
                    ,enterInformation.getGiftCourse()
                    ,enterInformation.getWapLiveOpen()
                    ,enterInformation.getDdShopIds(),enterInformation.getServiceEndTime(),enterInformation.getOpenPromoter(),enterInformation.getPromoterNum());
        }
        return toAjax(i);
    }

    /**
     * 删除 入驻信息
     */
    @Log(title = " 入驻信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(enterInformationService.deleteEnterInformationByIds(ids));
    }


    /**
     * 新增 入驻信息
     */
//    @Log(title = " 入驻信息", businessType = BusinessType.INSERT)
    @PostMapping("/addEnterInformation")
    public AjaxResult addEnterInformation(@RequestBody EnterInformation enterInformation) {
        String realName = enterInformation.getRealName();
        if (StringUtils.isBlank(enterInformation.getTeacherName())) {
            enterInformation.setTeacherName(realName);
        }
        EnterInformation enterInformation1 = new EnterInformation();
        enterInformation1.setTelNum(enterInformation.getTelNum());
        enterInformation1.setAppNameType(enterInformation.getAppNameType());
        List<EnterInformation> list = enterInformationService.getEnterInformationList(enterInformation1);
        if (CollectionUtils.isNotEmpty(list)) {
            return AjaxResult.error("手机号码已存在!请检查您的输入.");
        }
        //检测子账号是否存在并提示
        Long shopId = enterInformationService.selectSopIdForCheckSubAccount(enterInformation.getTelNum(),enterInformation.getAppNameType());
        if(shopId!=null){
            return AjaxResult.error("该手机号码为子账号!已存在!子账号所属店铺id:"+shopId);
        }
        if (enterInformation.getEntityType() == 1) {
            if (StringUtils.isNotEmpty(enterInformation.getIdNumber())) {
                EnterInformation enterInformation2 = new EnterInformation();
                enterInformation2.setIdNumber(enterInformation.getIdNumber());
                enterInformation2.setAppNameType(enterInformation.getAppNameType());
                List<EnterInformation> enterInformationList = enterInformationService.getEnterInformationList(enterInformation2);
                if (CollectionUtils.isNotEmpty(enterInformationList)) {
                    return AjaxResult.error("身份证号码已存在!请检查您的输入.");
                }
            }
        }
        enterInformation.setAuditType(1);
        //发送短信通知  审核中
        if (StringUtils.isNotEmpty(enterInformation.getTelNum())) {
            SmsOrderVO smsOrderVO = new SmsOrderVO();
            smsOrderVO.setPhoneNumber(enterInformation.getTelNum());
            String uri = "/sms_order/sendSmsAudit";
            String url = path + uri;
            String s = JSON.toJSONString(smsOrderVO);
            HttpUtil.post(url, s);
        }
        return toAjax(enterInformationService.insertEnterInformation(enterInformation));
    }


    @PostMapping("/queryPassword")
    public AjaxResult queryPassword(@RequestBody EnterInformation enterInformation) {
        PasswordInfoDTO passwordInfoDTO = enterInformationService.selectTeacherInfoByAppNameTypeAndTelNum(enterInformation);
        if(passwordInfoDTO==null){
            return AjaxResult.error("手机号码不存在");
        }
        return success(passwordInfoDTO);
    }
    @PostMapping("/sendSmsForChangeShopMobile")
    public AjaxResult sendSmsForChangeShopMobile(@RequestBody SendSmsForChangeShopMobileDTO sendSmsForChangeShopMobileDTO) {
        if(sendSmsForChangeShopMobileDTO==null||StringUtils.isBlank(sendSmsForChangeShopMobileDTO.getPhoneNumber())||StringUtils.length(sendSmsForChangeShopMobileDTO.getPhoneNumber())!=11){
            return AjaxResult.error("请正确输入手机号码");
        }
        String code = RandomStringUtils.randomNumeric(4);
        String uuid = smsService.sendValidateCodeSms(sendSmsForChangeShopMobileDTO.getPhoneNumber(), code);
        Map<String,String> map = new HashMap<>();
        map.put("uuid",uuid);
        return AjaxResult.success(map);
    }

    @PostMapping("/changeShopMobile")
    public AjaxResult changeShopMobile(@RequestBody ChangeShopMobileDTO changeShopMobileDTO) {
        if(StringUtils.isAnyBlank(changeShopMobileDTO.getNewTelNum(),changeShopMobileDTO.getOldTelNum())){
            return AjaxResult.error("新老手机号码有一个为空");
        }
        if(changeShopMobileDTO.getAppNameType()==null){
            return AjaxResult.error("appNameType参数不能为空");
        }
        //去除空格
        changeShopMobileDTO.setNewTelNum(changeShopMobileDTO.getNewTelNum().trim());
        if(changeShopMobileDTO.getNewTelNum().length()!=11){
            return AjaxResult.error("新手机号码长度不正确,必须为11位!");
        }
        int count = enterInformationService.countSmsRecord(changeShopMobileDTO);
        if(count<1){
            return AjaxResult.error("验证码错误");
        }
        EnterInformation enterInformation = new EnterInformation();
        enterInformation.setTelNum(changeShopMobileDTO.getOldTelNum());
        enterInformation.setAppNameType(changeShopMobileDTO.getAppNameType());
        PasswordInfoDTO passwordInfoDTO = enterInformationService.selectTeacherInfoByAppNameTypeAndTelNum(enterInformation);
        if(passwordInfoDTO==null){
            return AjaxResult.error("老手机号码不存在");
        }

        //判断新手机号码是否已经存在
        EnterInformation enterInformation1 = new EnterInformation();
        enterInformation1.setTelNum(changeShopMobileDTO.getNewTelNum());
        enterInformation1.setAppNameType(changeShopMobileDTO.getAppNameType());
        PasswordInfoDTO passwordInfoDTO1 = enterInformationService.selectTeacherInfoByAppNameTypeAndTelNum(enterInformation1);
        if(passwordInfoDTO1!=null){
            return AjaxResult.error("新手机号码对应店铺已存在");
        }
        //更新老师表
        int row = enterInformationService.updateTeacherMobile(changeShopMobileDTO);
        //更新sys_user表
        int row1 = enterInformationService.updateSysUserByNewMobile(changeShopMobileDTO);
        //更新入驻信息
        int row2 = enterInformationService.updateEnterInformationTelNum(changeShopMobileDTO);

        return AjaxResult.success("修改成功");
    }

    /**
     * 修改入驻信息
     */
//    @Log(title = " 入驻信息", businessType = BusinessType.INSERT)
    @PostMapping("/updateEnterInformation")
    public AjaxResult updateEnterInformation(@RequestBody EnterInformation enterInformation) {
        enterInformation.setRateType(2);
        if (enterInformation.getRateType()!=null&&enterInformation.getRateType() == 2) {
            if(enterInformation.getRate()==null){
                enterInformation.setRate(10L);
            }
        }
        if (enterInformation.getAuditType() == 0) {
            if(StringUtils.isNotBlank(enterInformation.getIdNumber())){
                boolean validCard = IdcardUtil.isValidCard(enterInformation.getIdNumber());
                if (!validCard) {
                    return AjaxResult.error("身份证输入有误");
                }
            }
            //发送短信通知  审核通过
//            EnterInformationMessage enterInformationMessage = new EnterInformationMessage();
//            BeanUtils.copyProperties(enterInformation, enterInformationMessage);
            //修改为直接操作数据库
            SysUser info = sysUserService.checkUserNameUniqueByWendao(enterInformation.getTelNum(), enterInformation.getAppNameType());
            if (Objects.nonNull(info)) {
                return AjaxResult.error("相同手机号码的账号已经存在!");
            }
            int count = enterInformationService.selectCountTeacherInfo(enterInformation.getTelNum(), enterInformation.getAppNameType());
            if (count > 0) {
                return AjaxResult.error("相同手机号码的店铺信息已经存在!");
            }
            //检测子账号是否存在并提示
            Long shopId = enterInformationService.selectSopIdForCheckSubAccount(enterInformation.getTelNum(),enterInformation.getAppNameType());
            if(shopId!=null){
                return AjaxResult.error("该手机号码为子账号!已存在!子账号所属店铺id:"+shopId);
            }
            try {
                String username = enterInformation.getTelNum();
                Integer appNameType = enterInformation.getAppNameType();
                String password = RandomStringUtils.randomNumeric(6);
                SysUser sysUser = new SysUser();
                sysUser.setUserName(username);
                sysUser.setNickName(username);
                sysUser.setPassword(SecurityUtils.encryptPassword(password));
                sysUser.setAppNameType(appNameType);
                boolean result = sysUserService.registerUserWendao(sysUser);
                if (result) {
                    Long userId = sysUser.getUserId();
                    TeacherDTO tTeacher = new TeacherDTO();
                    tTeacher.setAvatarUrl(enterInformation.getShopAvatarUrl());
                    tTeacher.setPassword(password);
                    tTeacher.setMobile(enterInformation.getTelNum());
                    tTeacher.setTeacherName(enterInformation.getTeacherName());
                    tTeacher.setTeacherId(userId);
                    tTeacher.setPlatform(enterInformation.getPlatform());
                    tTeacher.setOpenPromoter(enterInformation.getOpenPromoter());
                    tTeacher.setPromoterNum(enterInformation.getPromoterNum());
                    // 如果开通了知识店铺,则审核通过
                    //如果开通了知识店铺则设置店铺信息 begin
                    if(StringUtils.isNotBlank(tTeacher.getPlatform())&&tTeacher.getPlatform().contains("8")){
                        WendaoTcplayerLicense license = wendaoTcplayerLicenseService.selectNoUsedDomainRandom();
                        if (license != null) {
                            //直接更新为已使用
                            license.setIsUsed(1);
                            license.setTeacherId(tTeacher.getTeacherId());
                            wendaoTcplayerLicenseService.updateWendaoTcplayerLicense(license);
                            tTeacher.setKnowledgeStoreDomain(license.getKnowledgeStoreDomain());
                            tTeacher.setKnowledgeStoreName(tTeacher.getShopName());
                            tTeacher.setKnowledgeStoreDesc(tTeacher.getTeacherDesc());
                            tTeacher.setKnowledgeStoreAvatarUrl(tTeacher.getAvatarUrl());
                        } else {
                            System.out.println("域名已使用完!请等待新域名!");
                        }
                    }
                    tTeacher.setShopName(enterInformation.getShopNickname());
                    tTeacher.setTeacherDesc(enterInformation.getShopDesc());
                    tTeacher.setStatus(0);
                    tTeacher.setDyUid(enterInformation.getDyUid());
                    tTeacher.setGiftCourse(enterInformation.getGiftCourse());
                    tTeacher.setWapLiveOpen(enterInformation.getWapLiveOpen());
                    tTeacher.setDdShopIds(enterInformation.getDdShopIds());
                    tTeacher.setDeptId(enterInformation.getDeptId());
                    tTeacher.setUserId(enterInformation.getUserId());
                    //身份证
                    if(enterInformation.getEntityType() ==1){
                        String idNumber = enterInformation.getIdNumber();
                        IdcardUtil.Idcard idcardInfo = IdcardUtil.getIdcardInfo(idNumber);
                        //加性别
                        tTeacher.setGender(idcardInfo.getGender());
                    }
                    tTeacher.setVersion(enterInformation.getVersion());
                    tTeacher.setEndDate(enterInformation.getServiceEndTime());
                    tTeacher.setAppNameType(appNameType);
                    tTeacher.setCreateTime(new Date());
                    tTeacher.setUpdateTime(new Date());
                    int i = enterInformationService.insertTTeacher(tTeacher);
                    //发送短信
                    smsService.sendCreateUserAccount(enterInformation.getTelNum(), username, password,appNameType);
                    EnterInformation enterInformationUpdate = new EnterInformation();
                    enterInformationUpdate.setShopAccount(enterInformation.getTelNum());
                    enterInformationUpdate.setShopId(tTeacher.getTeacherId());
                    enterInformationUpdate.setId(enterInformation.getId());
                    enterInformationUpdate.setAppNameType(enterInformation.getAppNameType());
                    enterInformationService.updateEnterInformation(enterInformationUpdate);

                    if(enterInformation.getGiftCourse()!=null&&enterInformation.getGiftCourse()==1){
                        TeacherResourcePackage teacherResourcePackage = new TeacherResourcePackage();
                        //调用order服务创建包
                        teacherResourcePackage.setResourcePkgId(56847L);
                        teacherResourcePackage.setTeacherId(tTeacher.getTeacherId());
                        teacherResourcePackage.setResourcePkgName("2999年费版赠课资源包");
                        teacherResourcePackage.setPkgPrice(new BigDecimal(2999));
                        teacherResourcePackage.setValidityPeriod(365);
                        teacherResourcePackage.setResourceCount(360);
                        teacherResourcePackage.setRemainingCount(360);
                        teacherResourcePackage.setIsEnable(1);
                        teacherResourcePackage.setFeatureCode("gift_course");
                        teacherResourcePackage.setStartTime(enterInformation.getServiceBeginTime());
                        teacherResourcePackage.setEndTime(DateUtils.addYears(enterInformation.getServiceBeginTime(), 1));
                        enterInformationService.insertTeacherResourcePkg(teacherResourcePackage);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (enterInformation.getAuditType() == 2) {
            //发送短信通知  驳回
            if (StringUtils.isNotEmpty(enterInformation.getTelNum())) {
                SmsOrderVO smsOrderVO = new SmsOrderVO();
                smsOrderVO.setPhoneNumber(enterInformation.getTelNum());
                smsOrderVO.setReason(enterInformation.getRejectReason());
                String uri = "/sms_order/sendSmsAuditReject";
                String url = path + uri;
                String s = JSON.toJSONString(smsOrderVO);
                HttpUtil.post(url, s);
            }
        }
        enterInformation.setAuditTime(DateUtils.getNowDate());
        return toAjax(enterInformationService.updateEnterInformation(enterInformation));
    }

    /**
         * 入驻信息列表
     */
//    @Log(title = " 入驻信息", businessType = BusinessType.INSERT)
    @PostMapping("/selectAll")
    public AjaxResult selectAll(@RequestBody EnterInformationVO enterInformationVO) {
        if(enterInformationVO.getAppNameType()!=null&&enterInformationVO.getAppNameType()==3){
            enterInformationVO.setAppNameType(null);
        }
        return success(enterInformationService.selectAll(enterInformationVO));
    }


    /**
     * 上传图片
     */
//    @Log(title = " 入驻信息", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JSONObject upload(@RequestPart(value = "file") MultipartFile file) {
        String uri = "/fundIncome/upload";
        String url = path + uri;
        Map<String, Object> map = new HashMap<>();
        System.out.println(file);
        map.put("file", convert(file));
        return JSONObject.parseObject(HttpUtil.post(url, map));
    }


    public static File convert(MultipartFile file){
        File convFile = new File("temp_image", file.getOriginalFilename());
        if ( !convFile.getParentFile( ).exists()) {
            System.out.println( "mkdir:" + convFile.getParentFile( ).mkdirs());
        }
        try {
            convFile.createNewFile();
            FileOutputStream fos = new FileOutputStream(convFile);
            fos.write(file.getBytes());
            fos.close( );
        }catch (IOException e){
            e.printStackTrace( );
        }
        return convFile;
    }

    /**
     * 发送验证码
     *
     * @param sendSmsVO
     * @return
     */
    @PostMapping("/backstageSendSms")
    public JSONObject backstageSendSms(@RequestBody SendSmsVO sendSmsVO) {
        String uri = "/sms_controller/backstageSendSms";
        String url = teacherPath + uri;
        String s = JSON.toJSONString(sendSmsVO);
        return JSONObject.parseObject(HttpUtil.post(url, s));
    }

    /**
     * 校验验证码
     *
     * @param validateSmsVO
     * @return
     */
    @PostMapping("/backstageValidateSms")
    public JSONObject backstageValidateSms(@RequestBody ValidateSmsVO validateSmsVO) {
        String uri = "/sms_controller/backstageValidateSms";
        String url = teacherPath + uri;
        String s = JSON.toJSONString(validateSmsVO);
        return JSONObject.parseObject(HttpUtil.post(url, s));
    }

    /**
     * 老师入驻申请列表
     *
     * @param teacherEnterVO
     * @return
     */
    @PostMapping("/getTeacherEnterList")
    public AjaxResult getTeacherEnterList(@RequestBody TeacherEnterVO teacherEnterVO) {

        return success(enterInformationService.getTeacherEnterList(teacherEnterVO));
    }

    /**
     * 修改入驻申请状态
     */
    @PostMapping("/changeTeacherEnterStatus")
    public AjaxResult changeTeacherEnterStatus(@RequestBody TeacherEnterStatusVO enterStatusVO){
        return toAjax(enterInformationService.updateTeacherEnterStatus(enterStatusVO));

    }

    /**
     * 分类列表
     */
    @GetMapping("/getCategoryList")
    public AjaxResult getCategoryList() {
        List<DyMiniClassDTO> list = enterInformationService.selectDyMiniClassList();
        return AjaxResult.success(list);
    }

    /**
     * 检查抽佣比例是否发生变化
     * @param original 原始数据
     * @param updated 更新后的数据
     * @return 是否有变化
     */
    private boolean isRateChanged(EnterInformation original, EnterInformation updated) {
        // 比较抖音抽成比例
        if (!isIntegerPartEqual(original.getDyRate(), updated.getDyRate())) {
            return true;
        }
        // 比较快手抽成比例
        if (!isIntegerPartEqual(original.getKsRate(), updated.getKsRate())) {
            return true;
        }
        // 比较微信抽成比例
        if (!isIntegerPartEqual(original.getWxRate(), updated.getWxRate())) {
            return true;
        }
        // 比较视频号抽成比例
        if (!isIntegerPartEqual(original.getSphRate(), updated.getSphRate())) {
            return true;
        }
        // 比较知识店铺抽成比例
        if (!isIntegerPartEqual(original.getZsdpRate(), updated.getZsdpRate())) {
            return true;
        }
        // 比较小红书抽成比例
        if (!isIntegerPartEqual(original.getXhsRate(), updated.getXhsRate())) {
            return true;
        }
        // 比较h5抽成比例
        if (!isIntegerPartEqual(original.getH5Rate(), updated.getH5Rate())) {
            return true;
        }
        // 比较pc端抽成比例
        if (!isIntegerPartEqual(original.getPcRate(), updated.getPcRate())) {
            return true;
        }
        // 比较抖店端抽成比例
        if (!isIntegerPartEqual(original.getDdRate(), updated.getDdRate())) {
            return true;
        }
        return false;
    }

    private boolean isIntegerPartEqual(BigDecimal bd1, BigDecimal bd2) {
        // 处理null的情况
        if (bd1 == null && bd2 == null) {
            return true;
        }
        if (bd1 == null || bd2 == null) {
            return false;
        }

        // 获取整数部分（向零方向舍入）
        BigDecimal integerPart1 = bd1.setScale(0, RoundingMode.DOWN);
        BigDecimal integerPart2 = bd2.setScale(0, RoundingMode.DOWN);

        // 比较整数部分
        return integerPart1.compareTo(integerPart2) == 0;
    }

    /**
     * 备份入驻信息
     * @param enterInformation 要备份的入驻信息
     */
    private void backupEnterInformation(EnterInformation enterInformation) {
        try {
            EnterInformationBackup backup = new EnterInformationBackup();
            // 复制所有字段
            backup.setEntityType(enterInformation.getEntityType());
            backup.setFrontPath(enterInformation.getFrontPath());
            backup.setBackPath(enterInformation.getBackPath());
            backup.setRealName(enterInformation.getRealName());
            backup.setIdNumber(enterInformation.getIdNumber());
            backup.setTeacherName(enterInformation.getTeacherName());
            backup.setBusinessLicenseCompanyName(enterInformation.getBusinessLicenseCompanyName());
            backup.setBusinessLicenseNo(enterInformation.getBusinessLicenseNo());
            backup.setBusinessLicensePath(enterInformation.getBusinessLicensePath());
            backup.setTelNum(enterInformation.getTelNum());
            backup.setCourseForm(enterInformation.getCourseForm());
            backup.setFirstClassId(enterInformation.getFirstClassId());
            backup.setFirstClassPid(enterInformation.getFirstClassPid());
            backup.setFirstClassTitle(enterInformation.getFirstClassTitle());
            backup.setFirstClassDouyinClassId(enterInformation.getFirstClassDouyinClassId());
            backup.setSecondClassId(enterInformation.getSecondClassId());
            backup.setSecondClassPid(enterInformation.getSecondClassPid());
            backup.setSecondClassTitle(enterInformation.getSecondClassTitle());
            backup.setSecondClassDouyinClassId(enterInformation.getSecondClassDouyinClassId());
            backup.setShopNickname(enterInformation.getShopNickname());
            backup.setShopAvatarUrl(enterInformation.getShopAvatarUrl());
            backup.setShopDesc(enterInformation.getShopDesc());
            backup.setPlatform(enterInformation.getPlatform());
            backup.setDyAccount(enterInformation.getDyAccount());
            backup.setDyUid(enterInformation.getDyUid());
            backup.setKsAccount(enterInformation.getKsAccount());
            backup.setWxAccount(enterInformation.getWxAccount());
            backup.setSphAccount(enterInformation.getSphAccount());
            backup.setServiceBeginTime(enterInformation.getServiceBeginTime());
            backup.setServiceEndTime(enterInformation.getServiceEndTime());
            backup.setVersion(enterInformation.getVersion());
            backup.setRateType(enterInformation.getRateType());
            backup.setRate(enterInformation.getRate());
            backup.setAccountSpecialist(enterInformation.getAccountSpecialist());
            backup.setCustomerServiceSpecialist(enterInformation.getCustomerServiceSpecialist());
            backup.setShopAccount(enterInformation.getShopAccount());
            backup.setShopId(enterInformation.getShopId());
            backup.setAuditType(enterInformation.getAuditType());
            backup.setRejectReason(enterInformation.getRejectReason());
            backup.setAuditTime(enterInformation.getAuditTime());
            backup.setDyFansNum(enterInformation.getDyFansNum());
            backup.setKsFansNum(enterInformation.getKsFansNum());
            backup.setSphFansNum(enterInformation.getSphFansNum());
            backup.setDyMasterImg(enterInformation.getDyMasterImg());
            backup.setKsMasterImg(enterInformation.getKsMasterImg());
            backup.setWxMasterImg(enterInformation.getWxMasterImg());
            backup.setSphMasterImg(enterInformation.getSphMasterImg());
            backup.setAppNameType(enterInformation.getAppNameType());
            // 设置抽佣比例字段
            backup.setDyRate(enterInformation.getDyRate());
            backup.setKsRate(enterInformation.getKsRate());
            backup.setWxRate(enterInformation.getWxRate());
            backup.setSphRate(enterInformation.getSphRate());
            backup.setZsdpRate(enterInformation.getZsdpRate());
            backup.setXhsRate(enterInformation.getXhsRate());
            backup.setH5Rate(enterInformation.getH5Rate());
            backup.setPcRate(enterInformation.getPcRate());
            backup.setDdRate(enterInformation.getDdRate());
            backup.setGiftCourse(enterInformation.getGiftCourse());
            backup.setWapLiveOpen(enterInformation.getWapLiveOpen());
            backup.setDdShopIds(enterInformation.getDdShopIds());
            backup.setDeptId(enterInformation.getDeptId());
            backup.setUserId(enterInformation.getUserId());
            // backup.setClientEnterInformationId(enterInformation.getId());
            backup.setOpenPromoter(enterInformation.getOpenPromoter());
            backup.setPromoterNum(enterInformation.getPromoterNum());
            backup.setIsAgent(enterInformation.getIsAgent());
            
            // 设置备份时间
            backup.setCreateTime(new Date());
            backup.setUpdateTime(new Date());
            //backup.setCreateBy(SecurityUtils.getUsername());
            
            // 保存备份
            enterInformationBackupService.insertEnterInformationBackup(backup);
        } catch (Exception e) {
            // 记录日志，但不影响主流程
            System.err.println("备份入驻信息失败: " + e.getMessage());
            //e.printStackTrace();
        }
    }
}
