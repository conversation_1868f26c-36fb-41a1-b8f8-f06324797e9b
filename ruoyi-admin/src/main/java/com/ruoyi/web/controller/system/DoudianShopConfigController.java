package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.MaterialUploadImageSyncDataDTO;
import com.ruoyi.web.dto.MaterialUploadImageSyncParamDTO;
import com.ruoyi.web.vo.DoudianShopConfigVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/doudian_config_controller")
public class DoudianShopConfigController {
    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 查询抖店配置列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map, HttpServletRequest request) {
        String uri = path + "/doudian/backend/config/list";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, null);
        JSONObject jsonObject = JSON.parseObject(s);
        return jsonObject;
    }

    /**
     * 按id查询抖店配置
     */
    @GetMapping("/{id}")
    public JSONObject changeVideoName(@PathVariable("id") Long id, HttpServletRequest request) {
        String uri = path + "/doudian/backend/config/"+ id;
        return HttpClientPostFormUtil.getRequestBody(uri, null);
    }

    /**
     * 新增抖店配置
     * @param doudianShopConfig
     * @return
     */
    @PostMapping("/add")
    public JSONObject add(@RequestBody DoudianShopConfigVO doudianShopConfig)
    {
        String uri = path + "/doudian/backend/config";
        String jsonString = JSON.toJSONString(doudianShopConfig);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, null);
    }

    /**
     * 修改抖店配置
     * @param doudianShopConfig
     * @return
     */
    @PutMapping("/edit")
    public JSONObject edit(@RequestBody DoudianShopConfigVO doudianShopConfig)
    {
        String uri = path + "/doudian/backend/config";
        String jsonString = JSON.toJSONString(doudianShopConfig);
        return HttpClientPostFormUtil.putRequestBody(uri, jsonString, null);
    }


    @PostMapping("/uploadImageSync")
    public JSONObject uploadImageSync(@RequestBody MaterialUploadImageSyncParamDTO param) {
        String uri = path + "/doudian_controller/uploadImageSync";
        String jsonString = JSON.toJSONString(param);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, null);
    }

    @PostMapping("/queryMaterialDetail")
    public JSONObject queryMaterialDetail(@RequestBody MaterialUploadImageSyncDataDTO param1) {
        String uri = path + "/doudian_controller/queryMaterialDetail";
        String jsonString = JSON.toJSONString(param1);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, null);
    }




}
