package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.ClockInQuestSearchDTO;
import com.ruoyi.web.vo.ClockInQuestAllVO;
import com.ruoyi.web.vo.ClockInQuestVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/clock_in_quest")
public class ClockInQuestController {

    @Value("${wendao.teacher.path}")
    private String path;


    /**
     * 总后台查询打卡任务列表
     */
    @PostMapping("/listTotalAll")
    public JSONObject listTotalAll(@RequestBody ClockInQuestSearchDTO clockInQuestSearchDTO, HttpServletRequest request) {
        String uri = "/clock_in_quest/listTotalAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockInQuestSearchDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    @PostMapping("/selectAll")
    public JSONObject selectAll(@RequestBody ClockInQuestAllVO clockInQuestAllVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_in_quest/selectAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockInQuestAllVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    @PostMapping("/addClockInQuest")
    public JSONObject addClockInQuest(@RequestBody ClockInQuestVO clockInQuestVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_in_quest/addClockInQuest";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockInQuestVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }

    @GetMapping(value = "/getByCourseId/{id}")
    public JSONObject getByCourseId(@PathVariable("id") Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_in_quest/getByCourseId/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }


    @GetMapping(value = "/getById/{id}")
    public JSONObject getById(@PathVariable("id") Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_in_quest/getById/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }


    @PostMapping("/updateQuest")
    public JSONObject updateQuest(@RequestBody ClockInQuestVO clockInQuestVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_in_quest/updateQuest";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockInQuestVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }

    /**
     * 根据老师id查询店铺信息
     * @param clockInQuestVO
     * @return
     */
    @PostMapping("/teacherInfomation")
    public JSONObject teacherInfomation(@RequestBody ClockInQuestVO clockInQuestVO) {

        String uri = "/clock_in_quest/teacherInfomation";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockInQuestVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,null);
    }


}
