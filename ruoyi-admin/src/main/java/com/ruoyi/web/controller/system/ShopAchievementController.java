package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/shop_achievement")
public class ShopAchievementController {

    @Value("${wendao.teacher.path}")
    private String path;


    /**
     * 店铺业绩统计列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map) {
        String uri = path + "/shop_achievement/list";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, null);
        return JSON.parseObject(s);
    }

    /**
     * 商务专员列表
     */
    @GetMapping("/accountSpecialist")
    public JSONObject accountSpecialist() {
        String url = path + "/shop_achievement/accountSpecialist";
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,-1L));
    }

    /**
     * 客服专员列表
     */
    @GetMapping("/customerServiceSpecialist")
    public JSONObject customerServiceSpecialist() {
        String url = path + "/shop_achievement/customerServiceSpecialist";
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,-1L));
    }
}
