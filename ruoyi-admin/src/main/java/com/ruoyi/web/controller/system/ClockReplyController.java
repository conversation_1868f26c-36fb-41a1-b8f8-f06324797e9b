package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.ClockReply;
import com.ruoyi.web.vo.ClockReplyVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/clock_reply")
public class ClockReplyController {

    @Value("${wendao.teacher.path}")
    private String path;


    @PostMapping("/selectByClockRecordId")
    public JSONObject selectByClockRecordId(@RequestBody ClockReplyVO clockReplyVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_reply/selectByClockRecordId";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockReplyVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    @GetMapping(value = "/hideById/{id}")
    public JSONObject hideById(@PathVariable("id") Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_reply/hideById/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }

    @PostMapping("/addTeacherReply")
    public JSONObject addTeacherReply(@RequestBody ClockReply clockReply, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_reply/addTeacherReply";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockReply);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }
}
