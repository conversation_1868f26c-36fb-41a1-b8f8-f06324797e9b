package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.CourseRefundStateVO;
import com.ruoyi.web.vo.CourseRefundUpdateVO;
import com.ruoyi.web.vo.CourseRefundVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 退款信息Controller
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@RestController
@RequestMapping("/refund")
public class CourseRefundController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;

    /**
     * 查询退款信息列表
     */
    @PostMapping("/list")
    public JSONObject list(@RequestBody CourseRefundVO courseRefundVO) {

        if (Objects.nonNull(courseRefundVO.getTeacherId())) {
            Long teacherId = courseRefundVO.getTeacherId();
            String uri = "/refund/list";
            String url = path + uri;
            String jsonString = JSON.toJSONString(courseRefundVO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
        }
        String uri = "/refund/list";
        String url = path + uri;
        String jsonString = JSON.toJSONString(courseRefundVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


    /**
     * 获取退款信息详细信息
     */
    @GetMapping(value = "/detail/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id) {
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/refund/detail/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, teacherId));
    }

    /**
     * 同意退款
     *
     * @param courseRefundStateVO
     * @return
     */
    @PostMapping("/updateStatus")
    public JSONObject agreeRefund(@RequestBody CourseRefundStateVO courseRefundStateVO) {
        Long teacherId = courseRefundStateVO.getTeacherId();
        String uri = "/refund/updateStatus";
        String url = path + uri;
        String jsonString = JSON.toJSONString(courseRefundStateVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }

    /**
     * 拒绝退款
     */
    @PostMapping("/update")
    public JSONObject refusalRefund(@RequestBody CourseRefundUpdateVO courseRefundUpdateVO) {
        Long teacherId = courseRefundUpdateVO.getTeacherId();
        String uri = "/refund/update";
        String url = path + uri;
        String jsonString = JSON.toJSONString(courseRefundUpdateVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }

    /**
     *  查询小红书退款原因，传teacherId和id就可以
     * @param courseRefundUpdateVO
     * @return
     */
    @PostMapping("/queryXhsReason")
    public JSONObject queryXhsReason(@RequestBody CourseRefundUpdateVO courseRefundUpdateVO) {
        Long teacherId = courseRefundUpdateVO.getTeacherId();
        String uri = "/refund/queryXhsReason";
        String url = path + uri;
        String jsonString = JSON.toJSONString(courseRefundUpdateVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }

}
