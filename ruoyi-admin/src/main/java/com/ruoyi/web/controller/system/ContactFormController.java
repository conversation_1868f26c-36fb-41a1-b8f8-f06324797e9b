package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/contcat_form")
public class ContactFormController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 查询招商信息列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String, String> map) {
        String uri = "/contcat_form/list";
        String url = path + uri;
        String s = HttpClientPostFormUtil.sendGetMap(url, map, null);
        return JSON.parseObject(s);
    }
}
