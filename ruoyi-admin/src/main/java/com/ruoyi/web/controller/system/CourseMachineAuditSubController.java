package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.web.domain.CourseMachineAuditSub;
import com.ruoyi.web.service.CourseMachineAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/course_machine_audit_sub")
public class CourseMachineAuditSubController {
    @Autowired
    private CourseMachineAuditService courseMachineAuditService;

    @GetMapping("/list")
    public TableDataInfo list(CourseMachineAuditSub courseMachineAuditSub) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        return courseMachineAuditService.listSub(courseMachineAuditSub,pageNum,pageSize);
    }
}
