package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.PerformanceEvaluationDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 公司绩效考核
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@RequestMapping("/performance_evaluation")
public class PerformanceEvaluationController extends BaseController {
    @Value("${wendao.teacher.path}")
    private String path;
    /**
     * 店铺业绩基础信息统计
     */
    @PostMapping("/queryBasicInfo")
    public JSONObject queryBasicInfo(@RequestBody PerformanceEvaluationDTO performanceEvaluationDTO) {
        String url = path + "/performance_evaluation/queryBasicInfo";
        String jsonString = JSON.toJSONString(performanceEvaluationDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 奖金员工页面出单店铺
     *
     * @param
     * @return
     */
    @GetMapping("/employeeBasicInfo")
    public JSONObject employeeBasicInfo(@RequestParam Map<String, String> map) {
        String uri = path + "/performance_evaluation/employeeBasicInfo";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, null);
        return JSON.parseObject(s);
    }
    /**
     * 奖金员工页面未出单店铺
     *
     * @param
     * @return
     */
    @GetMapping("/employeeBasicInfoNoOrder")
    public JSONObject employeeBasicInfoNoOrder(@RequestParam Map<String, String> map) {
        String uri = path + "/performance_evaluation/employeeBasicInfoNoOrder";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, null);
        return JSON.parseObject(s);
    }
}
