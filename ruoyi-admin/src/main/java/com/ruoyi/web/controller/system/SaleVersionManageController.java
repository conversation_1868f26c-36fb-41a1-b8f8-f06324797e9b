package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SaleVersionManage;
import com.ruoyi.system.service.ISaleVersionManageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 问到系统售卖版本管理Controller
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@RestController
@RequestMapping("/system/SaleVersionManageController")
public class SaleVersionManageController extends BaseController
{
    @Autowired
    private ISaleVersionManageService saleVersionManageService;

    /**
     * 查询问到系统售卖版本管理列表
     */
    //@PreAuthorize("@ss.hasPermi('system:manage:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(SaleVersionManage saleVersionManage)
    {
        startPage();
        List<SaleVersionManage> list = saleVersionManageService.selectSaleVersionManageList(saleVersionManage);
        return getDataTable(list);
    }

    /**
     * 获取问到系统售卖版本管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:manage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(saleVersionManageService.selectSaleVersionManageById(id));
    }

    /**
     * 新增问到系统售卖版本管理
     */
    //@PreAuthorize("@ss.hasPermi('system:manage:add')")
    @Log(title = "问到系统售卖版本管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SaleVersionManage saleVersionManage)
    {
        return toAjax(saleVersionManageService.insertSaleVersionManage(saleVersionManage));
    }

    /**
     * 修改问到系统售卖版本管理
     */
    //@PreAuthorize("@ss.hasPermi('system:manage:edit')")
    @Log(title = "问到系统售卖版本管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SaleVersionManage saleVersionManage)
    {
        //id必传
        if(saleVersionManage.getId()==null){
            return error("id必传");
        }
        SaleVersionManage saleVersionManage1 = saleVersionManageService.selectSaleVersionManageById(saleVersionManage.getId());
        //不能修改版本id
        if(saleVersionManage.getVersionId()!=null&& !Objects.equals(saleVersionManage1.getVersionId(), saleVersionManage.getVersionId())){
            return error("不能修改版本id");
        }
        return toAjax(saleVersionManageService.updateSaleVersionManage(saleVersionManage));
    }

    @Log(title = "修改售卖版本启用停用状态", businessType = BusinessType.UPDATE)
    @PostMapping("/changeEnableStatus")
    public AjaxResult changeEnableStatus(@RequestBody SaleVersionManage saleVersionManage)
    {
        //id必传
        if(saleVersionManage.getId()==null){
            return error("id必传");
        }
        if(saleVersionManage.getIsEnabled()==null){
            //启用或禁用状态必传
            return error("启用或禁用状态必传");
        }
        if(saleVersionManage.getIsEnabled()!=1&&saleVersionManage.getIsEnabled()!=0){
            return error("启用或禁用状态传值不正确,值为:0禁用,1启用");
        }
        SaleVersionManage saleVersionManageUpdate = new SaleVersionManage();
        saleVersionManageUpdate.setId(saleVersionManage.getId());
        saleVersionManageUpdate.setIsEnabled(saleVersionManage.getIsEnabled());
        return toAjax(saleVersionManageService.updateSaleVersionManage(saleVersionManageUpdate));
    }

//    @Log(title = "修改售卖版本启用停用状态", businessType = BusinessType.UPDATE)
    @GetMapping("/getMaxVersionId")
    public AjaxResult getMaxVersionId()
    {
        //id必传
        Integer maxId =saleVersionManageService.selectMaxVersionId();

        return AjaxResult.success("查询成功!",maxId);
    }
}
