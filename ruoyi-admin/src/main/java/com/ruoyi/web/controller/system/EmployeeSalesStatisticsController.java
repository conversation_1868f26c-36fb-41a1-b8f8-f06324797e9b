package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.system.domain.EmployeeSalesStatistics;
import com.ruoyi.system.service.IEmployeeSalesStatisticsService;
import com.ruoyi.web.dto.ClockInQuestSearchDTO;
import com.ruoyi.web.vo.ClockInQuestAllVO;
import com.ruoyi.web.vo.ClockInQuestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;

@RestController
@RequestMapping("/employee_sales_statistics")
public class EmployeeSalesStatisticsController {
    @Autowired
    private IEmployeeSalesStatisticsService employeeSalesStatisticsService;

    /**
     * 统计数据
     *
     * @param statType   'daily','weekly','monthly','yearly','allTime' 统计类型
     * @param personType 统计角度,person,team
     * @return
     */
    @GetMapping(value = "/getEmployeeSalesStatistics")
    public AjaxResult getEmployeeSalesStatistics(@RequestParam("statType") String statType, @RequestParam("personType") String personType) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if ("person".equalsIgnoreCase(personType)) {
            EmployeeSalesStatistics employeeSalesStatistics = employeeSalesStatisticsService.selectEmployeeSalesStatisticsByUserId(user.getUserId(), statType);
            return AjaxResult.success(employeeSalesStatistics);
        }
        if ("team".equalsIgnoreCase(personType)) {
            if (user.getUserId() == 111L) {
                EmployeeSalesStatistics employeeSalesStatistics = employeeSalesStatisticsService.selectEmployeeSalesStatisticsByParentLeaderId(user.getUserId(), statType);
                if (!"allTime".equals(statType)) {
                    EmployeeSalesStatistics employeeSalesStatistics1 = employeeSalesStatisticsService.selectEmployeeSalesStatisticsByParentLeaderIdAndDeptId(user.getUserId(), statType);
                    if (employeeSalesStatistics != null) {
                        if (employeeSalesStatistics1.getTotalSales() == null || employeeSalesStatistics1.getTotalSales().compareTo(BigDecimal.ZERO) <= 0) {
                            employeeSalesStatistics.setGrowthRate(new BigDecimal(100));
                        } else {
                            employeeSalesStatistics.setGrowthRate(employeeSalesStatistics.getTotalSales().divide(employeeSalesStatistics1.getTotalSales(), 2, RoundingMode.HALF_UP));
                        }
                    }
                } else {
                    employeeSalesStatistics.setGrowthRate(new BigDecimal(100));
                }
                return AjaxResult.success(employeeSalesStatistics);
            } else {
                EmployeeSalesStatistics employeeSalesStatistics = employeeSalesStatisticsService.selectEmployeeSalesStatisticsByLeaderId(user.getUserId(), statType);
                if (!"allTime".equals(statType)) {
                    EmployeeSalesStatistics employeeSalesStatistics1 = employeeSalesStatisticsService.selectEmployeeSalesStatisticsByLeaderIdAndDeptId(user.getUserId(), statType);
                    if (employeeSalesStatistics != null) {
                        if (employeeSalesStatistics1.getTotalSales() == null || employeeSalesStatistics1.getTotalSales().compareTo(BigDecimal.ZERO) <= 0) {
                            employeeSalesStatistics.setGrowthRate(new BigDecimal(100));
                        } else {
                            employeeSalesStatistics.setGrowthRate(employeeSalesStatistics.getTotalSales().divide(employeeSalesStatistics1.getTotalSales(), 2, RoundingMode.HALF_UP));
                        }
                    }
                } else {
                    employeeSalesStatistics.setGrowthRate(new BigDecimal(100));
                }
                return AjaxResult.success(employeeSalesStatistics);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 查询是否有团队
     *
     * @return
     */
    @GetMapping(value = "/queryHasTeam")
    public AjaxResult queryHasTeam() {
        boolean hasTeam = false;
        SysUser user = SecurityUtils.getLoginUser().getUser();
        int count = employeeSalesStatisticsService.queryHasTeam(user.getUserId());
        hasTeam = count > 0;
        return AjaxResult.success(hasTeam);
    }
}
