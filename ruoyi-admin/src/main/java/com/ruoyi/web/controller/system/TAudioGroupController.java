package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.TAudioGroupVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 音频分组Controller
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@RestController
@RequestMapping("/audio_group")
public class TAudioGroupController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 查询音频分组列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/audio_group/list";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        JSONObject jsonObject = JSON.parseObject(s);

        return jsonObject;
    }

    /**
     * 新增音频分组,限制为某个用户
     */
    @PostMapping
    public JSONObject add(@RequestBody TAudioGroupVO tAudioGroupVO,HttpServletRequest request) {
        String uri = path + "/audio_group";
        String teacherId = request.getHeader("Teacherid");
        String jsonString = JSON.toJSONString(tAudioGroupVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 修改音频分组
     */
    @PutMapping
    public JSONObject edit(@RequestBody TAudioGroupVO tAudioGroupVO,HttpServletRequest request) {
        String uri = path + "/audio_group";
        String teacherId = request.getHeader("Teacherid");
        String jsonString = JSON.toJSONString(tAudioGroupVO);
        return HttpClientPostFormUtil.putRequestBody(uri, jsonString, teacherId);
    }

}
