package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.PassedPublicClass;
import com.ruoyi.system.domain.PublicWendaoCertInfo;
import com.ruoyi.system.service.IPassedPublicClassService;
import com.ruoyi.system.service.IPublicWendaoCertInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 资质中心信息Controller
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/system/public_cert_info")
public class PublicWendaoCertInfoController extends BaseController
{
    @Autowired
    private IPublicWendaoCertInfoService publicWendaoCertInfoService;

    @Autowired
    private IPassedPublicClassService passedPublicClassService;

    /**
     * 查询资质中心信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PublicWendaoCertInfo publicWendaoCertInfo)
    {
        startPage();
        List<PublicWendaoCertInfo> list = publicWendaoCertInfoService.selectPublicWendaoCertInfoList(publicWendaoCertInfo);
        return getDataTable(list);
    }

    /**
     * 导出资质中心信息列表
     */
    @Log(title = "资质中心信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PublicWendaoCertInfo publicWendaoCertInfo)
    {
        List<PublicWendaoCertInfo> list = publicWendaoCertInfoService.selectPublicWendaoCertInfoList(publicWendaoCertInfo);
        ExcelUtil<PublicWendaoCertInfo> util = new ExcelUtil<PublicWendaoCertInfo>(PublicWendaoCertInfo.class);
        util.exportExcel(response, list, "资质中心信息数据");
    }

    /**
     * 获取资质中心信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(publicWendaoCertInfoService.selectPublicWendaoCertInfoById(id));
    }

    /**
     * 新增资质中心信息
     */
    @Log(title = "资质中心信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PublicWendaoCertInfo publicWendaoCertInfo)
    {
        int i = publicWendaoCertInfoService.insertPublicWendaoCertInfo(publicWendaoCertInfo);
        //需要将类目添加到passed_public_class
        PassedPublicClass passedPublicClass = new PassedPublicClass();
        BeanUtils.copyProperties(publicWendaoCertInfo,passedPublicClass);
        passedPublicClass.setCreateTime(new Date());
        passedPublicClass.setUpdateTime(new Date());
        passedPublicClass.setAuditStatus(0);
        passedPublicClass.setCertPublicName(publicWendaoCertInfo.getClassTeacherName()+"的资质");
        passedPublicClass.setEntityId(publicWendaoCertInfo.getEntityId());
        passedPublicClassService.insertPassedPublicClass(passedPublicClass);
        if(i>0){
            int row = publicWendaoCertInfoService.insertPublicWendaoCertInfoWendao(publicWendaoCertInfo);
            if(row<1){
                return error("添加失败");
            }
        }else{
            return error("添加失败");
        }
        return toAjax(i);
    }

    /**
     * 修改资质中心信息
     */
    @Log(title = "资质中心信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PublicWendaoCertInfo publicWendaoCertInfo)
    {
        publicWendaoCertInfo.setAuditStatus(0);
        publicWendaoCertInfo.setDouyinAuditMessage(null);
        publicWendaoCertInfo.setErrCode(null);
        publicWendaoCertInfo.setErrMsg(null);
        publicWendaoCertInfo.setEntityId(null);

        publicWendaoCertInfo.setBasicAuthTaskid(null);
        publicWendaoCertInfo.setClassAuthTaskid(null);
        return toAjax(publicWendaoCertInfoService.updatePublicWendaoCertInfo1(publicWendaoCertInfo));
    }

    /**
     * 删除资质中心信息
     */
    @Log(title = "资质中心信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(publicWendaoCertInfoService.deletePublicWendaoCertInfoByIds(ids));
    }
}
