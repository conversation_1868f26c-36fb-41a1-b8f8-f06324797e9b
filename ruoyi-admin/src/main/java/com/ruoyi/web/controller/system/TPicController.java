package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.ChangePicNameVO;
import com.ruoyi.web.vo.DocUploadFinishVO;
import com.ruoyi.web.vo.PicFinishVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;

/**
 * 素材视频Controller
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@RestController
@RequestMapping("/pic")
public class TPicController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 上传音频完毕后写入数据库记录
     *
     * @param picFinishVO
     * @return
     */
    @PostMapping("/upload_finish")
    public JSONObject uploadFinish(@RequestBody PicFinishVO picFinishVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/pic/upload_finish";
        String jsonString = JSON.toJSONString(picFinishVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 查询素材音频列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/pic/list";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        JSONObject jsonObject = JSON.parseObject(s);
        return jsonObject;
    }

    /**
     * 修改音频素材名称
     */
    @PutMapping("/change_pic_name")
    public JSONObject changeVideoName(@RequestBody ChangePicNameVO changePicNameVO,HttpServletRequest request) {
        String uri = path + "/pic/change_pic_name";
        String teacherId = request.getHeader("Teacherid");
        String jsonString = JSON.toJSONString(changePicNameVO);
        return HttpClientPostFormUtil.putRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 删除音频素材
     */
    @DeleteMapping("/{ids}")
    public JSONObject remove(@PathVariable Long[] ids,HttpServletRequest request) {
        String arrayAsString = Arrays.toString(ids);
        String result = arrayAsString.substring(1, arrayAsString.length() - 1).replaceAll("\\s", "");
        String uri = path + "/pic/"+ result;
        String teacherId = request.getHeader("Teacherid");
        return HttpClientPostFormUtil.deleteRequestBody(uri,teacherId);
    }

    /**
     * 文档上传完毕及转码请求
     *
     * @param docUploadFinishVO
     * @return
     */
    @PostMapping("/doc_upload_finish_transcode")
    public JSONObject docUploadFinishTranscode(@RequestBody DocUploadFinishVO docUploadFinishVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/pic/doc_upload_finish_transcode";
        String jsonString = JSON.toJSONString(docUploadFinishVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    @GetMapping("/query_doc_transcode")
    public JSONObject queryDocTranscode(@RequestParam("jobId") String jobId, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = path + "/pic/query_doc_transcode?jobId=" + jobId;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }

    /**
     * 文件转码回调
     *
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/doc_transcode_callback")
    public JSONObject docUploadFinishTranscode(HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/pic/doc_transcode_callback";
        String jsonString = JSON.toJSONString(request);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }
}
