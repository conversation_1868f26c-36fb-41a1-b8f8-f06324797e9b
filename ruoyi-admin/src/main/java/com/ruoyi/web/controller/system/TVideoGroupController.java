package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.TVideoGroupVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 视频分组Controller
 *
 * <AUTHOR>
 * @date 2023-07-25
 */
@RestController
@RequestMapping("/group")
public class TVideoGroupController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 查询视频分组列表
     */
    // @RequiresPermissions("teacher:group:list")
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/group/list";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        JSONObject jsonObject = JSON.parseObject(s);

        return jsonObject;
    }

    /**
     * 新增视频分组,限制为某个用户
     */
    @PostMapping
    public JSONObject add(@RequestBody TVideoGroupVO tVideoGroupVO,HttpServletRequest request) {
        String uri = path + "/group";
        String teacherId = request.getHeader("Teacherid");
        String jsonString = JSON.toJSONString(tVideoGroupVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 修改视频分组
     */

    @PutMapping
    public JSONObject edit(@RequestBody TVideoGroupVO tVideoGroupVO,HttpServletRequest request) {
        String uri = path + "/group";
        String teacherId = request.getHeader("Teacherid");
        String jsonString = JSON.toJSONString(tVideoGroupVO);
        return HttpClientPostFormUtil.putRequestBody(uri, jsonString, teacherId);
    }
}
