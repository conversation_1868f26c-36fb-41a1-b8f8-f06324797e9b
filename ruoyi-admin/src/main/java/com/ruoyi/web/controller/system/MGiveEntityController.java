package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.MGiveEntityAndAddVO;
import com.ruoyi.web.vo.MGiveEntityVO;
import com.ruoyi.web.vo.ShopGiveEntityVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/20
 */
@RestController
@RequestMapping("/give_entity")
public class MGiveEntityController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 根据店铺查询绑定课程附赠实物信息
     *
     * @param mGiveEntityVO
     * @param request
     * @return
     */
    @PostMapping("/list")
    public JSONObject list(@RequestBody MGiveEntityVO mGiveEntityVO, HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/give_entity/list";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mGiveEntityVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 查询店铺所拥有的课程和附赠实物数量
     *
     * @param map
     * @return
     */
    @GetMapping("/shop_give_entity")
    public JSONObject list(@RequestParam Map<String,String> map) {
        String uri = path + "/give_entity/shop_give_entity";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, null);
        return JSON.parseObject(s);
    }

    /**
     * 根据id删除添加的附赠实物信息
     *
     * @param id
     * @param request
     * @return
     */
    @DeleteMapping("/{id}")
    public JSONObject remove(@PathVariable Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/give_entity/" + id;
        String url = path + uri;
        return HttpClientPostFormUtil.deleteRequestBody(url, teacherId);
    }

    /**
     * 关联课程，添加
     */
    @PostMapping("/add")
    public JSONObject add(@RequestBody MGiveEntityAndAddVO mGiveEntityAndAddVO, HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/give_entity/add";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mGiveEntityAndAddVO);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,teacherId);
    }


    /**
     * 编辑课程附赠实物信息
     */
    @PostMapping("/update")
    public JSONObject edit(@RequestBody MGiveEntityAndAddVO mGiveEntityAndAddVO, HttpServletRequest request){
        String teacherId = request.getHeader("teacherId");
        String uri = "/give_entity/update";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mGiveEntityAndAddVO);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,teacherId);
    }


    /**
     * 获取附赠实物详细信息
     */
    @GetMapping(value ="/detail/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id,HttpServletRequest request){
        String teacherId = request.getHeader("teacherId");
        String uri = "/give_entity/detail/"+id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }


}
