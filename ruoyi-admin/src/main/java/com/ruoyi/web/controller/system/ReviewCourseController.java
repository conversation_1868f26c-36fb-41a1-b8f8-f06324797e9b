package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.ReviewCourse;
import com.ruoyi.web.domain.ReviewReply;
import com.ruoyi.web.vo.ReviewCoursePageVO;
import com.ruoyi.web.vo.ReviewCourseUpdateVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 课程评论Controller
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@RestController
@RequestMapping("/review_course")
public class ReviewCourseController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;

    /**
     * 获取课程评论列表
     */
    @PostMapping(value = "/getReviewCourse")
    public JSONObject getReviewCourseList(@RequestBody ReviewCoursePageVO reviewCoursePageVO) {

        String uri = path + "/review_course/getReviewCourse";
        String jsonString = JSON.toJSONString(reviewCoursePageVO);
        //没用到teacherId
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, reviewCoursePageVO.getTeacherId().toString());
    }

    /**
     * 修改课程评论
     *
     * @param reviewCourseUpdateVO
     * @return
     */
    @PostMapping(value = "/updateReviewCourse")
    public JSONObject updateReviewCourse(@RequestBody ReviewCourseUpdateVO reviewCourseUpdateVO) {
        String uri = path + "/review_course/updateReviewCourse";
        String jsonString = JSON.toJSONString(reviewCourseUpdateVO);
        //没用到teacherId
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, reviewCourseUpdateVO.getTeacherId().toString());
    }

    /**
     * 课程评论回复
     *
     * @param reviewReply
     * @return
     */
    @PostMapping(value = "/addReviewReply")
    public JSONObject addReviewReply(@RequestBody ReviewReply reviewReply) {
        String uri = path + "/review_course/addReviewReply";
        String jsonString = JSON.toJSONString(reviewReply);
        //没用到teacherId
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, reviewReply.getTeacherId().toString());
    }

    /**
     * 发表课程评论 List
     */
    @PostMapping("/addReviewCourseList")
    public JSONObject addReviewCourseList(@RequestBody List<ReviewCourse> reviewCourseList) {
        String uri = path + "/review_course/addReviewCourseList";
        String jsonString = JSON.toJSONString(reviewCourseList);
        //没用到teacherId
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, reviewCourseList.get(0).getTeacherId().toString());
    }
}
