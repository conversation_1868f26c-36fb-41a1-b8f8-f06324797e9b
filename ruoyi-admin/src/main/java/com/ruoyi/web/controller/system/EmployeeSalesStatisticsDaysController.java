package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.system.domain.EmployeeSalesStatisticsDays;
import com.ruoyi.system.service.IEmployeeSalesStatisticsDaysService;
import com.ruoyi.system.service.IEmployeeSalesStatisticsService;
import com.ruoyi.web.dto.ClockInQuestSearchDTO;
import com.ruoyi.web.vo.ClockInQuestAllVO;
import com.ruoyi.web.vo.ClockInQuestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/employee_sales_statistics_days")
public class EmployeeSalesStatisticsDaysController {
    @Autowired
    private IEmployeeSalesStatisticsDaysService employeeSalesStatisticsDaysService;

    /**
     * 员工的7日数据
     * @param personType 统计角度,person,team
     * @return
     */
    @GetMapping(value = "/getEmployeeSalesStatisticsDays")
    public AjaxResult getEmployeeSalesStatisticsDays(@RequestParam("personType") String personType) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if("person".equalsIgnoreCase(personType)){
            EmployeeSalesStatisticsDays employeeSalesStatisticsDays = new EmployeeSalesStatisticsDays();
            employeeSalesStatisticsDays.setUserId(user.getUserId());
            List<EmployeeSalesStatisticsDays> list = employeeSalesStatisticsDaysService.selectEmployeeSalesStatisticsDaysList(employeeSalesStatisticsDays);
            return AjaxResult.success(list);
        }
        if("team".equalsIgnoreCase(personType)){
            if(user.getUserId()==111L){
                EmployeeSalesStatisticsDays employeeSalesStatisticsDays = new EmployeeSalesStatisticsDays();
                employeeSalesStatisticsDays.setUserId(user.getUserId());
                List<EmployeeSalesStatisticsDays> list = employeeSalesStatisticsDaysService.selectEmployeeSalesStatisticsDaysListSumParentLeaderId(employeeSalesStatisticsDays);
                return AjaxResult.success(list);
            }else{
                EmployeeSalesStatisticsDays employeeSalesStatisticsDays = new EmployeeSalesStatisticsDays();
                employeeSalesStatisticsDays.setUserId(user.getUserId());
                List<EmployeeSalesStatisticsDays> list = employeeSalesStatisticsDaysService.selectEmployeeSalesStatisticsDaysListSumLeaderId(employeeSalesStatisticsDays);
                return AjaxResult.success(list);
            }
        }
        return AjaxResult.success();
    }
}
