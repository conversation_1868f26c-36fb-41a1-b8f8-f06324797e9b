package com.ruoyi.web.controller.system;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.system.domain.EnterInformation;
import com.ruoyi.system.domain.dto.EnterInformationStatDTO;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.dto.QueryStoreGroupDTO;
import com.ruoyi.web.vo.CourseOrderVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/employee_customer_query")
public class EmployeeCustomerQueryController {
    @Value("${wendao.order.path}")
    private String path;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IEnterInformationService enterInformationService;

    @PostMapping(value = "/queryOrderList")
    public JSONObject queryOrderList(@RequestBody CourseOrderVO courseOrderVO) {
        //限制用户
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<Long> userIds = new ArrayList<>();
        userIds.add(111L);
        if (!userIds.contains(user.getUserId())) {
            List<Long> teacherIds = new ArrayList<>();
            if (StringUtils.equals(user.getNickName(), user.getDept().getLeader())) {
                System.out.println("是主管:" + user.getNickName() + ",leader:" + user.getDept().getLeader() + ",部门:" + user.getDept().getDeptName());
                Long deptId = user.getDeptId();
                List<SysUser> deptUsers = userService.selectUserListByDeptId(deptId);
                if (CollectionUtils.isNotEmpty(deptUsers)) {
                    for (SysUser deptUser : deptUsers) {
                        List<Long> teacherIdList = fetchTeacherIdList(deptUser);
                        if (CollectionUtils.isNotEmpty(teacherIdList)) {
                            teacherIds.addAll(teacherIdList);
                        }
                    }
                }
            } else {
                teacherIds = fetchTeacherIdList(user);
            }
            if (CollectionUtils.isNotEmpty(teacherIds)) {
                courseOrderVO.setTeacherIds(teacherIds);
            }
        }
        if (Objects.nonNull(courseOrderVO.getTeacherId())) {
            Long teacherId = courseOrderVO.getTeacherId();
            String uri = "/course_order/selectAll";
            String url = path + uri;
            String jsonString = JSON.toJSONString(courseOrderVO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
        } else {
            String uri = "/course_order/selectAll";
            String url = path + uri;
            String jsonString = JSON.toJSONString(courseOrderVO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, "0");
        }
    }

    private List<Long> fetchTeacherIdList(SysUser user) {
        String nickName = user.getNickName();
        EnterInformation enterInformation = new EnterInformation();
        enterInformation.setAccountSpecialist(nickName);
        enterInformation.setAuditType(0);
        List<EnterInformation> enterInformations = enterInformationService.selectEnterInformationList(enterInformation);
        if (CollectionUtils.isNotEmpty(enterInformations)) {
            List<Long> teacherIds = new ArrayList<>();
            for (EnterInformation item : enterInformations) {
                if (item.getShopId() != null) {
                    teacherIds.add(item.getShopId());
                }
            }
            return teacherIds;
        }
        return Collections.emptyList();
    }

    @PostMapping(value = "/getCustomerData")
    public AjaxResult getCustomerData(@RequestBody QueryStoreGroupDTO queryStoreGroupDTO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String accountSpecialist = user.getNickName();
        //statType为'daily','weekly','monthly','yearly','allTime'
        Calendar calendar = getZeroTime();
        Date todayZero = calendar.getTime();
        //创建昨天0点0分0秒
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterdayZero = calendar.getTime();
        calendar = getZeroTime();
        //创建距离今日0点7天的时间
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date sevenDaysAgo = calendar.getTime();
        //创建这个月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisMonthOne = calendar.getTime();
        //创建本年1月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisYearOne = calendar.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = null;
        Date endTime = null;
        Date lastPeriodStartTime = null;
        Date lastPeriodEndTime = null;
        
        Map<String, Object> result = new HashMap<>();
        
        if ("daily".equals(queryStoreGroupDTO.getStatType())) {
            startTime = yesterdayZero;
            endTime = todayZero;
            // 上一个周期：前天到昨天
            Calendar prevDayCalendar = getZeroTime();
            prevDayCalendar.add(Calendar.DAY_OF_MONTH, -2);
            lastPeriodStartTime = prevDayCalendar.getTime();
            lastPeriodEndTime = yesterdayZero;
        }
        if ("weekly".equals(queryStoreGroupDTO.getStatType())) {
            startTime = sevenDaysAgo;
            endTime = todayZero;
            // 上一个周期：14天前到7天前
            Calendar prev7DaysCalendar = getZeroTime();
            prev7DaysCalendar.add(Calendar.DAY_OF_MONTH, -14);
            lastPeriodStartTime = prev7DaysCalendar.getTime();
            lastPeriodEndTime = sevenDaysAgo;
        }
        if ("monthly".equals(queryStoreGroupDTO.getStatType())) {
            startTime = thisMonthOne;
            endTime = todayZero;
            // 上一个周期：上个月1号到上个月最后一天
            Calendar lastMonthCalendar = getZeroTime();
            lastMonthCalendar.set(Calendar.DAY_OF_MONTH, 1);
            lastMonthCalendar.add(Calendar.MONTH, -1);
            lastPeriodStartTime = lastMonthCalendar.getTime();

            Calendar lastMonthEndCalendar = getZeroTime();
            lastMonthEndCalendar.set(Calendar.DAY_OF_MONTH, 1);
            lastMonthEndCalendar.add(Calendar.DAY_OF_MONTH, -1);
            lastPeriodEndTime = lastMonthEndCalendar.getTime();
        }
        if ("yearly".equals(queryStoreGroupDTO.getStatType())) {
            startTime = thisYearOne;
            endTime = todayZero;
        }
        if ("allTime".equals(queryStoreGroupDTO.getStatType())) {
            // 查询所有时间，startTime和endTime都为null
            startTime = null;
            endTime = null;
        }
        
        // 查询当前周期数据
        List<EnterInformationStatDTO> currentPeriodData = enterInformationService.queryByTimeAndSpecialist(
            startTime, endTime, accountSpecialist, 0);
        
        // 查询上个周期数据（本年和所有时间不需要比较）
        List<EnterInformationStatDTO> lastPeriodData = null;
        if (!"yearly".equals(queryStoreGroupDTO.getStatType()) && !"allTime".equals(queryStoreGroupDTO.getStatType())) {
            lastPeriodData = enterInformationService.queryByTimeAndSpecialist(
                lastPeriodStartTime, lastPeriodEndTime, accountSpecialist, 0);
        }
        
        // 计算当前周期统计数据
        Map<String, Object> currentStats = calculateStatistics(currentPeriodData);
        
        // 计算上个周期统计数据
        Map<String, Object> lastStats = null;
        if (lastPeriodData != null) {
            lastStats = calculateStatistics(lastPeriodData);
        }
        
        // 计算增长率
        result.put("openAccountCount", currentStats.get("totalCount"));
        result.put("openAccountAmount", currentStats.get("totalAmount"));
        result.put("openAccountAmountYuan", ((Long)currentStats.get("totalAmount")) / 100.0);
        result.put("functionalOpenCount", currentStats.get("functionalCount"));
        result.put("functionalAmount", currentStats.get("functionalAmount"));
        
        if (lastStats != null) {
            // 开户数量增长率
            Long currentCount = (Long) currentStats.get("totalCount");
            Long lastCount = (Long) lastStats.get("totalCount");
            double countGrowth = lastCount > 0 ? ((currentCount - lastCount) * 100.0 / lastCount) : 0;
            result.put("openAccountCountGrowth", String.format("%.1f%%", countGrowth));
            
            // 开户金额增长率
            Long currentAmount = (Long) currentStats.get("totalAmount");
            Long lastAmount = (Long) lastStats.get("totalAmount");
            double amountGrowth = lastAmount > 0 ? ((currentAmount - lastAmount) * 100.0 / lastAmount) : 0;
            result.put("openAccountAmountGrowth", String.format("%.1f%%", amountGrowth));
            
            // 功能开通数量增长率
            Long currentFuncCount = (Long) currentStats.get("functionalCount");
            Long lastFuncCount = (Long) lastStats.get("functionalCount");
            double funcCountGrowth = lastFuncCount > 0 ? ((currentFuncCount - lastFuncCount) * 100.0 / lastFuncCount) : 0;
            result.put("functionalOpenCountGrowth", String.format("%.1f%%", funcCountGrowth));
            
            // 功能金额增长率
            Long currentFuncAmount = (Long) currentStats.get("functionalAmount");
            Long lastFuncAmount = (Long) lastStats.get("functionalAmount");
            double funcAmountGrowth = lastFuncAmount > 0 ? ((currentFuncAmount - lastFuncAmount) * 100.0 / lastFuncAmount) : 0;
            result.put("functionalAmountGrowth", String.format("%.1f%%", funcAmountGrowth));
        } else {
            result.put("openAccountCountGrowth", "+0.0%");
            result.put("openAccountAmountGrowth", "+0.0%");
            result.put("functionalOpenCountGrowth", "+0.0%");
            result.put("functionalAmountGrowth", "+0.0%");
        }
        
        // 版本分布统计
        result.put("basicVersionCount", currentStats.get("basicCount"));
        result.put("basicVersionAmount", ((Long)currentStats.get("basicAmount")) / 100.0);
        result.put("standardVersionCount", currentStats.get("standardCount"));
        result.put("standardVersionAmount", ((Long)currentStats.get("standardAmount")) / 100.0);
        result.put("professionalVersionCount", currentStats.get("professionalCount"));
        result.put("professionalVersionAmount", ((Long)currentStats.get("professionalAmount")) / 100.0);
        result.put("flagshipVersionCount", currentStats.get("flagshipCount"));
        result.put("flagshipVersionAmount", ((Long)currentStats.get("flagshipAmount")) / 100.0);
        
        // 功能开通统计
        result.put("giftCourseCount", currentStats.get("giftCourseCount"));
        result.put("giftCourseAmount", ((Long)currentStats.get("giftCourseAmount")) / 100.0);
        result.put("h5LiveCount", currentStats.get("h5LiveCount"));
        result.put("h5LiveAmount", ((Long)currentStats.get("h5LiveAmount")) / 100.0);
        result.put("promoterCount", currentStats.get("promoterCount"));
        result.put("promoterAmount", ((Long)currentStats.get("promoterAmount")) / 100.0);
        
        return AjaxResult.success(result);
    }
    
    private Map<String, Object> calculateStatistics(List<EnterInformationStatDTO> data) {
        Map<String, Object> stats = new HashMap<>();
        
        long totalCount = 0;
        long totalAmount = 0;
        long functionalCount = 0;
        long functionalAmount = 0;
        
        // 版本统计
        long basicCount = 0, basicAmount = 0;
        long standardCount = 0, standardAmount = 0;
        long professionalCount = 0, professionalAmount = 0;
        long flagshipCount = 0, flagshipAmount = 0;
        
        // 功能统计
        long giftCourseCount = 0, giftCourseAmount = 0;
        long h5LiveCount = 0, h5LiveAmount = 0;
        long promoterCount = 0, promoterAmount = 0;
        
        for (EnterInformationStatDTO record : data) {
            Integer version = record.getVersion();
            totalCount++;
            
            // 版本价格计算
            long versionPrice = 0;
            if (version == 11) { // 基础版
                versionPrice = 99900; // 999元，单位分
                basicCount++;
                basicAmount += versionPrice;
            } else if (version == 15) { // 标准版
                versionPrice = 299900; // 2999元
                standardCount++;
                standardAmount += versionPrice;
            } else if (version == 19) { // 专业版
                versionPrice = 699900; // 6999元
                professionalCount++;
                professionalAmount += versionPrice;
            } else if (version == 14) { // 旗舰版
                versionPrice = 999900; // 9999元
                flagshipCount++;
                flagshipAmount += versionPrice;
            }
            totalAmount += versionPrice;
            
            // 功能价格计算（这里需要根据实际功能价格调整）
            long funcPrice = 0;
            
            // 赠课功能
            Integer giftCourse = record.getGiftCourse();
            if (giftCourse != null && giftCourse == 1) {
                giftCourseCount++;
                long giftCoursePrice = 299900; // 假设500元
                giftCourseAmount += giftCoursePrice;
                funcPrice += giftCoursePrice;
                functionalCount++;
            }
            
            // h5直播功能
            Integer wapLiveOpen = record.getWapLiveOpen();
            if (wapLiveOpen != null && wapLiveOpen == 1) {
                h5LiveCount++;
                long h5LivePrice = 299900; // 假设1000元
                h5LiveAmount += h5LivePrice;
                funcPrice += h5LivePrice;
                functionalCount++;
            }
            
            // 推广员功能
            Integer openPromoter = record.getOpenPromoter();
            Integer promoterNum = record.getPromoterNum();
            if (openPromoter != null && openPromoter == 1 && promoterNum != null && promoterNum > 5) {
                promoterCount++;
                long promoterPrice = 299900; // 假设2000元
                promoterAmount += promoterPrice;
                funcPrice += promoterPrice;
                functionalCount++;
            }
            
            functionalAmount += funcPrice;
        }
        
        stats.put("totalCount", totalCount);
        stats.put("totalAmount", totalAmount);
        stats.put("functionalCount", functionalCount);
        stats.put("functionalAmount", functionalAmount);
        
        stats.put("basicCount", basicCount);
        stats.put("basicAmount", basicAmount);
        stats.put("standardCount", standardCount);
        stats.put("standardAmount", standardAmount);
        stats.put("professionalCount", professionalCount);
        stats.put("professionalAmount", professionalAmount);
        stats.put("flagshipCount", flagshipCount);
        stats.put("flagshipAmount", flagshipAmount);
        
        stats.put("giftCourseCount", giftCourseCount);
        stats.put("giftCourseAmount", giftCourseAmount);
        stats.put("h5LiveCount", h5LiveCount);
        stats.put("h5LiveAmount", h5LiveAmount);
        stats.put("promoterCount", promoterCount);
        stats.put("promoterAmount", promoterAmount);
        
        return stats;
    }

    private static Calendar getZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }
}
