package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/qrcode")
public class QrcodeController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 生成二维码
     * 必要的时候可以解码再编码生成自己的二维码图片
     * @return
     */
//    @RequiresPermissions("teacher:qrcode:create")
    @PostMapping("/create")
    public JSONObject create(@RequestParam("courseId") Long courseId) {
        String uri = "/qrcode/create?courseId=" + courseId;
        String url = path + uri;
        String s = HttpClientPostFormUtil.sendPost(url, courseId.toString());
        JSONObject jsonObject = JSONObject.parseObject(s);
        return jsonObject;
    }

}
