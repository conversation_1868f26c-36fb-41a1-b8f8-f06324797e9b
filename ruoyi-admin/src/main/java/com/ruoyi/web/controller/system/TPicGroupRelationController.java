package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.AddPicGroupRelationVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * 图片和分组关系Controller
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@RestController
@RequestMapping("/pic_group_relation")
public class TPicGroupRelationController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 新增图片和分组关系
     */
    @PostMapping
    public JSONObject add(@RequestBody AddPicGroupRelationVO addPicGroupRelationVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/pic_group_relation";
        String jsonString = JSON.toJSONString(addPicGroupRelationVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 将图片从分组中移除
     *
     * @param addPicGroupRelationVO
     * @return
     */
    @PostMapping("remove_from_group")
    public JSONObject removeFromGroup(@RequestBody AddPicGroupRelationVO addPicGroupRelationVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/pic_group_relation/remove_from_group";
        String jsonString = JSON.toJSONString(addPicGroupRelationVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }
}
