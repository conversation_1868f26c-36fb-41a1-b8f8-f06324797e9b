package com.ruoyi.web.controller.system;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.ReviewCourseDraft;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

/**
 * 课程评论草稿Controller
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@RestController
@RequestMapping("/review_course_draft")
public class ReviewCourseDraftController extends BaseController {
    @Value("${wendao.order.path}")
    private String path;

    /**
     * 查询课程评论草稿列表
     */
    @GetMapping("/getReviewCourseDraftList")
    public JSONObject list(@RequestParam("number") Integer number) {
        String uri = path + "/review_course_draft/getReviewCourseDraftList?number=" + number;
        String s = HttpUtil.get(uri);
        JSONObject jsonObject = JSON.parseObject(s);
        return jsonObject;
    }

    /**
     * 修改课程评论草稿
     */
    @PostMapping("/reviewCourseDraft")
    public JSONObject updateReviewCourseDraft(@RequestBody ReviewCourseDraft reviewCourseDraft) {
        String uri = path + "/review_course_draft/reviewCourseDraft";
        String jsonString = JSON.toJSONString(reviewCourseDraft);
        //没用到teacherId
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, "");
    }
}
