package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.CoursePromoterDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/course_promoter")
public class CoursePromoterController extends BaseController {


    @Value("${wendao.teacher.path}")
    private String path;


    /**
     * 查询所有店铺推广员基本信息
     * @param coursePromoterDTO
     * @return
     */
    @PostMapping("/listAll")
    public JSONObject selectPromoterAll(@RequestBody CoursePromoterDTO coursePromoterDTO) {
        String uri = "/course_promoter/listAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(coursePromoterDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


}
