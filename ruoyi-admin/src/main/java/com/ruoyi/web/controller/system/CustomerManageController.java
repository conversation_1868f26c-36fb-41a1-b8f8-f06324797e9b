package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.CustomerManageVO;
import com.ruoyi.web.vo.UserManagementVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/18
 */

@RestController
@RequestMapping("/manage")
public class CustomerManageController extends BaseController {


    @Value("${wendao.teacher.path}")
    private String path;


    /**
     * 根据店铺查询绑定用户信息
     * @param customerManageVO
     * @param request
     * @return
     */
    @PostMapping("/list")
    public JSONObject list(@RequestBody CustomerManageVO customerManageVO,HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/manage/list";
        String url = path + uri;
        String jsonString = JSON.toJSONString(customerManageVO);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,teacherId);
    }


    @PostMapping("/userControl")
    public JSONObject userControl(@RequestBody UserManagementVO userManagementVO) {
        String uri = "/manage/userControl";
        String url = path + uri;
        String jsonString = JSON.toJSONString(userManagementVO);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,null);
    }


}
