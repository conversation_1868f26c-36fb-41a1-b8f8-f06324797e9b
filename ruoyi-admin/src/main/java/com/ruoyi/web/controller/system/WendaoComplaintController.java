package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.WendaoCreateRefundDTO;
import com.ruoyi.web.vo.WendaoComplaintVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/8
 */
@RestController
@RequestMapping("/complaint")
public class WendaoComplaintController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;

    @Autowired
    private RedisCache redisCache;

    private static final String PLATFORM_REFUND_FROM_Complaint = "platform_refund_from_Complaint:";


    @PostMapping("/list")
    public JSONObject list(@RequestBody WendaoComplaintVo wendaoComplaint) {
        String uri = "/complaint/list";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoComplaint);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


    /**
     * 获取退款信息详细信息
     */
    @GetMapping(value = "/detail/{complaintId}")
    public JSONObject getInfo(@PathVariable("complaintId") Integer complaintId) {
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/complaint/detail/" + complaintId;
        String url = path + uri;
        JSONObject jsonObject = JSON.parseObject(HttpClientPostFormUtil.sendGet(url, teacherId));
        return jsonObject;
    }


    /**
     * 线下沟通退款
     *
     * @param wendaoComplaintVo
     * @return
     */
    @PostMapping("/updateDesc")
    public JSONObject addDesc(@RequestBody WendaoComplaintVo wendaoComplaintVo) {
        Long teacherId = wendaoComplaintVo.getTeacherId();
        String uri = "/complaint/updateDesc";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoComplaintVo);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }

    /**
     * 线下退款
     *
     * @param wendaoComplaintVo
     * @return
     */
    @PostMapping("/updateAlipay")
    public JSONObject updateAlipay(@RequestBody WendaoComplaintVo wendaoComplaintVo) {
        Long teacherId = wendaoComplaintVo.getTeacherId();
        String uri = "/complaint/updateAlipay";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoComplaintVo);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }


    /**
     * 修改投诉人的支付宝账号信息
     *
     * @param wendaoComplaintVo
     * @return
     */
    @PostMapping("/updateOffline")
    public JSONObject updateOffline(@RequestBody WendaoComplaintVo wendaoComplaintVo) {
        Long teacherId = wendaoComplaintVo.getTeacherId();
        String uri = "/complaint/updateOffline";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoComplaintVo);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }


    /**
     * 修改投诉人的支付宝账号信息
     *
     * @param wendaoComplaintVo
     * @return
     */
    @PostMapping("/resubmit")
    public JSONObject resubmit(@RequestBody WendaoComplaintVo wendaoComplaintVo) {
        Long teacherId = wendaoComplaintVo.getTeacherId();
        String uri = "/complaint/resubmit";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoComplaintVo);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }


    /**
     * 线上退款
     *
     * @param wendaoComplaintVo
     * @return
     */
    @PostMapping("/onlineRefund")
    public JSONObject onlineRefund(@RequestBody WendaoComplaintVo wendaoComplaintVo) {
        redisCache.setCacheObject(PLATFORM_REFUND_FROM_Complaint + wendaoComplaintVo.getOrderId(), "Complaint_refund",3,TimeUnit.DAYS);
        if(wendaoComplaintVo.getSource()==0){
            String uri = "/dy_trade_system_controller/create_refund";
            String url = path + uri;
            String orderId = wendaoComplaintVo.getOrderId();
            WendaoCreateRefundDTO wendaoCreateRefundDTO = new WendaoCreateRefundDTO();
            wendaoCreateRefundDTO.setOrderId(orderId);
            String jsonString = JSON.toJSONString(wendaoCreateRefundDTO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
        }else{
            //请求/order_refund_review/complaintRefund
            String uri = "/order_refund_review/complaintRefund";
            String url = path + uri;
            String orderId = wendaoComplaintVo.getOrderId();
            WendaoCreateRefundDTO wendaoCreateRefundDTO = new WendaoCreateRefundDTO();
            wendaoCreateRefundDTO.setOrderId(orderId);
            String jsonString = JSON.toJSONString(wendaoCreateRefundDTO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
        }

    }


}
