package com.ruoyi.web.controller.system;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.dto.DeptRelation;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.IEmployeeTeacherStatisticsService;
import com.ruoyi.system.domain.EmployeeTeacherStatistics;
import com.ruoyi.web.dto.QueryStoreGroupDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/employee_store_query")
public class EmployeeStoreQueryController {
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IEmployeeTeacherStatisticsService employeeTeacherStatisticsService;
    @Autowired
    private ISysUserService userService;

    private static final Long manager_id = 111L;

    @PostMapping(value = "/getStoreData")
    public AjaxResult getStoreData(@RequestBody QueryStoreGroupDTO queryStoreGroupDTO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<Long> deptIds = queryStoreGroupDTO.getDeptIds();
        List<Long> userIds = new ArrayList<>();
        for (Long deptId : deptIds) {
            List<SysUser> sysUsers = sysUserService.selectUserListByDeptId(deptId);
            for (SysUser sysUser : sysUsers) {
                userIds.add(sysUser.getUserId());
            }
        }
        //如果是经理
        if (user.getUserId().equals(manager_id) && !userIds.contains(manager_id) && deptIds.size() > 1) {
            userIds.add(manager_id);
        }
        //商务专员只查自己的数据
        List<SysUser> sysUsers = userService.selectUserListByDeptId(104L);
        sysUsers.stream().filter(sysUser -> sysUser.getUserId().equals(user.getUserId()) && StringUtils.equals("商务专员", sysUser.getPostName())).findFirst().ifPresent(sysUser -> {
            userIds.clear();
            userIds.add(sysUser.getUserId());
        });

        //statType为'daily','weekly','monthly','yearly','allTime'
        Calendar calendar = getZeroTime();
        Date todayZero = calendar.getTime();
        //创建昨天0点0分0秒
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterdayZero = calendar.getTime();
        calendar = getZeroTime();
        //创建距离今日0点7天的时间
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date sevenDaysAgo = calendar.getTime();
        //创建这个月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisMonthOne = calendar.getTime();
        //创建本年1月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisYearOne = calendar.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeQueryStr = null;
        String lastPeriodTimeQueryStr = null;

        if ("daily".equals(queryStoreGroupDTO.getStatType())) {
            timeQueryStr = sdf.format(yesterdayZero) + "~" + sdf.format(todayZero);
            // 上一个周期：前天到昨天
            Calendar prevDayCalendar = getZeroTime();
            prevDayCalendar.add(Calendar.DAY_OF_MONTH, -2);
            Date dayBeforeYesterday = prevDayCalendar.getTime();
            lastPeriodTimeQueryStr = sdf.format(dayBeforeYesterday) + "~" + sdf.format(yesterdayZero);
        }
        if ("weekly".equals(queryStoreGroupDTO.getStatType())) {
            timeQueryStr = sdf.format(sevenDaysAgo) + "~" + sdf.format(todayZero);
            // 上一个周期：14天前到7天前
            Calendar prev7DaysCalendar = getZeroTime();
            prev7DaysCalendar.add(Calendar.DAY_OF_MONTH, -14);
            Date fourteenDaysAgo = prev7DaysCalendar.getTime();
            lastPeriodTimeQueryStr = sdf.format(fourteenDaysAgo) + "~" + sdf.format(sevenDaysAgo);
        }
        if ("monthly".equals(queryStoreGroupDTO.getStatType())) {
            timeQueryStr = sdf.format(thisMonthOne) + "~" + sdf.format(todayZero);
            // 上一个周期：上个月1号到上个月最后一天
            Calendar lastMonthCalendar = getZeroTime();
            lastMonthCalendar.set(Calendar.DAY_OF_MONTH, 1);
            lastMonthCalendar.add(Calendar.MONTH, -1);
            Date lastMonthOne = lastMonthCalendar.getTime();

            Calendar lastMonthEndCalendar = getZeroTime();
            lastMonthEndCalendar.set(Calendar.DAY_OF_MONTH, 1);
            lastMonthEndCalendar.add(Calendar.DAY_OF_MONTH, -1);
            Date lastMonthEnd = lastMonthEndCalendar.getTime();
            lastPeriodTimeQueryStr = sdf.format(lastMonthOne) + "~" + sdf.format(lastMonthEnd);
        }
        if ("yearly".equals(queryStoreGroupDTO.getStatType())) {
            timeQueryStr = sdf.format(thisYearOne) + "~" + sdf.format(todayZero);
        }

        // 查询员工老师数据统计
        // 获取按交易金额排序的前20个记录
        List<EmployeeTeacherStatistics> top20List = employeeTeacherStatisticsService.selectTop20ByEmployeeIdsAndTimeQueryStr(userIds, timeQueryStr);

        // 计算增长率（仅对daily、weekly、monthly）
        if (lastPeriodTimeQueryStr != null && !top20List.isEmpty()) {
            // 提取teacherId列表
            List<Long> teacherIds = new ArrayList<>();
            for (EmployeeTeacherStatistics item : top20List) {
                teacherIds.add(item.getTeacherId());
            }

            // 查询上一个周期的数据
            List<EmployeeTeacherStatistics> lastPeriodList = employeeTeacherStatisticsService.selectByTeacherIdsAndTimeQueryStr(teacherIds, lastPeriodTimeQueryStr);

            // 创建上一个周期数据的Map，以teacherId为key
            Map<Long, BigDecimal> lastPeriodAmountMap = new HashMap<>();
            for (EmployeeTeacherStatistics lastItem : lastPeriodList) {
                lastPeriodAmountMap.put(lastItem.getTeacherId(), lastItem.getDealAmount());
            }

            // 计算增长率
            for (EmployeeTeacherStatistics currentItem : top20List) {
                BigDecimal currentAmount = currentItem.getDealAmount();
                BigDecimal lastAmount = lastPeriodAmountMap.get(currentItem.getTeacherId());

                if (currentAmount != null && lastAmount != null && lastAmount.compareTo(BigDecimal.ZERO) > 0) {
                    // 增长率 = (当前周期金额 - 上一周期金额) / 上一周期金额 * 100
                    BigDecimal growthRate = currentAmount.subtract(lastAmount)
                            .divide(lastAmount, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    currentItem.setGrowRatio(growthRate);
                } else if (currentAmount != null && (lastAmount == null || lastAmount.compareTo(BigDecimal.ZERO) == 0)) {
                    // 如果上一周期没有数据或为0，则增长率为100%
                    currentItem.setGrowRatio(new BigDecimal("100"));
                } else {
                    // 其他情况增长率为0
                    currentItem.setGrowRatio(BigDecimal.ZERO);
                }
            }
        }

        // 统计店铺总数
        Integer totalShops = employeeTeacherStatisticsService.countTotalShops(userIds, timeQueryStr);

        // 统计有交易的店铺数
        Integer shopsWithDeal = employeeTeacherStatisticsService.countShopsWithDealAmount(userIds, timeQueryStr);

        // 计算总销售额
        BigDecimal totalDealAmount = employeeTeacherStatisticsService.sumTotalDealAmount(userIds, timeQueryStr);

        // 计算平均销售额
        BigDecimal averageDealAmount = employeeTeacherStatisticsService.getAverageDealAmount(userIds, timeQueryStr);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("top20List", top20List);
        result.put("totalShops", totalShops);
        result.put("shopsWithDeal", shopsWithDeal);
        result.put("totalDealAmount", totalDealAmount);
        result.put("averageDealAmount", averageDealAmount);
        result.put("timeQueryStr", timeQueryStr);
        result.put("lastPeriodTimeQueryStr", lastPeriodTimeQueryStr);

        //这个是经理的逻辑 查找deptId为104的子部门
        if (user.getUserId().equals(manager_id)) {
            DeptRelation deptRelation = sysDeptService.selectBusinessDeptAndChildren();
            if (deptRelation != null && !deptRelation.getChildren().isEmpty()) {
                // 获取子部门列表
                List<SysDept> childrenDepts = deptRelation.getChildren();
                // 按部门分组存储员工userId
                Map<String, List<Long>> deptUserIdsMap = new HashMap<>();
                // 按部门统计销售额
                Map<String, BigDecimal> deptSalesMap = new HashMap<>();
                // 遍历子部门
                for (SysDept dept : childrenDepts) {
                    // 查询部门下的员工
                    List<SysUser> users = sysUserService.selectUserListByDeptId(dept.getDeptId());
                    List<Long> userIdList = new ArrayList<>();
                    // 提取员工userId
                    for (SysUser userItem : users) {
                        userIdList.add(userItem.getUserId());
                    }
                    // 将员工userId按deptName分组
                    deptUserIdsMap.put(dept.getDeptName(), userIdList);
                    // 按deptName查询部门的总销售额
                    BigDecimal deptTotalSales = employeeTeacherStatisticsService.sumTotalDealAmount(userIdList, timeQueryStr);
                    deptSalesMap.put(dept.getDeptName(), deptTotalSales);
                }
                result.put("deptUserIds", deptUserIdsMap);
                result.put("deptSales", deptSalesMap);
            }
        } else {
            DeptRelation deptRelation = sysDeptService.selectBusinessDeptAndChildren();
            if (deptRelation != null && !deptRelation.getChildren().isEmpty()) {
                String finalTimeQueryStr = timeQueryStr;
                deptRelation.getChildren().stream().filter(dept -> StringUtils.equals(dept.getLeader(), user.getNickName())).findFirst().ifPresent(dept -> {
                    Map<String, List<Long>> deptUserIdsMap = new HashMap<>();
                    Map<String, BigDecimal> deptSalesMap = new HashMap<>();
                    List<SysUser> users = sysUserService.selectUserListByDeptId(dept.getDeptId());
                    List<Long> userIdList = new ArrayList<>();
                    for (SysUser userItem : users) {
                        userIdList.add(userItem.getUserId());
                        BigDecimal personTotalSales = employeeTeacherStatisticsService.sumTotalDealAmount(Collections.singletonList(userItem.getUserId()), finalTimeQueryStr);
                        deptSalesMap.put(userItem.getNickName(), personTotalSales);
                    }
                    deptUserIdsMap.put(dept.getDeptName(), userIdList);
                    result.put("deptUserIds", deptUserIdsMap);
                    result.put("deptSales", deptSalesMap);
                });
            }
        }
        return AjaxResult.success(result);
    }

    private static Calendar getZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    @GetMapping(value = "/queryGroupList")
    public AjaxResult queryGroupList() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysUser sysUser = sysUserService.selectUserById(user.getUserId());
        if (user.getUserId() == 111L) {
            //返回全部小组
            DeptRelation deptRelation = sysDeptService.selectBusinessDeptAndChildren();
            List<SysDept> children = deptRelation.getChildren();
            return AjaxResult.success(children);
        } else {
            //返回个人小组
            SysDept dept = sysUser.getDept();
            return AjaxResult.success(Collections.singleton(dept));
        }
    }
}
