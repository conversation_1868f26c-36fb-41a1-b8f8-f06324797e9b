package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.WendaoCertInfo;
import com.ruoyi.system.service.IWendaoCertInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 资质中心信息Controller
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/system/cert_info")
public class WendaoCertInfoController extends BaseController {
    @Value("${wendao.teacher.path}")
    private String path;

    @Autowired
    private IWendaoCertInfoService wendaoCertInfoService;
    /**
     * 查询资质中心信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(WendaoCertInfo wendaoCertInfo) {
        startPage();
        List<WendaoCertInfo> list = wendaoCertInfoService.selectWendaoCertInfoBySearchValue(wendaoCertInfo);

        return getDataTable(list);
    }

    /**
     * 导出资质中心信息列表
     */
    @Log(title = "资质中心信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WendaoCertInfo wendaoCertInfo) {
        List<WendaoCertInfo> list = wendaoCertInfoService.selectWendaoCertInfoList(wendaoCertInfo);
        ExcelUtil<WendaoCertInfo> util = new ExcelUtil<WendaoCertInfo>(WendaoCertInfo.class);
        util.exportExcel(response, list, "资质中心信息数据");
    }

    /**
     * 获取资质中心信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wendaoCertInfoService.selectWendaoCertInfoById(id));
    }

    /**
     * 新增资质中心信息
     */
    @Log(title = "资质中心信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WendaoCertInfo wendaoCertInfo) {
        //发送消息
        int i = wendaoCertInfoService.insertWendaoCertInfo(wendaoCertInfo);
        return toAjax(i);
    }

    @Log(title = "资质中心信息", businessType = BusinessType.INSERT)
    @PostMapping("/public")
    public AjaxResult addPublic(@RequestBody WendaoCertInfo wendaoCertInfo) {
        return toAjax(wendaoCertInfoService.insertWendaoCertInfo(wendaoCertInfo));
    }

    /**
     * 修改资质中心信息
     */
    @Log(title = "资质中心信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WendaoCertInfo wendaoCertInfo) {
        wendaoCertInfo.setAuditStatus(0);
        wendaoCertInfo.setDouyinAuditMessage(null);
        wendaoCertInfo.setBasicAuthTaskid(null);
        wendaoCertInfo.setClassAuthTaskid(null);
        wendaoCertInfo.setErrCode(null);
        wendaoCertInfo.setErrMsg(null);
        wendaoCertInfo.setEntityId(null);
        return toAjax(wendaoCertInfoService.updateWendaoCertInfo1(wendaoCertInfo));
    }

    /**
     * 删除资质中心信息
     */
    @Log(title = "资质中心信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wendaoCertInfoService.deleteWendaoCertInfoByIds(ids));
    }

    /**
     * 查询审核通过的并且授权角色了的
     *
     * @return
     */
    @PostMapping("/passedQualificationList")
    public JSONObject passedQualificationList(HttpServletRequest request,@RequestParam("appNameType") Integer appNameType) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/wendao_cert_info/passedQualificationList";
        String url = path + uri;
        return HttpClientPostFormUtil.postRequestBody(url, teacherId, teacherId);
    }

    @GetMapping("/queryUserList")
    public JSONObject queryUserList(@RequestParam Map<String, String> map, HttpServletRequest request) {
        //        Long teacherId = SecurityUtils.getUserId();
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/wendao_cert_info/queryUserList";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        return JSON.parseObject(s);
    }


    /**
     * 解绑抖音号绑定
     *
     * @return
     */
    @GetMapping("/unbindCert")
    public JSONObject unbindCert(@RequestParam Map<String, String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/wendao_cert_info/unbindCert";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        return JSON.parseObject(s);
    }

    /**
     * 直播挂载
     * @return
     */
    @GetMapping("/apply/imBinding")
    public JSONObject applyLive(@RequestParam Map<String, String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/wendao_cert_info/apply/imBinding";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        return JSON.parseObject(s);
    }

    /**
     * 抖音号发起直播短视频挂载
     * @return
     */
    @GetMapping("/apply/shortWithDouyinhao")
    public JSONObject shortWithDouyinhao(@RequestParam Map<String, String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/wendao_cert_info/apply/shortWithDouyinhao";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        return JSON.parseObject(s);
    }


    /**
     * 短视频挂载
     * @return
     */
    @GetMapping("/apply/short")
    public JSONObject applyShort(@RequestParam Map<String, String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/wendao_cert_info/apply/short";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        return JSON.parseObject(s);
    }
    /**
     * 获取挂载能力二维码并授权
     * @return
     */
    @GetMapping("/createQualificationQrCode")
    public JSONObject createQualificationQrCode(@RequestParam Map<String, String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/wendao_cert_info/createQualificationQrCode";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        return JSON.parseObject(s);
    }

    /**
     * 获取资质中心信息详细信息
     */
    @GetMapping(value = "/info")
    public JSONObject getInfo(@RequestParam Map<String, String> map, HttpServletRequest request)
    {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/wendao_cert_info/info";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        return JSON.parseObject(s);
    }
}
