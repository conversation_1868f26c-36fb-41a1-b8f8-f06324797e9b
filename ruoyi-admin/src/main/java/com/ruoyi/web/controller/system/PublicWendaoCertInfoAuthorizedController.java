package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import com.ruoyi.web.dto.TeacherAuthorizedDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 公共资质授权Controller
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/system/public_authorized")
public class PublicWendaoCertInfoAuthorizedController extends BaseController
{
    @Autowired
    private IPublicWendaoCertInfoAuthorizedService publicWendaoCertInfoAuthorizedService;

    @Autowired
    private IPublicWendaoCertInfoService publicWendaoCertInfoService;

    @Autowired
    private IEnterInformationService enterInformationService;

    @Autowired
    private IWendaoCertInfoService wendaoCertInfoService;

    @Autowired
    private ITTeacherService teacherService;

    @Autowired
    private IPassedPublicClassService passedPublicClassService;

    /**
     * 查询公共资质授权列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized)
    {
        startPage();
        List<PublicWendaoCertInfoAuthorized> list = publicWendaoCertInfoAuthorizedService.selectPublicWendaoCertInfoAuthorizedList(publicWendaoCertInfoAuthorized);
        return getDataTable(list);
    }

    /**
     * 导出公共资质授权列表
     */
    @Log(title = "公共资质授权", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized)
    {
        List<PublicWendaoCertInfoAuthorized> list = publicWendaoCertInfoAuthorizedService.selectPublicWendaoCertInfoAuthorizedList(publicWendaoCertInfoAuthorized);
        ExcelUtil<PublicWendaoCertInfoAuthorized> util = new ExcelUtil<PublicWendaoCertInfoAuthorized>(PublicWendaoCertInfoAuthorized.class);
        util.exportExcel(response, list, "公共资质授权数据");
    }

    /**
     * 获取公共资质授权详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(publicWendaoCertInfoAuthorizedService.selectPublicWendaoCertInfoAuthorizedById(id));
    }

    /**
     * 新增公共资质授权
     */
    @Log(title = "公共资质授权", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TeacherAuthorizedDTO teacherAuthorizedDTO)
    {
        Long teacherId = teacherAuthorizedDTO.getTeacherId();
        String entityId = teacherAuthorizedDTO.getEntityId();
        Long certId = teacherAuthorizedDTO.getCertId();

        PublicWendaoCertInfo publicWendaoCertInfo = publicWendaoCertInfoService.selectPublicWendaoCertInfoById(certId);
        if(publicWendaoCertInfo==null){
            return AjaxResult.error("资质id错误!");
        }

        PublicWendaoCertInfoAuthorized query = new PublicWendaoCertInfoAuthorized();
        query.setTeacherId(teacherId);
        query.setEntityId(entityId);
        query.setFirstClassDouyinClassId(publicWendaoCertInfo.getFirstClassDouyinClassId());
        query.setFirstClassDouyinClassId(publicWendaoCertInfo.getSecondClassDouyinClassId());
        List<PublicWendaoCertInfoAuthorized> list = publicWendaoCertInfoAuthorizedService.selectPublicWendaoCertInfoAuthorizedList(query);
        if(CollectionUtils.isNotEmpty(list)){
            return AjaxResult.error("相同类目资质授权已经存在,无法重复授权!");
        }

        PublicWendaoCertInfoAuthorized publicWendaoCertInfoAuthorized = new PublicWendaoCertInfoAuthorized();
        publicWendaoCertInfoAuthorized.setAppNameType(teacherAuthorizedDTO.getAppNameType());
        BeanUtils.copyProperties(publicWendaoCertInfo,publicWendaoCertInfoAuthorized);
        //老师的id
        TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(teacherId);
        if(tTeacher==null){
            return AjaxResult.error("店铺id对应的店铺不存在!");
        }
        publicWendaoCertInfoAuthorized.setShopName(tTeacher.getShopName());


        EnterInformation enterInformation = enterInformationService.selectEnterInformationByShopId(teacherId);
        if(enterInformation==null){
            return AjaxResult.error("入驻信息不存在!");
        }
        if(StringUtils.isNotBlank(enterInformation.getIdNumber())){
            publicWendaoCertInfoAuthorized.setAuthorizedTeacherIdNumber(enterInformation.getIdNumber());
        }
        publicWendaoCertInfoAuthorized.setAuthorizedTeacherMobile(enterInformation.getTelNum());
        publicWendaoCertInfoAuthorized.setTeacherId(teacherId);
        publicWendaoCertInfoAuthorized.setAuthorizedTeacherName(enterInformation.getTeacherName());
        publicWendaoCertInfoAuthorized.setId(null);

        WendaoCertInfo wendaoCertInfo = new WendaoCertInfo();
        wendaoCertInfo.setTeacherId(teacherId);
        wendaoCertInfo.setFirstClassDouyinClassId(publicWendaoCertInfo.getFirstClassDouyinClassId());
        wendaoCertInfo.setSecondClassDouyinClassId(publicWendaoCertInfo.getSecondClassDouyinClassId());
        List<WendaoCertInfo> wendaoCertInfos = wendaoCertInfoService.selectWendaoCertInfoList(wendaoCertInfo);
        if(CollectionUtils.isEmpty(wendaoCertInfos)){
            WendaoCertInfo wendaoCertInfo1 = new WendaoCertInfo();
            wendaoCertInfo1.setTeacherId(teacherId);
            wendaoCertInfo1.setEntityId(entityId);
            wendaoCertInfo1.setAuditStatus(1);
            wendaoCertInfo1.setAuditStatus(1);
            wendaoCertInfo1.setEntityType(3L);
            wendaoCertInfo1.setAuthRoleStatus(1);

            wendaoCertInfo1.setBusinessLicenseCompanyName(publicWendaoCertInfo.getBusinessLicenseCompanyName());
            wendaoCertInfo1.setFirstClassId(publicWendaoCertInfo.getFirstClassId());
            wendaoCertInfo1.setFirstClassPid(publicWendaoCertInfo.getFirstClassPid());
            wendaoCertInfo1.setFirstClassDouyinClassId(publicWendaoCertInfo.getFirstClassDouyinClassId());
            wendaoCertInfo1.setFirstClassTitle(publicWendaoCertInfo.getFirstClassTitle());

            wendaoCertInfo1.setSecondClassId(publicWendaoCertInfo.getSecondClassId());
            wendaoCertInfo1.setSecondClassPid(publicWendaoCertInfo.getSecondClassPid());
            wendaoCertInfo1.setSecondClassDouyinClassId(publicWendaoCertInfo.getSecondClassDouyinClassId());
            wendaoCertInfo1.setSecondClassTitle(publicWendaoCertInfo.getSecondClassTitle());
            wendaoCertInfo1.setAppNameType(tTeacher.getAppNameType());

            wendaoCertInfoService.insertWendaoCertInfo(wendaoCertInfo1);
        }else{
            wendaoCertInfo.setAuditStatus(-1);
            List<WendaoCertInfo> wendaoCertInfos0 = wendaoCertInfoService.selectWendaoCertInfoList(wendaoCertInfo);
            for(WendaoCertInfo wendaoCertInfo1 :wendaoCertInfos0){
                wendaoCertInfoService.deleteWendaoCertInfoById(wendaoCertInfo1.getId());
            }

            WendaoCertInfo wendaoCertInfo1 = new WendaoCertInfo();
            wendaoCertInfo1.setTeacherId(teacherId);
            wendaoCertInfo1.setEntityId(entityId);
            wendaoCertInfo1.setAuditStatus(1);
            wendaoCertInfo1.setAuditStatus(1);
            wendaoCertInfo1.setEntityType(3L);
            wendaoCertInfo1.setAuthRoleStatus(1);

            wendaoCertInfo1.setBusinessLicenseCompanyName(publicWendaoCertInfo.getBusinessLicenseCompanyName());
            wendaoCertInfo1.setFirstClassId(publicWendaoCertInfo.getFirstClassId());
            wendaoCertInfo1.setFirstClassPid(publicWendaoCertInfo.getFirstClassPid());
            wendaoCertInfo1.setFirstClassDouyinClassId(publicWendaoCertInfo.getFirstClassDouyinClassId());
            wendaoCertInfo1.setFirstClassTitle(publicWendaoCertInfo.getFirstClassTitle());

            wendaoCertInfo1.setSecondClassId(publicWendaoCertInfo.getSecondClassId());
            wendaoCertInfo1.setSecondClassPid(publicWendaoCertInfo.getSecondClassPid());
            wendaoCertInfo1.setSecondClassDouyinClassId(publicWendaoCertInfo.getSecondClassDouyinClassId());
            wendaoCertInfo1.setSecondClassTitle(publicWendaoCertInfo.getSecondClassTitle());

            wendaoCertInfo1.setAppNameType(tTeacher.getAppNameType());

            wendaoCertInfoService.insertWendaoCertInfo(wendaoCertInfo1);
        }
        return toAjax(publicWendaoCertInfoAuthorizedService.insertPublicWendaoCertInfoAuthorized(publicWendaoCertInfoAuthorized));
    }

    /**
     * 取消公共资质授权
     */
    @Log(title = "公共资质授权", businessType = BusinessType.INSERT)
    @PostMapping("/cancel")
    public AjaxResult cancel(@RequestBody TeacherAuthorizedDTO teacherAuthorizedDTO)
    {
        PublicWendaoCertInfoAuthorized item = publicWendaoCertInfoAuthorizedService.selectPublicWendaoCertInfoAuthorizedById(teacherAuthorizedDTO.getId());
        if(item==null){
            return AjaxResult.error("授权不存在,无法取消授权!");
        }
        publicWendaoCertInfoAuthorizedService.deletePublicWendaoCertInfoAuthorizedById(item.getId());
        WendaoCertInfo wendaoCertInfo = new WendaoCertInfo();
        wendaoCertInfo.setTeacherId(item.getTeacherId());
        wendaoCertInfo.setEntityId(item.getEntityId());
        wendaoCertInfo.setFirstClassDouyinClassId(item.getFirstClassDouyinClassId());
        wendaoCertInfo.setSecondClassDouyinClassId(item.getSecondClassDouyinClassId());
        List<WendaoCertInfo> wendaoCertInfos = wendaoCertInfoService.selectWendaoCertInfoList(wendaoCertInfo);
        for(WendaoCertInfo wendaoCertInfo1:wendaoCertInfos){
            wendaoCertInfoService.deleteWendaoCertInfoById(wendaoCertInfo1.getId());
        }
        return AjaxResult.success();
    }

    /**
     * 修改公共资质授权
     */
    @Log(title = "公共资质授权", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TeacherAuthorizedDTO teacherAuthorizedDTO)
    {
        Long teacherId = teacherAuthorizedDTO.getTeacherId();
        String entityId = teacherAuthorizedDTO.getEntityId();

        PublicWendaoCertInfoAuthorized query = new PublicWendaoCertInfoAuthorized();
        query.setTeacherId(teacherId);
        query.setEntityId(entityId);
        List<PublicWendaoCertInfoAuthorized> list = publicWendaoCertInfoAuthorizedService.selectPublicWendaoCertInfoAuthorizedList(query);
        if(CollectionUtils.isNotEmpty(list)){
            return AjaxResult.error("相同授权已存在,无法变更授权!");
        }
        query.setEntityId(null);
        List<PublicWendaoCertInfoAuthorized> listHave = publicWendaoCertInfoAuthorizedService.selectPublicWendaoCertInfoAuthorizedList(query);
        if(CollectionUtils.isNotEmpty(listHave)){
            for(PublicWendaoCertInfoAuthorized item:listHave){
                publicWendaoCertInfoAuthorizedService.deletePublicWendaoCertInfoAuthorizedById(item.getId());
            }
        }
        return add(teacherAuthorizedDTO);
    }

    /**
     * 删除公共资质授权
     */
    @Log(title = "公共资质授权", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(publicWendaoCertInfoAuthorizedService.deletePublicWendaoCertInfoAuthorizedByIds(ids));
    }
}
