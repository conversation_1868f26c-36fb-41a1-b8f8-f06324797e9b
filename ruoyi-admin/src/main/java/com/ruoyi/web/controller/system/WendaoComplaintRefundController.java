package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.AlipayRefundVO;
import com.ruoyi.web.vo.WendaoComplaintRefundVo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

/**
 * 问到投诉退款管理Controller
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
@RestController
@RequestMapping("/complaintRefund")
public class WendaoComplaintRefundController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;


    @PostMapping("/list")
    public JSONObject list(@RequestBody WendaoComplaintRefundVo wendaoComplaintRefundVo) {

        String uri = "/complaintRefund/list";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoComplaintRefundVo);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


    /**
     * 获取退款信息详细信息
     */
    @GetMapping(value = "/{complaintId}")
    public JSONObject getInfo(@PathVariable("complaintId") Integer complaintId) {
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/complaintRefund/" + complaintId;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, teacherId));
    }


    /**
     * 获取退款信息详细信息
     */
    @PostMapping(value = "/alipayRefund")
    public JSONObject alipayRefund(@RequestBody AlipayRefundVO alipayRefundVO) {
        String uri = "/complaintRefund/alipayRefund";
        String url = path + uri;
        String jsonString = JSON.toJSONString(alipayRefundVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 主动驳回
     */
    @PostMapping("/voluntaryRejection")
    public JSONObject voluntaryRejection(@RequestBody WendaoComplaintRefundVo wendaoComplaintRefundVo) {
        String uri = "/complaintRefund/voluntaryRejection";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoComplaintRefundVo);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


}
