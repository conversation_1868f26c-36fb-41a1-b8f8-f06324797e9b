package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.ClockInQuestVO;
import com.ruoyi.web.vo.ClockRecordAndUserVO;
import com.ruoyi.web.vo.ClockRecordUserVO;
import com.ruoyi.web.vo.ClockRecordVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/clock_record")
public class ClockRecordController {

    @Value("${wendao.teacher.path}")
    private String path;




    @PostMapping("/selectByCourseId")
    public JSONObject selectByCourseId(@RequestBody ClockRecordVO clockRecordVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_record/selectByCourseId";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockRecordVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    @GetMapping(value = "/removeById/{id}")
    public JSONObject removeById(@PathVariable("id") Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_record/removeById/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }

    @PostMapping("/selectByUserId")
    public JSONObject selectByUserId(@RequestBody ClockRecordUserVO clockRecordUserVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_record/selectByUserId";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockRecordUserVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    @PostMapping("/getByCourseId")
    public JSONObject getByCourseId(@RequestBody ClockRecordAndUserVO clockRecordAndUserVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/clock_record/getByCourseId";
        String url = path + uri;
        String jsonString = JSON.toJSONString(clockRecordAndUserVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }
}
