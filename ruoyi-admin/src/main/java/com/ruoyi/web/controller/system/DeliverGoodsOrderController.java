package com.ruoyi.web.controller.system;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.DeliverGoodsOrder;
import com.ruoyi.web.vo.DeliverGoodsOrderExportVO;
import com.ruoyi.web.vo.DeliverGoodsOrderVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 物流信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@RestController
@RequestMapping("/deliver_goods_order")
public class DeliverGoodsOrderController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;


    /**
     * 修改物流信息
     */
    @PutMapping("/createDeliverOrderByBackEnd")
    public JSONObject createDeliverOrderByBackEnd(@RequestBody DeliverGoodsOrder deliverGoodsOrder) {
        String uri = "/deliver_goods_order/createDeliverOrderByBackEnd20250604";
        String url = path + uri;
        String jsonString = JSON.toJSONString(deliverGoodsOrder);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,"1");
    }

    /**
     * 导出物流信息列表
     */
    @PostMapping("/export")
    public JSONObject export(@RequestBody DeliverGoodsOrderExportVO deliverGoodsOrderExportVO) {
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/deliver_goods_order/export";
        String url = path + uri;
        String jsonString = JSON.toJSONString(deliverGoodsOrderExportVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }

    /**
     * 修改物流信息
     */
    @PutMapping("/updateDeliverGoodsOrder")
    public JSONObject edit(@RequestBody DeliverGoodsOrder deliverGoodsOrder) {
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/deliver_goods_order/updateDeliverGoodsOrder";
        String url = path + uri;
        String jsonString = JSON.toJSONString(deliverGoodsOrder);
        return HttpClientPostFormUtil.putRequestBody(url, jsonString, teacherId.toString());
    }

    /**
     * 查询物流信息列表检索
     */
    @PostMapping("/selectAll")
    public JSONObject selectAll(@RequestBody DeliverGoodsOrderVO deliverGoodsOrderVO) {
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/deliver_goods_order/selectAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(deliverGoodsOrderVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }

        /**
     * 导入发货信息
     *
     * @param file 文件信息
     * @return 结果
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JSONObject upload(@RequestPart(value = "file") MultipartFile file){
        String uri = "/deliver_goods_order/upload";
        String url = path + uri;
        Map<String, Object> map = new HashMap<>();
        System.out.println(file);
        map.put("file", convert(file));
        System.out.println(map);
        return JSONObject.parseObject(HttpUtil.post(url, map));
    }

    public static File convert(MultipartFile file){
        File convFile = new File("temp_image", file.getOriginalFilename());
        if ( !convFile.getParentFile( ).exists()) {
            System.out.println( "mkdir:" + convFile.getParentFile( ).mkdirs());
        }
        try {
            convFile.createNewFile();
            FileOutputStream fos = new FileOutputStream(convFile);
            fos.write(file.getBytes());
            fos.close( );
        }catch (IOException e){
            e.printStackTrace( );
        }
        return convFile;
    }

}
