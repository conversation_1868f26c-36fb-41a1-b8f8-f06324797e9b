package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.RecoverDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 课程Controller
 *
 * <AUTHOR>
 * @date 2024-03-18
 */
@RestController
@RequestMapping("/teacherRecover")
public class TeacherRecoverController extends BaseController {
    @Value("${wendao.teacher.path}")
    private String path;

    @PostMapping("/recover")
    public JSONObject importCourse(@RequestBody RecoverDTO recoverDTO) {
        String uri = "/teacherRecover/recover";
        String url = path + uri;
        String jsonString = JSON.toJSONString(recoverDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

}
