package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.WendaoCreateRefundDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/8
 */
@RestController
@RequestMapping("/dy_trade_system_controller")
public class DyTradeSystemController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;
    /**
     * 处理投诉单
     *
     * @param wendaoCreateRefundDTO
     * @return
     */
    @PostMapping("/cd_create_refund")
    public JSONObject cdCreateRefund(@RequestBody WendaoCreateRefundDTO wendaoCreateRefundDTO) {
        String uri = "/dy_trade_system_controller/cd_create_refund";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoCreateRefundDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 撤销处理投诉单
     *
     * @param wendaoCreateRefundDTO
     * @return
     */
    @PostMapping("/cd_create_refund_return")
    public JSONObject cdCreateRefundReturn(@RequestBody WendaoCreateRefundDTO wendaoCreateRefundDTO) {
        String uri = "/dy_trade_system_controller/cd_create_refund_return";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoCreateRefundDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }




}
