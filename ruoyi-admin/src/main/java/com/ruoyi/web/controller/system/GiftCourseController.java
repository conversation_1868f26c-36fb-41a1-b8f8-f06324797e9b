package com.ruoyi.web.controller.system;

import cn.hutool.core.util.PhoneUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.poi.ExcelXlsxReader;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.TTeacher;
import com.ruoyi.system.dto.ChangeShopMobileDTO;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.ITTeacherService;
import com.ruoyi.web.dto.ChangeRateDTO;
import com.ruoyi.web.dto.TelNumberImportDTO;
import com.wendao101.system.api.model.LoginUser;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@RestController
//@RequestMapping("/teacher")
public class GiftCourseController {

    public final static String SECRET = "qrlwznhlwewrwqtsfurhygjzhq";
    public static final String baseUrl = "https://goodteacher.wendao101.com/prod-api";


    public final static String back_teacher_key = "back_teacher_key:";
    @Autowired
    private ITTeacherService teacherService;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IEnterInformationService enterInformationService;


    @PostMapping("/order/change_order_rate/changeRate")
    public JSONObject changeRate(@RequestBody ChangeRateDTO changeRateDTO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        if(StringUtils.isBlank(teacherId)){
            AjaxResult result  = AjaxResult.error("Teacherid未传");
            return JSON.parseObject(JSON.toJSONString(result));
        }
        //查询老师信息
        String uri = baseUrl + "/order/change_order_rate/changeRate";
        if(StringUtils.isBlank(changeRateDTO.getOperatorMobile())||StringUtils.isBlank(changeRateDTO.getCode())||StringUtils.isBlank(changeRateDTO.getUuid())){
            AjaxResult result  = AjaxResult.error("短信验证信息不全,uuid,code,operatorMobile都必传!");
            return JSON.parseObject(JSON.toJSONString(result));
        }
        ChangeShopMobileDTO changeShopMobileDTO = new ChangeShopMobileDTO();
        changeShopMobileDTO.setMasterPhoneNumber(changeRateDTO.getOperatorMobile());
        changeShopMobileDTO.setCode(changeRateDTO.getCode());
        changeShopMobileDTO.setUuid(changeRateDTO.getUuid());
        int count = enterInformationService.countSmsRecord(changeShopMobileDTO);
        if(count<1){
            AjaxResult result  = AjaxResult.error("验证码错误");
            return JSON.parseObject(JSON.toJSONString(result));
        }
        String jsonString = JSON.toJSONString(changeRateDTO);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(uri, jsonString, token);
    }

    @GetMapping("/teacher/course_controller/query_course_audit_paged")
    public JSONObject query_course_audit_paged(@RequestParam Map<String, String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        //查询老师信息
        String url = baseUrl + "/teacher/course_controller/query_course_audit_paged";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    @GetMapping("/teacher/course_visit/downloadTelNumberTemplate")
    public void downloadTelNumberTemplate(HttpServletResponse response) {
        ExcelUtil<TelNumberImportDTO> util = new ExcelUtil<>(TelNumberImportDTO.class);
        List<TelNumberImportDTO> list = new ArrayList<>();
        util.exportExcel(response, list, "导入手机号码", "导入手机号码模版");
    }

    @PostMapping("/teacher/course_visit/importTelNumberFromExcel")
    public AjaxResult importTelNumberFromExcel(@RequestParam("file") MultipartFile file) throws Exception {
        //解决大数据量内存溢出问题
        ExcelXlsxReader reader = new ExcelXlsxReader();
        reader.process(file.getInputStream());
        List<List<String>> rowList = reader.getRows();
        List<TelNumberImportDTO> telNumberList = importFromExcel(rowList);
        return AjaxResult.success("导入成功!",telNumberList);
    }

    @PostMapping("/teacher/course_visit/giveAwayByMobileAllInOne")
    public JSONObject giveAwayByMobileAllInOne(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = baseUrl + "/teacher/course_visit/giveAwayByMobileAllInOne";
        jsonObject.put("sourceFrom","backend");
        String jsonString = JSON.toJSONString(jsonObject);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(uri, jsonString, token);
    }
    @GetMapping("/order/course_order/cancel_give_away/{orderId}")
    public JSONObject cancel_give_away(@PathVariable("orderId") String orderId, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        //查询老师信息
        String url = baseUrl + "/order/course_order/cancel_give_away/"+orderId;
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, new HashMap<>(), token);
        return JSON.parseObject(s);
    }
    @PostMapping("/teacher/course_visit/giveAwayFromRecord")
    public JSONObject giveAwayFromRecord(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = baseUrl + "/teacher/course_visit/giveAwayFromRecord";
        String jsonString = JSON.toJSONString(jsonObject);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(uri, jsonString, token);
    }

    @PostMapping("/order/course_order/selectAll")
    public JSONObject selectAll(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = baseUrl + "/order/course_order/selectAll";
        String jsonString = JSON.toJSONString(jsonObject);
        String token = getTeacherToken(teacherId);
        return HttpClientPostFormUtil.postRequestBodyWithToken(uri, jsonString, token);
    }

    @GetMapping("/teacher/course_visit/list")
    public JSONObject course_visit_list(@RequestParam Map<String, String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        //查询老师信息
        String url = baseUrl + "/teacher/course_visit/list";
        String token = getTeacherToken(teacherId);
        String s = HttpClientPostFormUtil.sendGetMapWithToken(url, map, token);
        return JSON.parseObject(s);
    }

    private String getTeacherToken(String teacherId) {
        TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(Long.valueOf(teacherId));
        String token = redisCache.getCacheObject(back_teacher_key + teacherId);
        if (StringUtils.isBlank(token)) {
            token = createToken(tTeacher.getTeacherId(), tTeacher.getMobile());
            redisCache.setCacheObject(back_teacher_key + teacherId, token, 23, TimeUnit.HOURS);
        }
        return token;
    }


    public String createToken(Long userId, String userName) {
        LoginUser loginUser = new LoginUser();
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        loginUser.setUserid(userId);
        loginUser.setUsername(userName);
        refreshToken(loginUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<>();
        claimsMap.put("user_key", token);
        claimsMap.put("user_id", userId);
        claimsMap.put("username", userName);

        return createToken(claimsMap);
    }

    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + 24L * 3600L * 1000L);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisCache.setCacheObject(userKey, loginUser, 24, TimeUnit.HOURS);
    }

    private String getTokenKey(String token) {
        return "login_tokens:" + token;
    }

    public static String createToken(Map<String, Object> claims) {
        return Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, SECRET).compact();
    }

    private List<TelNumberImportDTO> importFromExcel(List<List<String>> rowList) {
        List<TelNumberImportDTO> telNumberList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rowList)) {
            for (int i = 1; i < rowList.size(); i++) {
                List<String> list = rowList.get(i);
                if (!list.isEmpty()) {
                    TelNumberImportDTO telNumberImportDTO = new TelNumberImportDTO();
                    String telNumber = list.get(0).trim();
                    // 校验不为空
                    if (StringUtils.isBlank(telNumber)) {
                        continue;
                    }
                    telNumberImportDTO.setTelNumber(list.get(0).trim());
                    telNumberImportDTO.setIsCorrect(true);
                    //校验手机号是否正确
                    if (!PhoneUtil.isMobile(telNumber)) {
                        telNumberImportDTO.setErrorMessage("手机号格式不正确!");
                        telNumberImportDTO.setIsCorrect(false);
                    }
                    telNumberList.add(telNumberImportDTO);
                }
            }
        }
        return telNumberList;
    }
}
