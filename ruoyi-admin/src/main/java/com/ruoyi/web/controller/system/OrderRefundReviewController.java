package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;

import com.ruoyi.web.domain.OrderRefundReview;
import com.ruoyi.web.dto.OrderRefundReviewDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 退款审核Controller
 *
 * <AUTHOR>
 * @date 2024-03-19
 */
@RestController
@RequestMapping("/order_refund_review")
public class OrderRefundReviewController extends BaseController {
    @Value("${wendao.order.path}")
    private String path;
    /**
     * 查询退款审核列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String, String> map) {
        String url = path + "/order_refund_review/list";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, null);
        return JSON.parseObject(s);
    }

    /**
     * 新增退款审核
     */
    @PostMapping("/add")
    public JSONObject add(@RequestBody OrderRefundReviewDTO orderRefundReviewDTO) {
        String url = path + "/order_refund_review/add";
        String jsonString = JSON.toJSONString(orderRefundReviewDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 修改退款审核
     */
    @PostMapping("/submitResult")
    public JSONObject edit(@RequestBody OrderRefundReview orderRefundReview) {
        String url = path + "/order_refund_review/submitResult";
        String jsonString = JSON.toJSONString(orderRefundReview);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }
}
