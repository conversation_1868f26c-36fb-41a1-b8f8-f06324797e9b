package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.AudioFinishVO;
import com.ruoyi.web.vo.ChangeAudioNameVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;


/**
 * 素材音频Controller
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@RestController
@RequestMapping("/audio")
public class TAudioController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 上传音频完毕后写入数据库记录
     */
    @PostMapping("/upload_finish")
    public JSONObject uploadFinish(@RequestBody AudioFinishVO audioFinishVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = path + "/audio/upload_finish";
        String jsonString = JSON.toJSONString(audioFinishVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 查询素材音频列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = path + "/audio/list";
        String s = HttpClientPostFormUtil.sendGetMap(url, map, Long.valueOf(teacherId));
        JSONObject jsonObject = JSON.parseObject(s);
        return jsonObject;
    }

    /**
     * 修改音频素材名称
     */
    @PutMapping("/change_audio_name")
    public JSONObject changeVideoName(@RequestBody ChangeAudioNameVO changeAudioNameVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String url = path + "/audio/change_audio_name";
        String jsonString = JSON.toJSONString(changeAudioNameVO);
        return HttpClientPostFormUtil.putRequestBody(url, jsonString, teacherId);
    }

    /**
     * 删除音频素材
     */
    @DeleteMapping("/{ids}")
    public JSONObject remove(@PathVariable Long[] ids,HttpServletRequest request) {
        String arrayAsString = Arrays.toString(ids);
        String result = arrayAsString.substring(1, arrayAsString.length() - 1).replaceAll("\\s", "");
        String url = path + "/audio/"+ result;
        String teacherId = request.getHeader("Teacherid");
        return HttpClientPostFormUtil.deleteRequestBody(url,teacherId);
    }

}
