package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.MLiveStreamAndAddVO;
import com.ruoyi.web.vo.MLiveStreamAndUpdateVO;
import com.ruoyi.web.vo.MLiveStreamVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/21
 */
@RestController
@RequestMapping("/live_stream_map")
public class MLiveStreamMapController {
    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 查询推广页基本信息列表
     */
    @PostMapping("/list")
    public JSONObject list(@RequestBody MLiveStreamVO mLiveStreamVO, HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/live_stream_map/list";
        String url = path +uri;
        String JsonString = JSON.toJSONString(mLiveStreamVO);
        return HttpClientPostFormUtil.postRequestBody(url, JsonString,teacherId);
    }

    /**
     * 展示店铺的直播信息页
     */
    @PostMapping("/shop_live_stream")
    public JSONObject shop_live_stream(@RequestBody MLiveStreamVO mLiveStreamVO) {
        String uri = "/live_stream_map/shop_live_stream";
        String url = path +uri;
        String jsonString = JSON.toJSONString(mLiveStreamVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,null);
    }


    /**
     * 删除推广页基本信息(修改状态)假删除
     */
    @GetMapping("/delete/{id}")
    public JSONObject remove(@PathVariable Long id,HttpServletRequest request){
        String teacherId = request.getHeader("teacherId");
        String uri = "/live_stream_map/delete/"+id;
        String url = path +uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }
    /**
     * 新增直播集合页
     */
    @PostMapping("/add")
    public JSONObject add(@RequestBody MLiveStreamAndAddVO mLiveStreamAndAddVO, HttpServletRequest request){
        String teacherId = request.getHeader("teacherId");
        String uri = "/live_stream_map/add";
        String url = path +uri;
        String jsonString = JSON.toJSONString(mLiveStreamAndAddVO);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,teacherId);
    }
    /**
     * 修改推广页基本信息
     */
    @PostMapping("/update")
    public JSONObject edit(@RequestBody MLiveStreamAndUpdateVO mLiveStreamAndUpdateVO, HttpServletRequest request){
        String teacherId = request.getHeader("teacherId");
        String uri = "/live_stream_map/update";
        String url = path +uri;
        String jsonString = JSON.toJSONString(mLiveStreamAndUpdateVO);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,teacherId);
    }

    @PostMapping("/theme/list")
    public JSONObject themeList() {
        String uri = "/live_stream_map/theme/list";
        String url = path +uri;
        String jsonString = JSON.toJSONString(null);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,null);
    }


    @GetMapping(value = "/detail/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id,HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/live_stream_map/detail/"+id;
        String url = path +uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }



}
