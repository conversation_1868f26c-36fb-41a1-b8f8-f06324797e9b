package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.ChangeVideoNameVO;
import com.ruoyi.web.vo.VideoFinishVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;


/**
 * 素材视频Controller
 *
 * <AUTHOR>
 * @date 2023-07-25
 */
@RestController
@RequestMapping("/video")
public class TVideoController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;


    /**
     * 上传视频完毕后写入数据库记录
     *
     * @param videoFinishVO
     * @return
     */
    @PostMapping("/upload_finish")
    public JSONObject uploadFinish(@RequestBody VideoFinishVO videoFinishVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/video/upload_finish";
        String jsonString = JSON.toJSONString(videoFinishVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 视频转码状态查询
     */
    @GetMapping("/query_trans_status")
    public JSONObject queryTransStatus(@RequestParam Long id,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/video/query_trans_status";
        String s = HttpClientPostFormUtil.sendGet(uri, id.toString(), Long.valueOf(teacherId));
        JSONObject jsonObject = JSON.parseObject(s);
        return jsonObject;

    }

    /**
     * 查询素材视频列表
     */
    //@RequiresPermissions("teacher:video:list")
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/video/list";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        return JSON.parseObject(s);
    }

    /**
     * 修改素材视频
     */
    @PutMapping("/change_video_name")
    public JSONObject changeVideoName(@RequestBody ChangeVideoNameVO changeVideoNameVO, HttpServletRequest request) {
        String uri = path + "/video/change_video_name";
        String teacherId = request.getHeader("Teacherid");
        String jsonString = JSON.toJSONString(changeVideoNameVO);
        return HttpClientPostFormUtil.putRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 删除素材视频
     */
    @DeleteMapping("/{ids}")
    public JSONObject remove(@PathVariable Long[] ids,HttpServletRequest request) {
        String arrayAsString = Arrays.toString(ids);
        String result = arrayAsString.substring(1, arrayAsString.length() - 1).replaceAll("\\s", "");
        String uri = path + "/video/" + result;
        String teacherId = request.getHeader("Teacherid");
        return HttpClientPostFormUtil.deleteRequestBody(uri, teacherId);
    }

}
