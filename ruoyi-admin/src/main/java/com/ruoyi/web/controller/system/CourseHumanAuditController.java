package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.web.domain.CourseMachineAudit;
import com.ruoyi.web.dto.CourseHumanAuditDTO;
import com.ruoyi.web.service.CourseMachineAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/course_human_audit")
public class CourseHumanAuditController extends BaseController {
    @Autowired
    private CourseMachineAuditService courseMachineAuditService;
    @PostMapping("/submitAuditStatus")
    public AjaxResult submitAuditStatus(@RequestBody CourseHumanAuditDTO courseHumanAuditDTO){
        return courseMachineAuditService.submitAuditStatus(courseHumanAuditDTO);
    }

    @PostMapping("/auditDetail")
    public AjaxResult auditDetail(@RequestBody CourseHumanAuditDTO courseHumanAuditDTO){
        return courseMachineAuditService.auditDetail(courseHumanAuditDTO);
    }
}
