package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.StudyData;
import com.ruoyi.web.dto.StudyDataDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/learning_materials")
@Slf4j
@RequiredArgsConstructor
public class LearningMaterialsController extends BaseController {


    @Value("${wendao.teacher.path}")
    private String path;
    /**
     * 根据老师id查询学习资料
     *
     * @param studyData
     * @return
     */
    @PostMapping("/listByTeacher")
    public JSONObject listByTeacher(@RequestBody StudyData studyData) {
        String uri = "/learning_materials/listByTeacher";
        String url = path + uri;
        String jsonString = JSON.toJSONString(studyData);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);

    }
    /**
     * 添加学习资料
     *
     * @param studyData
     * @return
     */
    @PostMapping("/upload")
    public JSONObject upload(@RequestBody StudyData studyData) {
        String uri = "/learning_materials/upload";
        String url = path + uri;
        String jsonString = JSON.toJSONString(studyData);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);

    }
    /**
     * 根据学习资料id查询学习资料详情
     *
     * @param
     * @return
     */
    @PostMapping("/seleteById")
    public JSONObject selectLearningMaterialsById(@RequestBody StudyData studyData) {
        String uri = "/learning_materials/seleteById";
        String url = path + uri;
        String jsonString = JSON.toJSONString(studyData);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


    /**
     * 查询没有和这个学习资料绑定的可成
     *
     * @return
     */
    @GetMapping("/list_not_binding/{teacherId}/{id}")
    public JSONObject selectByTeacherIdToCourse(@PathVariable("teacherId") Long teacherId,
                                                @PathVariable("id") Long id) {
        String uri = "/learning_materials/list_not_binding/" + teacherId + "/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, teacherId));
    }
    /**
     * 添加学习资料与课程的绑定
     *
     * @return
     */
    @PostMapping("/binding_course")
    public JSONObject bindingCourseById(@RequestBody StudyDataDTO studyDataDTO) {
        Long teacherId = studyDataDTO.getTeacherId();
        String uri = "/learning_materials/binding_course";
        String url = path + uri;
        String jsonString = JSON.toJSONString(studyDataDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }
    /**
     * 根据学习资料id删除其绑定的所有课程
     *
     * @param id 学习资料id
     * @return
     */
    @PostMapping("/delete/{id}")
    public JSONObject deleteLearningMaterialsById(@PathVariable("id") Long id) {
        String uri = "/learning_materials/delete/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendPost(url, id.toString()));
    }
    /**
     * 根据学习资料id移除绑定课程
     *
     * @param id 学习资料id courseId 课程id
     * @return
     */
    @PostMapping("/remove/{id}/{courseId}")
    public JSONObject removCourseById(@PathVariable("id") Long id,
                                      @PathVariable("courseId") Long courseId) {
        String uri = "/learning_materials/remove/" + id + "/" + courseId;
        String url = path + uri;
        String parom = id.toString() + courseId.toString();
        return JSON.parseObject(HttpClientPostFormUtil.sendPost(url, parom));
    }
    /**
     * 总后台查询所有学习资料
     *
     * @param studyData
     * @return
     */
    @PostMapping("/selectAll")
    public JSONObject selectAll(@RequestBody StudyData studyData) {
        String uri = "/learning_materials/selectAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(studyData);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);

    }
}
