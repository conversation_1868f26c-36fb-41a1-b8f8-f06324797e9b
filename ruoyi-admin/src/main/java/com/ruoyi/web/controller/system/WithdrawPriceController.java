package com.ruoyi.web.controller.system;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.WithdrawPriceAndFrozenAccountVO;
import com.ruoyi.web.vo.WithdrawPricePromoterVO;
import com.ruoyi.web.vo.WithdrawPriceVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

/**
 * 提现金额Controller
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@RestController
@RequestMapping("/withdrawPrice")
public class WithdrawPriceController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;

    /**
     * 自己后台老师流水列表
     *
     * @param withdrawPriceVO
     * @return
     */
    @PostMapping("/selectAll")
    public JSONObject selectAll(@RequestBody WithdrawPriceVO withdrawPriceVO) {
        String uri = "/withdrawPrice/selectAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawPriceVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


    /**
     * 自己后台推广员流水列表
     *
     * @param withdrawPricePromoterVO
     * @return
     */
    @PostMapping("/promoter/selectAll")
    public JSONObject promoterSelectAll(@RequestBody WithdrawPricePromoterVO withdrawPricePromoterVO) {
        String uri = "/withdrawPrice/promoter/selectAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawPricePromoterVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


    /**
     * 流水列表详情
     */
    @GetMapping(value = "/getByTeacherId/{id}")
    public JSONObject getByTeacherId(@PathVariable("id") Long teacherId) {
        String uri = "/withdrawPrice/getByTeacherId/" + teacherId;
        String url = path + uri;
        String s = HttpUtil.get(url);
        return JSONObject.parseObject(s);
    }


    /**
     * 推广员流水列表详情
     */
    @GetMapping(value = "/getByPromoterId/{id}")
    public JSONObject getByPromoterId(@PathVariable("id") Long promoterId) {
        String uri = "/withdrawPrice/getByPromoterId/" + promoterId;
        String url = path + uri;
        String s = HttpUtil.get(url);
        return JSONObject.parseObject(s);
    }


    /**
     * 老师/推广员
     * 冻结账号
     */
    @PostMapping(value = "/frozenAccount")
    public JSONObject frozenAccount(@RequestBody WithdrawPriceAndFrozenAccountVO withdrawPriceAndFrozenAccountVO) {
        String uri = "/withdrawPrice/frozenAccount";
        String url = path + uri;
        String jsonString = JSON.toJSONString(withdrawPriceAndFrozenAccountVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

}
