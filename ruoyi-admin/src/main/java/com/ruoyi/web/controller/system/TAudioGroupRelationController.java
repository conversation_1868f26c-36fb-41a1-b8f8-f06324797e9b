package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.AddAudioGroupRelationVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * 音频和分组关系Controller
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@RestController
@RequestMapping("/audio_group_relation")
public class TAudioGroupRelationController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 新增音频和分组关系
     */
    @PostMapping
    public JSONObject add(@RequestBody AddAudioGroupRelationVO addAudioGroupRelationVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/audio_group_relation";
        String jsonString = JSON.toJSONString(addAudioGroupRelationVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 将音频从分组中移除
     *
     * @param addAudioGroupRelationVO
     * @return
     */
    @PostMapping("remove_from_group")
    public JSONObject removeFromGroup(@RequestBody AddAudioGroupRelationVO addAudioGroupRelationVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/audio_group_relation/remove_from_group";
        String jsonString = JSON.toJSONString(addAudioGroupRelationVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }
}
