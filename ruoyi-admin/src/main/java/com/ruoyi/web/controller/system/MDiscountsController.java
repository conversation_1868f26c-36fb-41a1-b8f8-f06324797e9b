package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.dto.MdTotalSearchDTO;
import com.ruoyi.web.vo.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/discounts")
public class MDiscountsController extends BaseController {
    @Value("${wendao.teacher.path}")
    private String path;



    /**
     * 总后台查询所有店铺优惠券
     */
    @PostMapping("/selectAll")
    public JSONObject selectAll(@RequestBody MdTotalSearchDTO MdTotalSearchDTO, HttpServletRequest request) {
        String uri = "/discounts/selectAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(MdTotalSearchDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,null);

    }


    @PostMapping("/list")
    public JSONObject list(@RequestBody MDiscountsAndStatusVO mDiscountsAndStatusVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/list";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mDiscountsAndStatusVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    @PostMapping("/add")
    public JSONObject add(@RequestBody MDiscountsVO mDiscountsVO, HttpServletRequest request) {

        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/add";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mDiscountsVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    @GetMapping(value = "/detail/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/detail/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }



    @PostMapping("/select/receive/Coupon")
    public JSONObject selectReceiveCoupon(@RequestBody MreceiveCouponVO mreceiveCouponVO, HttpServletRequest request) {

        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/select/receive/Coupon";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mreceiveCouponVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }

    @GetMapping("/delete/{id}")
    public JSONObject remove(@PathVariable Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/delete/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }


    @PostMapping("/update")
    public JSONObject updateById(@RequestBody MDiscountsVO mDiscountsVO, HttpServletRequest request) {

        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/update";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mDiscountsVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    @PostMapping("/codeList")
    public JSONObject SelectDiscountsCode(@RequestBody MDiscountsAndCodeVO mDiscountsAndCodeVO, HttpServletRequest request) {

        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/codeList";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mDiscountsAndCodeVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }

    /**
     * 优惠码页面数据
     */
    @GetMapping("/codeData/discountsId")
    public JSONObject SelectDiscountsCodeData(@RequestParam("discountsId") Long discountsId, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/codeData/discountsId?discountsId=" + discountsId;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }


    @PostMapping("/stopAction")
    public JSONObject stopAction(@RequestBody MDiscountsStopActionVO mDiscountsStopActionVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/stopAction";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mDiscountsStopActionVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }

    @GetMapping("/cancellation/{discountsCodeId}")
    public JSONObject isCancellation(@PathVariable("discountsCodeId") Long discountsCodeId, HttpServletRequest request) {

        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/cancellation/" + discountsCodeId;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }


    @PostMapping("/addDiscountsCode")
    public JSONObject AddDiscountsCode(@RequestBody MDiscountsAndAddCodeVO mDiscountsAndAddCodeVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/addDiscountsCode";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mDiscountsAndAddCodeVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);

    }


    @PostMapping("/provideDiscounts")
    public JSONObject provideDiscounts(@RequestBody MDiscountsCodeProvideVO mDiscountsCodeProvideVO, HttpServletRequest request) {

        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/provideDiscounts";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mDiscountsCodeProvideVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }



    @PostMapping("/export")
    public JSONObject export(@RequestBody MDiscountsAndCodeVO mDiscountsAndCodeVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/discounts/export";
        String url = path + uri;
        String jsonString = JSON.toJSONString(mDiscountsAndCodeVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }
}
