package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.web.service.CourseMachineAuditService;
import com.ruoyi.web.domain.CourseMachineAudit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/course_machine_audit")
public class CourseMachineAuditController {
    @Autowired
    private CourseMachineAuditService courseMachineAuditService;


    /**
     * 查询机器审核列表
     */
    //@RequiresPermissions("teacher:audit:list")
    @GetMapping("/list")
    public TableDataInfo list(CourseMachineAudit courseMachineAudit) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        return courseMachineAuditService.list(courseMachineAudit,pageNum,pageSize);
    }
}
