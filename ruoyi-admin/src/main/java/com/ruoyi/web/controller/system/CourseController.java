package com.ruoyi.web.controller.system;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.Course;
import com.ruoyi.web.dto.AddAnchorInfoDTO;
import com.ruoyi.web.dto.CopyCourseDTO;
import com.ruoyi.web.dto.ImportCourseDTO;
import com.ruoyi.web.vo.StoreLevelCourseVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * 课程Controller
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
@RestController
@RequestMapping("/course_controller")
public class CourseController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 导入课程
     * @param importCourseDTO
     * @return
     */
    @PostMapping("/import_course")
    public JSONObject importCourse(@RequestBody ImportCourseDTO importCourseDTO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/import_course";
        String url = path + uri;
        String jsonString = JSON.toJSONString(importCourseDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 复制课程
     * @param copyCourseDTO
     * @return
     */
    @PostMapping("/copy_course")
    public JSONObject copyCourse(@RequestBody CopyCourseDTO copyCourseDTO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/copy_course";
        String url = path + uri;
        String jsonString = JSON.toJSONString(copyCourseDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 新增/修改课程第三步
     * @param addAnchorInfoDTO
     * @return
     */
    @PostMapping("/add_or_modify_third")
    public JSONObject addOrModifyThird(@RequestBody AddAnchorInfoDTO addAnchorInfoDTO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/add_or_modify_third";
        String url = path + uri;
        String jsonString = JSON.toJSONString(addAnchorInfoDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 第三步编辑时候回显
     * @return
     */
    @GetMapping(value = "/get_third_info/{id}")
    public JSONObject getThirdInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/get_third_info/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }

    /**
     * 新增/修改课程第二步
     * @param course
     * @return
     */
    @PostMapping("/add_or_modify_second")
    public JSONObject addOrModifySecond(@RequestBody Course course, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/add_or_modify_second";
        String url = path + uri;
        String jsonString = JSON.toJSONString(course);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }


    /**
     * 修改课程排序
     * @return
     */
    @GetMapping("/change_course_seq")
    public JSONObject changeCourseSeq(@RequestParam Map<String,String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/change_course_seq";
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGetMap(url, map, Long.valueOf(teacherId)));
    }

    /**
     * 获取分类列表
     * @return
     */
    @GetMapping("/query_category_list")
    public JSONObject queryCategoryList(HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/query_category_list";
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }


    /**
     * 分页查询查询课程列表
     * 需要合并审核通过的表course_dy
     */
    @GetMapping("/query_course_paged")
    public JSONObject queryCoursePaged(@RequestParam Map<String,String> map ,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        if (Objects.isNull(teacherId)){
            String uri = "/course_controller/query_course_paged";
            String url = path + uri;
            return JSON.parseObject(HttpClientPostFormUtil.sendGetMap(url, map, null));
        }else {
            String s = teacherId;
            String uri = "/course_controller/query_course_paged";
            String url = path + uri;
            return JSON.parseObject(HttpClientPostFormUtil.sendGetMap(url, map, Long.valueOf(s)));
        }
    }


    /**
     * 分页查询查询课程列表
     * 需要合并审核通过的表course_dy
     */
    @GetMapping("/query_course_audit_paged")
    public JSONObject queryCourseAuditPaged(@RequestParam Map<String,String> map) {
        String teacherId = map.get("teacherId");
        if (Objects.isNull(teacherId)){
            String uri = "/course_controller/query_course_audit_paged";
            String url = path + uri;
            return JSON.parseObject(HttpClientPostFormUtil.sendGetMap(url, map, null));
        }else {
            String s = teacherId;
            String uri = "/course_controller/query_course_paged";
            String url = path + uri;
            return JSON.parseObject(HttpClientPostFormUtil.sendGetMap(url, map, Long.valueOf(s)));
        }
    }


    /**
     * 获取课程详细信息
     * 第一步信息
     */
    @GetMapping(value = "/get_first_info/{id}")
    public JSONObject getFirstInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/get_first_info/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }

    /**
     * 获取课程详细信息
     * 第二步信息
     */
    @GetMapping(value = "/get_second_info/{id}")
    public JSONObject getSecondInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/get_second_info/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url, Long.valueOf(teacherId)));
    }

    /**
     * 新增课程第一步
     */
    @PostMapping
    public JSONObject add(@RequestBody Course course, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller";
        String url = path + uri;
        String jsonString = JSON.toJSONString(course);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 修改课程第一步
     */
    @PutMapping
    public JSONObject edit(@RequestBody Course course, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller";
        String url = path + uri;
        String jsonString = JSON.toJSONString(course);
        return HttpClientPostFormUtil.putRequestBody(url, jsonString, teacherId);
    }

    /**
     * 删除课程
     */
    @DeleteMapping("/{ids}")
    public JSONObject remove(@PathVariable Long[] ids, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String arrayAsString = Arrays.toString(ids);
        String result = arrayAsString.substring(1, arrayAsString.length() - 1).replaceAll("\\s", "");
        String uri = path + "/course_controller/"+ result;
        return HttpClientPostFormUtil.deleteRequestBody(uri,teacherId);
    }

    /**
     * 课程上下架
     */
    @PostMapping("/updateCourse")
    public JSONObject updateCourse(@RequestBody Course course, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/course_controller/updateCourse";
        String url = path + uri;
        String jsonString = JSON.toJSONString(course);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 短信营销 h5 页面返回参数
     */
    @GetMapping("/getByCidAndTid")
    public JSONObject getByCidAndTid(@RequestParam("cid") Long cid, @RequestParam("tid") Long tid){
        String uri = "/course_controller/getByCidAndTid?cid=" + cid + "&tid=" + tid;
        String url = path + uri;
        String s = HttpUtil.get(url);
        return JSONObject.parseObject(s);
    }

    @PostMapping("/storeLevelCourse")
    public JSONObject storeLevelCourse(@RequestBody StoreLevelCourseVO storeLevelCourseVO) {
        String uri = "/course_controller/storeLevelCourse";
        String url = path + uri;
        String jsonString = JSON.toJSONString(storeLevelCourseVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }
}
