package com.ruoyi.web.controller.system;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.qcloud.vod.VodUploadClient;
import com.qcloud.vod.model.VodUploadRequest;
import com.qcloud.vod.model.VodUploadResponse;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.TTeacher;
import com.ruoyi.system.dto.PicDTO;
import com.ruoyi.system.dto.UEditorImageDTO;
import com.ruoyi.system.service.ITTeacherService;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资质中心信息Controller
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/system/ueditor")
public class UEditorController extends BaseController {
    @Value("${wendao.teacher.path}")
    private String path;

    @Value("${wendao.video.secretId}")
    private String secretId;
    @Value("${wendao.video.secretKey}")
    private String secretKey;
    @Value("${wendao.video.vodsubAppid}")
    private Long vodsubAppid;
    private final String IMGE_PATH = "/root/temp_upload/ueditor/images/";

    @Autowired
    private ITTeacherService teacherService;
    private static final Logger log = LoggerFactory.getLogger(UEditorController.class);

    /**
     * ueditor上传文件
     *
     * @param action
     * @param upfile
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/server_url/{teacherId}")
    public Object serverUrl(HttpServletRequest request, @PathVariable Long teacherId,@RequestParam(value = "action") String action, MultipartFile upfile) throws Exception {
        //String teacherIds = request.getHeader("Teacherid");
        //Long  teacherId = new Long(teacherIds);
        if (StringUtils.equals("listimage", action)) {
            return listimage(teacherId, request.getParameter("start"), request.getParameter("size"));
        }
        //请求时如果图片文件夹不存在则直接创建
        File file = new File(IMGE_PATH);
        if (!file.exists()) {
            file.mkdirs();
        }

        Long finalTeacherId = teacherId;
        List<Object> param = new ArrayList<Object>() {{
            add(action);
            if (upfile != null) {
                add(upfile);
            }
            String[] imageUrls = request.getParameterValues("source[]");
            if (imageUrls != null && imageUrls.length > 0) {
                add(imageUrls);
            }
            add(finalTeacherId);
        }};
        Method method = this.getClass().getMethod(action, List.class);
        return method.invoke(this, param);
    }

    /**
     * 读取配置文件
     *
     * @param param
     * @return
     * @throws Exception
     */
    public JSONObject config(List<Object> param) throws Exception {
        ClassPathResource classPathResource = new ClassPathResource("config.json");
        String jsonString = new BufferedReader(new InputStreamReader(classPathResource.getInputStream())).lines().parallel().collect(Collectors.joining(System.lineSeparator()));
        return JSON.parseObject(jsonString, JSONObject.class);
    }

    /**
     * 拉去远程图片
     *
     * @param param
     * @return
     * @throws Exception
     */
    public JSONObject catchimage(List<Object> param) {
        String[] sources = (String[]) param.get(1);
        Long teacherId = (Long) param.get(2);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", downloadImages(sources, teacherId));
        jsonObject.put("state", "SUCCESS");
        return jsonObject;
    }

    /**
     * 上传图片
     *
     * @param param
     * @return
     * @throws Exception
     */
    public JSONObject uploadimage(List<Object> param) throws Exception {
        JSONObject json = new JSONObject();
        json.put("state", "SUCCESS");
        json.put("url", uploadImageToQCloud(param));
        return json;
    }

    public JSONObject listimage(Long teacherId, String start, String size) {
        int startI = 0;
        int pageSize = 20;
        if (StringUtils.isNotBlank(start)) {
            startI = new Integer(start);
        }
        if (StringUtils.isNotBlank(size)) {
            pageSize = new Integer(size);
        }
        int pageNum = (startI / pageSize) + 1;
        int returnStart = pageNum * pageSize;
        PageHelper.startPage(pageNum, pageSize, null).setReasonable(true);
        List<UEditorImageDTO> dtoList = teacherService.selectTeacherPics(teacherId);
        long total = new PageInfo(dtoList).getTotal();
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("state", "SUCCESS");
        jsonMap.put("list", dtoList);
        jsonMap.put("start", returnStart);
        jsonMap.put("total", total);
        return JSONObject.parseObject(JSON.toJSONString(jsonMap));
    }

    private String uploadImageToQCloud(List<Object> param) throws Exception {
        MultipartFile upFile = (MultipartFile) param.get(1);
        Long teacherId = (Long) param.get(2);
        String suffix = StringUtils.isEmpty(upFile.getOriginalFilename()) ? "png" : upFile.getOriginalFilename().substring(upFile.getOriginalFilename().lastIndexOf(".") + 1);
        String fileName = StringUtils.format("{}.{}", getUUID(), suffix);
        String filePath = IMGE_PATH + fileName;
        File file = new File(filePath);
        upFile.transferTo(file);
        return upload(filePath, file, teacherId);
    }


    private String upload(String serverFilePath, File file, Long teacherId) {
        //文件不存在
        if (!file.exists()) {
            return null;
        }
        try {
            VodUploadClient client = new VodUploadClient(secretId, secretKey);
            VodUploadRequest request = new VodUploadRequest();
            request.setMediaFilePath(serverFilePath);
            request.setSubAppId(vodsubAppid);
            request.setStorageRegion("ap-chongqing");
            VodUploadResponse response = client.upload("ap-chongqing", request);
            file.delete();
            log.info("Upload FileId = {}", response.getFileId());
            System.out.println("内容:" + JSON.toJSONString(response));
            // 先查找先相关fileId的记录是否已经存在，如果存在则报错
            TTeacher tTeacher = teacherId == null ? null : teacherService.selectTTeacherByTeacherId(teacherId);
            if (tTeacher != null) {
                PicDTO tPic = new PicDTO();
                tPic.setTcvodFileId(response.getFileId());
                //插入图片记录
                tPic.setFileName("来自图文编辑器" + getUUID());
                tPic.setTcvodMediaUrlBeforeTrans(response.getMediaUrl());
                tPic.setTeacherId(teacherId);
                //例如生成1000-9999之间的随机数
                long num = (long) (Math.random() * (9999 - 1000 + 1) + 1000);
                tPic.setFileOriginalSize(num);
                //不需要转码的
                tPic.setFileAfterTransSize(num);
                tPic.setPathUrl(response.getMediaUrl());
                tPic.setTranscodeStatus(1);
                tPic.setAppNameType(tTeacher.getAppNameType());
                int row  = teacherService.insertTPic(tPic);
            }
            return response.getMediaUrl();
        } catch (Exception e) {
            // 业务方进行异常处理
            log.error("Upload Err", e);
        }
        return null;
    }

    private List<Map<String, Object>> downloadImages(String[] sources, Long teacherId) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (String str : sources) {
            Map<String, Object> map = new HashMap<>();
            map.put("source", str);
            map.put("url", catchImageToQCloud(str, teacherId));
            map.put("state", "SUCCESS");
            list.add(map);
        }
        return list;
    }

    private String catchImageToQCloud(String str, Long teacherId) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        String fileSuffix = getImageSuffix(str);
        String fileName = StringUtils.format("{}.{}", getUUID(), fileSuffix);
        String filePath = IMGE_PATH + fileName;
        File file = new File(filePath);
        try {
            URL url = new URL(str);
            FileUtils.copyURLToFile(url, file, 10000, 60000);
        } catch (Exception e) {
            e.printStackTrace();
            return str;
        }
        return upload(filePath, file, teacherId);
    }

    private static String getUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    private String getImageSuffix(String str) {
        if (str.contains(".jpeg")) {
            return "jpeg";
        }
        if (str.contains(".bmp")) {
            return "bmp";
        }
        if (str.contains(".jpg")) {
            return "jpg";
        }
        if (str.contains(".png")) {
            return "png";
        }
        if (str.contains(".gif")) {
            return "gif";
        }
        return "png";
    }
}
