package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.Course;
import com.ruoyi.web.vo.AddExistCourseVO;
import com.ruoyi.web.vo.AddWecomeOrTel;
import com.ruoyi.web.vo.QueryCourseByIdVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22
 */
@RestController
@RequestMapping("/new_course_controller")
public class NewCourseController {

    @Value("${wendao.teacher.path}")
    private String path;

    @PostMapping("/addOrEdit")
    public JSONObject addOrEdit(@RequestBody Course course,HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/new_course_controller/addOrEdit";
        String url = path + uri;
        String jsonString = JSON.toJSONString(course);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }
    @PostMapping("/add_or_modify_wecome_or_tel")
    public JSONObject addWecomeOrTel(@RequestBody AddWecomeOrTel addWecomeOrTel, HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/new_course_controller/add_or_modify_wecome_or_tel";
        String url = path + uri;
        String jsonString = JSON.toJSONString(addWecomeOrTel);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    @PostMapping("/query_course_by_id")
    public JSONObject queryCourseById(@RequestBody QueryCourseByIdVO queryCourseByIdVO, HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/new_course_controller/query_course_by_id";
        String url = path + uri;
        String jsonString = JSON.toJSONString(queryCourseByIdVO);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,teacherId);
    }

    @PostMapping("/add_exist_course")
    public JSONObject addExistCourse(@RequestBody AddExistCourseVO AddExistCourseVO,HttpServletRequest request) {
        String teacherId = request.getHeader("teacherId");
        String uri = "/new_course_controller/add_exist_course";
        String url = path + uri;
        String jsonString = JSON.toJSONString(AddExistCourseVO);
        return HttpClientPostFormUtil.postRequestBody(url,jsonString,teacherId);
    }


}
