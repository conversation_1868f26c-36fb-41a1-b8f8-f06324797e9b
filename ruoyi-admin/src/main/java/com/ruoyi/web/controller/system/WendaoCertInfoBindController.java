package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.WendaoCertInfoBind;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;


@RestController
@RequestMapping("/cert_douyin_bind")
public class WendaoCertInfoBindController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;





    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map , HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        if (Objects.isNull(teacherId)){
            String uri = "/cert_douyin_bind/list";
            String url = path + uri;
            return JSON.parseObject(HttpClientPostFormUtil.sendGetMap(url, map, null));
        }else {
            String s = teacherId;
            String uri = "/cert_douyin_bind/list";
            String url = path + uri;
            return JSON.parseObject(HttpClientPostFormUtil.sendGetMap(url, map, Long.valueOf(s)));
        }
    }

    @GetMapping(value = "/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id, HttpServletRequest request)
    {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/cert_douyin_bind/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }


    @PostMapping
    public JSONObject add(@RequestBody WendaoCertInfoBind wendaoCertInfoBind, HttpServletRequest request)
    {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/cert_douyin_bind";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoCertInfoBind);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    @PutMapping
    public JSONObject edit(@RequestBody WendaoCertInfoBind wendaoCertInfoBind, HttpServletRequest request)
    {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/cert_douyin_bind";
        String url = path + uri;
        String jsonString = JSON.toJSONString(wendaoCertInfoBind);
        return HttpClientPostFormUtil.putRequestBody(url, jsonString,teacherId);
    }


    @DeleteMapping("/{ids}")
    public JSONObject remove(@PathVariable Long[] ids, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String arrayAsString = Arrays.toString(ids);
        String result = arrayAsString.substring(1, arrayAsString.length() - 1).replaceAll("\\s", "");
        String uri = path + "/cert_douyin_bind/"+ result;
        return HttpClientPostFormUtil.deleteRequestBody(uri,teacherId);

    }
}
