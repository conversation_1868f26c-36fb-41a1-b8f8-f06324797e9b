package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.PromoterFlowRecordVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 推广员流水记录Controller
 *
 * <AUTHOR>
 * @date 2023-10-12
 */
@RestController
@RequestMapping("/promoter_flow_record")
public class PromoterFlowRecordController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;
    /**
     * 查询推广员流水记录列表
     */
    @PostMapping("/selectAll")
    public JSONObject selectAll(@RequestBody PromoterFlowRecordVO promoterFlowRecordVO) {
        String uri = "/promoter_flow_record/selectAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(promoterFlowRecordVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }


}
