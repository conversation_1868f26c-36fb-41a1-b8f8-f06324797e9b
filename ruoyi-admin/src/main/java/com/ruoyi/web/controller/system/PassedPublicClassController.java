package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.PassedPublicClass;
import com.ruoyi.system.domain.PublicWendaoCertInfo;
import com.ruoyi.system.service.IPassedPublicClassService;
import com.ruoyi.system.service.IPublicWendaoCertInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公共资质授权Controller
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/system/passed_cert_class")
public class PassedPublicClassController extends BaseController
{
    @Autowired
    private IPassedPublicClassService passedPublicClassService;

    @Autowired
    private IPublicWendaoCertInfoService publicWendaoCertInfoService;

    /**
     * 查询公共资质授权列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PublicWendaoCertInfo publicWendaoCertInfo)
    {
        startPage();
        //PublicWendaoCertInfo publicWendaoCertInfo = new PublicWendaoCertInfo();
        publicWendaoCertInfo.setAuditStatus(1);
        List<PublicWendaoCertInfo> publicWendaoCertInfos = publicWendaoCertInfoService.selectPublicWendaoCertInfoList(publicWendaoCertInfo);
        List<PassedPublicClass> collect = publicWendaoCertInfos.stream().map(item -> {
            PassedPublicClass pc = new PassedPublicClass();
            BeanUtils.copyProperties(item, pc);
            pc.setCertPublicName(item.getClassTeacherName()+"的资质");
            return pc;
        }).collect(Collectors.toList());
        return getDataTable(collect);
    }

    /**
     * 导出公共资质授权列表
     */
    @Log(title = "公共资质授权", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PassedPublicClass passedPublicClass)
    {
        List<PassedPublicClass> list = passedPublicClassService.selectPassedPublicClassList(passedPublicClass);
        ExcelUtil<PassedPublicClass> util = new ExcelUtil<PassedPublicClass>(PassedPublicClass.class);
        util.exportExcel(response, list, "公共资质授权数据");
    }

    /**
     * 获取公共资质授权详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(passedPublicClassService.selectPassedPublicClassById(id));
    }

    /**
     * 新增公共资质授权
     */
    @Log(title = "公共资质授权", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PassedPublicClass passedPublicClass)
    {
        return toAjax(passedPublicClassService.insertPassedPublicClass(passedPublicClass));
    }

    /**
     * 修改公共资质授权
     */
    @Log(title = "公共资质授权", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PassedPublicClass passedPublicClass)
    {
        return toAjax(passedPublicClassService.updatePassedPublicClass(passedPublicClass));
    }

    /**
     * 删除公共资质授权
     */
    @Log(title = "公共资质授权", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(passedPublicClassService.deletePassedPublicClassByIds(ids));
    }
}
