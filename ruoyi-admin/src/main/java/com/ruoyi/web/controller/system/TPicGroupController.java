package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.TPicGroupVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 图片分组Controller
 */
@RestController
@RequestMapping("/pic_group")
public class TPicGroupController extends BaseController {

//    private final String url = "http://192.168.2.41:9500";

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 查询图片分组列表
     */
    @GetMapping("/list")
    public JSONObject list(@RequestParam Map<String,String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/pic_group/list";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        JSONObject jsonObject = JSON.parseObject(s);

        return jsonObject;
    }

    /**
     * 新增图片分组,限制为某个用户
     */
    @PostMapping
    public JSONObject add(@RequestBody TPicGroupVO tPicGroupVO, HttpServletRequest request) {
        String uri = path + "/pic_group";
        String teacherId = request.getHeader("Teacherid");
        String jsonString = JSON.toJSONString(tPicGroupVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 修改图片分组
     *
     * @param tPicGroupVO
     * @return
     */
    @PutMapping
    public JSONObject edit(@RequestBody TPicGroupVO tPicGroupVO, HttpServletRequest request) {
        String uri = path + "/pic_group";
        String teacherId = request.getHeader("Teacherid");
        String jsonString = JSON.toJSONString(tPicGroupVO);
        return HttpClientPostFormUtil.putRequestBody(uri, jsonString, teacherId);
    }

}
