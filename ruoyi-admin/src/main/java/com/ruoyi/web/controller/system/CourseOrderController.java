package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.system.dto.ChangeOrderMobileDTO;
import com.ruoyi.system.dto.ChangeShopMobileDTO;
import com.ruoyi.system.dto.SendSmsForChangeShopMobileDTO;
import com.ruoyi.system.service.IChangeOrderMobileRecordService;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.SmsService;
import com.ruoyi.web.dto.*;
import com.ruoyi.web.vo.CourseOrderExportVO;
import com.ruoyi.web.vo.CourseOrderPromoterVO;
import com.ruoyi.web.vo.CourseOrderRecordVO;
import com.ruoyi.web.vo.CourseOrderVO;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.system.domain.ChangeOrderMobileRecord;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 订单信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@RestController
@RequestMapping("/course_order")
public class CourseOrderController extends BaseController {

    @Value("${wendao.order.path}")
    private String path;

    @Value("${wendao.teacher.path}")
    private String teacherPath;

    @Autowired
    private SmsService smsService;

    @Autowired
    private RedisCache redisCache;
    private static final String doudian_resend_sms_redis_prefix = "doudian_resend_sms_redis_prefix:";

    @Autowired
    private IEnterInformationService enterInformationService;
    @Autowired
    private IChangeOrderMobileRecordService changeOrderMobileRecordService;

    @GetMapping("/doudian_sync_coupons")
    public JSONObject sendSmsForChangeMobile(@RequestParam("shopId") Long shopId,@RequestParam("orderId") String orderId,@RequestParam("num") Integer num,@RequestParam("teacherId") Long teacherId) {
        String uri = "/doudian_controller/syncCoupons?shopId="+shopId+"&orderId="+orderId+"&num="+num;
        String url = teacherPath + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,teacherId));
    }


    @PostMapping("/sendSmsForChangeMobile")
    public AjaxResult sendSmsForChangeMobile(@RequestBody SendSmsForChangeShopMobileDTO sendSmsForChangeShopMobileDTO) {
        if(sendSmsForChangeShopMobileDTO==null||StringUtils.isBlank(sendSmsForChangeShopMobileDTO.getPhoneNumber())||StringUtils.length(sendSmsForChangeShopMobileDTO.getPhoneNumber())!=11){
            return AjaxResult.error("请正确输入手机号码");
        }
        String code = RandomStringUtils.randomNumeric(4);
        String uuid = smsService.sendValidateCodeSms(sendSmsForChangeShopMobileDTO.getPhoneNumber(), code);
        Map<String,String> map = new HashMap<>();
        map.put("uuid",uuid);
        return AjaxResult.success(map);
    }

    @PostMapping("/changeOrderMobile")
    public JSONObject changeOrderMobileByBackend20241210(@RequestBody ChangeOrderMobileDTO changeOrderMobileDTO) {
//        ChangeShopMobileDTO changeShopMobileDTO = new ChangeShopMobileDTO();
//        changeShopMobileDTO.setMasterPhoneNumber(changeOrderMobileDTO.getOperatorMobile());
//        changeShopMobileDTO.setCode(changeOrderMobileDTO.getCode());
//        changeShopMobileDTO.setUuid(changeOrderMobileDTO.getUuid());
//        int count = enterInformationService.countSmsRecord(changeShopMobileDTO);
//        if(count<1){
//            AjaxResult result  = AjaxResult.error("验证码错误");
//            return JSON.parseObject(JSON.toJSONString(result));
//        }
//        ChangeOrderMobileRecord changeOrderMobileRecord = new ChangeOrderMobileRecord();
//        changeOrderMobileRecord.setOrderId(changeOrderMobileDTO.getOrderId());
//        changeOrderMobileRecord.setOperatorMobile(changeOrderMobileDTO.getOperatorMobile());
//        //changeOrderMobileRecord.setOldBuyerUserMobile(changeOrderMobileDTO.getBuyerUserMobile());
//        changeOrderMobileRecord.setNewBuyerUserMobile(changeOrderMobileDTO.getBuyerUserMobile());
//        changeOrderMobileRecordService.insertChangeOrderMobileRecord(changeOrderMobileRecord);
        String uri = "/course_order/changeOrderMobileByBackend20241210?orderId="+changeOrderMobileDTO.getOrderId()+"&buyerUserMobile="+changeOrderMobileDTO.getBuyerUserMobile();
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,changeOrderMobileDTO.getTeacherId()));
    }

    /**
     * 重发抖店短信
     */
    @PostMapping("/doudianSmsResend")
    public JSONObject doudianSmsResend(@RequestBody DoudianSmsResendDTO doudianSmsResendDTO) {
        if (StringUtils.isBlank(doudianSmsResendDTO.getPhoneNumber())) {
            AjaxResult result = AjaxResult.error("手机号不能为空");
            return JSON.parseObject(JSON.toJSONString(result));
        }
        if (StringUtils.isBlank(doudianSmsResendDTO.getOrderId())) {
            AjaxResult result = AjaxResult.error("订单号不能为空");
            return JSON.parseObject(JSON.toJSONString(result));
        }
        String code = redisCache.getCacheObject(doudian_resend_sms_redis_prefix + doudianSmsResendDTO.getOrderId());
        if (StringUtils.isBlank(code)) {
            AjaxResult result = AjaxResult.error("此订单不能重发!code不存在!");
            return JSON.parseObject(JSON.toJSONString(result));
        }
        String uri="/doudian_hexiao_controller/backendResendSms";
        String url = path + uri;
        String jsonString = JSON.toJSONString(doudianSmsResendDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, "3");
    }

    /**
     * 获取订单信息详细信息
     */
    @GetMapping(value = "/{orderId}")
    public JSONObject getInfo(@PathVariable("orderId") String orderId) {
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/course_order/"+orderId;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,teacherId));
    }

    /**
     * 获取订单信息详细信息
     */
    @GetMapping(value = "/getCourseIdNumberByCourseId")
    public JSONObject getCourseIdNumberByCourseId(@RequestParam("courseId") Long courseId) {
        String uri = "/course_order/getCourseIdNumberByCourseId";
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendPost(url,"courseId="+courseId));
    }




    /**
     * 检索查询订单信息列表
     */
    @PostMapping("/selectAll")
    public JSONObject selectAll(@RequestBody CourseOrderVO courseOrderVO) {
        if (Objects.nonNull(courseOrderVO.getTeacherId())) {
            Long teacherId = courseOrderVO.getTeacherId();
            String uri = "/course_order/selectAll";
            String url = path + uri;
            String jsonString = JSON.toJSONString(courseOrderVO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
        }else {
            String uri = "/course_order/selectAll";
            String url = path + uri;
            String jsonString = JSON.toJSONString(courseOrderVO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, "0");
        }

    }

    /**
     * 检索查询订单信息列表,返回json格式
     */
    @GetMapping(value= "/genWxUrlLink",produces="application/json;charset=UTF-8")
    public String genWxUrlLink(@RequestParam("orderId") String orderId, @RequestParam("appNameType") Integer appNameType, @RequestParam(value = "source", required = false) String source) {
        String uri = "/course_order/genWxUrlLink";
        String url = path + uri;
        Map<String, String> param = new HashMap<>();
        param.put("orderId", orderId);
        param.put("appNameType", String.valueOf(appNameType));
        param.put("source", source);
        return HttpClientPostFormUtil.sendGetMap(url, param, null);

    }

    /**
     * 检索订单  购买记录
     */
    @PostMapping("/selectBuyRecord")
    public JSONObject selectBuyRecord(@RequestBody CourseOrderRecordVO courseOrderRecordVO) {
//        Long teacherId = SecurityUtils.getUserId();
        String uri = "/course_order/selectBuyRecord";
        String url = path + uri;
        String jsonString = JSON.toJSONString(courseOrderRecordVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, courseOrderRecordVO.getTeacherId().toString());
    }

    /**
     * 导出订单信息列表
     */
    @PostMapping("/export")
    public JSONObject export(@RequestBody CourseOrderExportVO courseOrderExportVO){
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/course_order/export";
        String url = path + uri;
        String jsonString = JSON.toJSONString(courseOrderExportVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }

    /**
     * 导出推广员订单信息列表
     */
    @PostMapping("/exportPromoter")
    public JSONObject exportPromoter(@RequestBody CourseOrderExportVO courseOrderExportVO){
        Long teacherId = SecurityUtils.getUserId();
        String uri = "/course_order/exportPromoter";
        String url = path + uri;
        String jsonString = JSON.toJSONString(courseOrderExportVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId.toString());
    }

    /**
     * 课程管理 推广记录
     */
    @PostMapping("/selectCourseOrderPromoter")
    public JSONObject selectCourseOrderPromoter(@RequestBody CourseOrderPromoterVO courseOrderPromoterVO) {
        String uri = "/course_order/selectCourseOrderPromoter";
        String url = path + uri;
        String jsonString = JSON.toJSONString(courseOrderPromoterVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, courseOrderPromoterVO.getTeacherId().toString());
    }

    /**
     * 线上退款
     */
    @PostMapping("/onlineRefund")
    public JSONObject onlineRefund(@RequestBody CourseOrderDTO courseOrderDTO) {
        // 获取当前时间的 Calendar 对象
        Calendar currentTime = Calendar.getInstance();

        // 创建一个表示七天后的 Calendar 对象
        Calendar sevenDaysLater = Calendar.getInstance();
        sevenDaysLater.add(Calendar.DAY_OF_MONTH, 7); // 当前日期增加七天

        // 创建一个表示十五天后的 Calendar 对象
        Calendar fifteenDaysLater = Calendar.getInstance();
        fifteenDaysLater.add(Calendar.DAY_OF_MONTH, -15); // 当前日期增加十五天

        // 获取 orderTime 的 Calendar 对象
        Date orderTime = courseOrderDTO.getOrderTime();
        Calendar orderCalendar = Calendar.getInstance();
        orderCalendar.setTime(orderTime);

        // 判断 orderTime 是否距离当前时间超过七天且小于十五天
        Calendar sevenDaysAgo = (Calendar) currentTime.clone();
        sevenDaysAgo.add(Calendar.DAY_OF_MONTH, -7); // 当前日期减去七天
        if (orderCalendar.before(sevenDaysAgo) && orderCalendar.after(fifteenDaysLater)) {
            String uri ="/dy_trade_system_controller/create_refund_section";
            String url = path + uri;
            String orderId = courseOrderDTO.getOrderId();
            CreateRefundSectionDTO wendaoCreateRefundDTO = new CreateRefundSectionDTO();
            wendaoCreateRefundDTO.setOrderId(orderId);
            wendaoCreateRefundDTO.setRefundPrice(courseOrderDTO.getRefundPrice());
            String jsonString = JSON.toJSONString(wendaoCreateRefundDTO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
        } else {
            String uri = "/dy_trade_system_controller/create_refund";
            String url = path + uri;
            String orderId = courseOrderDTO.getOrderId();
            WendaoCreateRefundDTO wendaoCreateRefundDTO = new WendaoCreateRefundDTO();
            wendaoCreateRefundDTO.setOrderId(orderId);
            String jsonString = JSON.toJSONString(wendaoCreateRefundDTO);
            return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
        }

    }


    /**
     * 线上退款
     */
    @PostMapping("/onlineRefundCe")
    public JSONObject onlineRefundCe(@RequestBody CourseOrderDTO courseOrderDTO) {
        String uri ="/dy_trade_system_controller/create_refund_section";
        String url = path + uri;
        String orderId = courseOrderDTO.getOrderId();
        CreateRefundSectionDTO wendaoCreateRefundDTO = new CreateRefundSectionDTO();
        wendaoCreateRefundDTO.setOrderId(orderId);
        wendaoCreateRefundDTO.setRefundPrice(courseOrderDTO.getRefundPrice());
        String jsonString = JSON.toJSONString(wendaoCreateRefundDTO);
        JSONObject jsonObject = HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
        return jsonObject;
    }


    @PostMapping("/userComplaint")
    public JSONObject userComplaint(@RequestBody UserComplaintDTO userComplaintDTO){
        String uri = "/course_order/userComplaint";
        String url = path + uri;
        String jsonString = JSON.toJSONString(userComplaintDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

    /**
     * 总后台首页数据总览
     * @param orderDataDTO
     * @return
     */
    @PostMapping("/orderData")
    public JSONObject orderData(@RequestBody OrderDataDTO orderDataDTO){
        String uri = "/course_order/orderData";
        String url = path + uri;
        String jsonString = JSON.toJSONString(orderDataDTO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, null);
    }

}
