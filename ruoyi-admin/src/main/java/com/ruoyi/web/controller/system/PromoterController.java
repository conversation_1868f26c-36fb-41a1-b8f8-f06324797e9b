package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.domain.PromoterCourse;
import com.ruoyi.web.vo.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 推广员Controller
 *
 * <AUTHOR>
 * @date 2023-07-29
 */
@RestController
@RequestMapping("/promoter")
public class PromoterController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 查询指定推广员
     */
    @PostMapping("/selectAll")
    public JSONObject selectPromoterAll(@RequestBody PromoterVO promoterVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/selectAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(promoterVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    /**
     * 修改推广员状态
     *
     * @param promoterStateVO
     * @return
     */
    @PostMapping("/updateStatus")
    public JSONObject remove(@RequestBody PromoterStateVO promoterStateVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/updateStatus";
        String url = path + uri;
        String jsonString = JSON.toJSONString(promoterStateVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 查询推广员领取记录列表
     * @return
     */
//    @RequiresPermissions("teacher:admin:list")
    @PostMapping("/receive/list")
    public JSONObject receiveList(@RequestBody PromoterAndReceiveVO promoterAndReceiveVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/receive/list";
        String url = path + uri;
        String jsonString = JSON.toJSONString(promoterAndReceiveVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }

    /**
     * 添加推广员
     * @param promoterCourseVO
     * @param request
     * @return
     */
    @PostMapping("/add")
    public JSONObject add(@RequestBody PromoterCourseVO promoterCourseVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/add";
        String url = path + uri;
        String jsonString = JSON.toJSONString(promoterCourseVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 添加临时推广员
     * @param promoterCourseVO
     * @param request
     * @return
     */
    @PostMapping("/add/promoter_temp")
    public JSONObject addPromoterTemp(@RequestBody PromoterCourseVO promoterCourseVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/add/promoter_temp";
        String url = path + uri;
        String jsonString = JSON.toJSONString(promoterCourseVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 获取推广员详细信息
     */
    @GetMapping(value = "/detail/{id}")
    public JSONObject getInfo(@PathVariable("id") Long id,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/detail/" + id;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,Long.valueOf(teacherId)));
    }



//
    /**
     * 获取手机号授权账号信息
     */
    @GetMapping(value = "/account/{phone}")
    public JSONObject getAccountInformation(@PathVariable("phone") String phone,@RequestParam("appNameType") Integer appNameType, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/account/" + phone;
        String url = path + uri;
        return JSON.parseObject(HttpClientPostFormUtil.sendGet(url,"appNameType="+appNameType,Long.valueOf(teacherId)));
    }

    /**
     * 修改推广员
     *
     * @param promoterCourseVO
     * @param request
     * @return
     */
//    @WenDaoLog(title = "营销中心", subTitle = "推广员管理", businessType = "修改推广员")
    @PostMapping("/updatePromoter")
    public JSONObject updatePromoter(@RequestBody PromoterCourseVO promoterCourseVO, HttpServletRequest request) {
        //获取教师id
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/updatePromoter" ;
        String url = path + uri;
        String jsonString = JSON.toJSONString(promoterCourseVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);

    }

    /**
     * 生成推广员绑定信息二二维码
     * @param
     * @return
     */
    @GetMapping("/createPromoterQrCode")
    public JSONObject createPromoterQrCode(@RequestParam Map<String,String> map, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/promoter/createPromoterQrCode";
        String s = HttpClientPostFormUtil.sendGetMap(uri, map, Long.valueOf(teacherId));
        JSONObject jsonObject = JSON.parseObject(s);
        return jsonObject;
    }

//        /**
//     * 根据id修改状态
//     */
//    @GetMapping("/updatePromotionStatus/{ids}")
//    public JSONObject updatePromotionStatus(@PathVariable Long[] ids) {
//        String uri = "/promoter/createPromoterQrCode/" + ids;
//        String url = path + uri;
//        return  JSON.parseObject(HttpClientPostFormUtil.sendGet(url, null));
//    }
//    /**
//     * 查询推广员列表
//     */
//    @RequiresPermissions("teacher:promoter:list")
//    @GetMapping("/list")
//    public TableDataInfo list(Promoter promoter)
//    {
//        startPage();
//        List<Promoter> list = promoterService.selectPromoterList(promoter);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出推广员列表
//     */
//    @RequiresPermissions("teacher:promoter:export")
//    @Log(title = "推广员", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, Promoter promoter)
//    {
//        List<Promoter> list = promoterService.selectPromoterList(promoter);
//        ExcelUtil<Promoter> util = new ExcelUtil<Promoter>(Promoter.class);
//        util.exportExcel(response, list, "推广员数据");
//    }
//

    //
//    /**
//     * 新增推广员
//     */
//    @RequiresPermissions("teacher:promoter:add")
//    @Log(title = "推广员", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody Promoter promoter)
//    {
//        return toAjax(promoterService.insertPromoter(promoter));
//    }
//
//    /**
//     * 修改推广员
//     */
//    @RequiresPermissions("teacher:promoter:edit")
//    @Log(title = "推广员", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody Promoter promoter)
//    {
//        return toAjax(promoterService.updatePromoter(promoter));
//    }
//
//    /**
//     * 删除推广员
//     */
//    @RequiresPermissions("teacher:promoter:remove")
//    @Log(title = "推广员", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(promoterService.deletePromoterByIds(ids));
//    }
//    private String getString(HttpServletRequest request) {
//        String strTeacherId = request.getHeader(SecurityConstants.DETAILS_USER_ID);
//        if (StringUtils.isBlank(strTeacherId)) {
//            throw new SecurityException("没有权限");
//        }
//        Long teacherId = new Long(strTeacherId);
//        if (teacherId.longValue() == 0) {
//            throw new SecurityException("参数错误");
//        }
//        return strTeacherId;
//    }

    /**
     * 查询指定推广员
     */
    @PostMapping("/getPromoterAll")
    public JSONObject getPromoterAll(@RequestBody CoursePromoterVO coursePromoterVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/getPromoterAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(coursePromoterVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);

    }
    /**
     * 查询添加推广员列表
     */
    @PostMapping("/getPromoterAddAll")
    public JSONObject getPromoterAddAll(@RequestBody CoursePromoterVO coursePromoterVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/getPromoterAddAll";
        String url = path + uri;
        String jsonString = JSON.toJSONString(coursePromoterVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString, teacherId);
    }

    /**
     * 添加推广员推广课程
     */
    @PostMapping("/addPromoterCourse")
    public JSONObject addPromoterCourse(@RequestBody List<PromoterCourse> promoterCourseList,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/addPromoterCourse";
        String url = path + uri;
        String jsonString = JSON.toJSONString(promoterCourseList);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }


    /**
     * 指定推广员列表-修改推广课程比例
     *
     * @param appointPromoterVO
     * @return
     */
    @PostMapping("/updateCoursePromoter")
    public JSONObject updateCoursePromoter(@RequestBody AppointPromoterVO appointPromoterVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/updateCoursePromoter";
        String url = path + uri;
        String jsonString = JSON.toJSONString(appointPromoterVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }

    /**
     * 指定推广员列表-删除推广员课程
     *
     * @param appointPromoterVO
     * @return
     */
    @PostMapping("/deleteCoursePromoter")
    public JSONObject deleteCoursePromoter(@RequestBody AppointPromoterVO appointPromoterVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = "/promoter/deleteCoursePromoter";
        String url = path + uri;
        String jsonString = JSON.toJSONString(appointPromoterVO);
        return HttpClientPostFormUtil.postRequestBody(url, jsonString,teacherId);
    }



//
//    /**
//     * 根据id修改状态
//     */
//    @GetMapping("/updatePromotionStatus/{ids}")
//    public AjaxResult updatePromotionStatus(@PathVariable Long[] ids) {
//
//        return toAjax(promoterService.updatePromotionStatus(ids));
//    }



//    private String getString(HttpServletRequest request) {
//        String strTeacherId = request.getHeader(SecurityConstants.DETAILS_USER_ID);
//        if (StringUtils.isBlank(strTeacherId)) {
//            throw new SecurityException("没有权限");
//        }
//        Long teacherId = new Long(strTeacherId);
//        if (teacherId.longValue() == 0) {
//            throw new SecurityException("参数错误");
//        }
//        return strTeacherId;
//    }
}
