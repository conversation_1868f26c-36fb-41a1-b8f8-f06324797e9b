package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.IEnterInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/employee_backend_data")
public class EmployeeBackendDataController {
    @Autowired
    private IEnterInformationService enterInformationService;

    /**
     * 查询7天每天的开户数量,昨天开始往前推7天,今天的数据不算
     * 查询audit_type为0且update_time在每一天时间范围内的数量
     * @return
     */
    @PostMapping(value = "/querySevenDaysOpenCount")
    public AjaxResult querySevenDaysOpenCount() {
        try {
            List<Map<String, Object>> result = enterInformationService.querySevenDaysOpenCount();
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("查询7天开户数量失败：" + e.getMessage());
        }
    }

    /**
     * 查询7天每天的订单交易总额,昨天开始往前推7天,今天的数据不算
     * 查询order_status为1且order_time在每一天时间范围内的订单的pay_price(支付金额)总和
     * @return
     */
    @PostMapping(value = "/querySevenDaysOrderAmount")
    public AjaxResult querySevenDaysOrderAmount() {
        try {
            List<Map<String, Object>> result = enterInformationService.querySevenDaysOrderAmount();
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("查询7天订单交易总额失败：" + e.getMessage());
        }
    }
}
