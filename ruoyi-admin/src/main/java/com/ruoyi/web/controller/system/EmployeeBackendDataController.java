package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.web.dto.QueryStoreGroupDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/employee_backend_data")
public class EmployeeBackendDataController {
    @Autowired
    private IEnterInformationService enterInformationService;

    /**
     * 查询7天每天的开户数量,昨天开始往前推7天,今天的数据不算
     * @param queryStoreGroupDTO
     * @return
     */
    @PostMapping(value = "/querySevenDaysOpenCount")
    public AjaxResult querySevenDaysOpenCount(@RequestBody QueryStoreGroupDTO queryStoreGroupDTO) {
        return AjaxResult.success();
    }
}
