package com.ruoyi.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.http.HttpClientPostFormUtil;
import com.ruoyi.web.vo.AddVideoGroupRelationVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * 视频和分组关系Controller
 *
 * <AUTHOR>
 * @date 2023-07-25
 */
@RestController
@RequestMapping("/relation")
public class TVideoGroupRelationController extends BaseController {

    @Value("${wendao.teacher.path}")
    private String path;

    /**
     * 新增视频和分组关系
     */
    @PostMapping
    public JSONObject add(@RequestBody AddVideoGroupRelationVO addVideoGroupRelationVO, HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/relation";
        String jsonString = JSON.toJSONString(addVideoGroupRelationVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }

    /**
     * 将视频从分组中移除
     *
     * @param addVideoGroupRelationVO
     * @return
     */
    @PostMapping("/remove_from_group")
    public JSONObject removeFromGroup(@RequestBody AddVideoGroupRelationVO addVideoGroupRelationVO,HttpServletRequest request) {
        String teacherId = request.getHeader("Teacherid");
        String uri = path + "/relation/remove_from_group";
        String jsonString = JSON.toJSONString(addVideoGroupRelationVO);
        return HttpClientPostFormUtil.postRequestBody(uri, jsonString, teacherId);
    }
}
