package com.ruoyi.web.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.web.domain.CourseMachineAudit;
import com.ruoyi.web.domain.CourseMachineAuditSub;
import com.ruoyi.web.dto.CourseHumanAuditDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Service
@FeignClient(url = "${wendao.teacher.path}", name = "courseMachineAuditService")
public interface CourseMachineAuditService {
    @PostMapping("/course_machine_audit/list")
    TableDataInfo list(@RequestBody CourseMachineAudit courseMachineAudit,@RequestParam(value = "pageNum",required = false) Integer pageNum,@RequestParam(value = "pageSize",required = false) Integer pageSize);

    @PostMapping("/course_machine_audit_sub/list")
    TableDataInfo listSub(@RequestBody CourseMachineAuditSub courseMachineAuditSub, @RequestParam(value = "pageNum",required = false)Integer pageNum, @RequestParam(value = "pageSize",required = false)Integer pageSize);

    @PostMapping("/course_human_audit/submitAuditStatus")
    AjaxResult submitAuditStatus(@RequestBody CourseHumanAuditDTO courseHumanAuditDTO);

    @PostMapping("/course_human_audit/auditDetail")
    AjaxResult auditDetail(@RequestBody CourseHumanAuditDTO courseHumanAuditDTO);
}
