package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 目录对象 course_directory
 * 
 * <AUTHOR>
 * @date 2023-08-11
 */
@Data
public class CourseDirectory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private String frontUUID;

    /** 主键 */
    private Long id;

    /** 章节id */
    @Excel(name = "章节id")
    private Long chapterId;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 关联章节序号序号 */
    @Excel(name = "关联章节序号序号")
    private Integer serialNumber;

    /** 全部序号 */
    @Excel(name = "全部序号")
    private Integer serialAllNumber;

    /** 目录名称 */
    @Excel(name = "目录名称")
    private String directoryName;

    /** 图片地址  视频地址  音频地址 */
    @Excel(name = "图片地址  视频地址  音频地址")
    private String courseDirectoryUrl;

    /** 时长 */
    @Excel(name = "时长")
    private Long duration;

    /** 是否试看(0不能试看1可以试看) */
    @Excel(name = "是否试看(0不能试看1可以试看)")
    private Integer isTrySee;

    /** 试看时长 */
    @Excel(name = "试看时长")
    private Long trySeeDuration;

    /** 移除章节状态 0否 1是 */
    @Excel(name = "移除章节状态 0否 1是")
    private Integer removeChapter;

    /** 删除（0未删除1已删除） */
    @Excel(name = "删除", readConverterExp = "0=未删除1已删除")
    private Integer isDelete;

    /** 章节名称 */
    @Excel(name = "章节名称")
    private String chaptyerName;

    /** 目录类型1视频，2音频，3图片 */
    @Excel(name = "目录类型1视频，2音频，3图片")
    private Integer directoryType;

    
}
