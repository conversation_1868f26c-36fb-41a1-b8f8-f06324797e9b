package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 章节对象 chapter
 * 
 * <AUTHOR>
 * @date 2023-07-29
 */
public class Chapter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private List<CourseDirectory> courseDirectoryList;

    /** $column.columnComment */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程主键id */
    @Excel(name = "课程主键id")
    private Long courseId;

    /** 序号数 */
    @Excel(name = "序号数")
    private Integer serialNumber;

    /** 章节名称 */
    @Excel(name = "章节名称")
    private String chapterName;

    /** 删除（0未删除1已删除） */
    @Excel(name = "删除", readConverterExp = "0=未删除1已删除")
    private Integer isDelete;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setChapterName(String chapterName) 
    {
        this.chapterName = chapterName;
    }

    public String getChapterName() 
    {
        return chapterName;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getIsDelete()
    {
        return isDelete;
    }

    public List<CourseDirectory> getCourseDirectoryList() {
        return courseDirectoryList;
    }

    public void setCourseDirectoryList(List<CourseDirectory> courseDirectoryList) {
        this.courseDirectoryList = courseDirectoryList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("courseId", getCourseId())
            .append("chapterName", getChapterName())
            .append("isDelete", getIsDelete())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
