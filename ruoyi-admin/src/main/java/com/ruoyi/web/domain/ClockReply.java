package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 打卡回复对象 clock_reply
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
public class ClockReply extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 打卡记录表id
     */
    @Excel(name = "打卡记录表id")
    private Long clockRecordId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String userImg;

    /**
     * 打卡内容
     */
    @Excel(name = "打卡内容 ")
    private String clockContent;

    /**
     * 回复人id
     */
    @Excel(name = "回复人id")
    private Long replyId;

    /**
     * 回复人名称
     */
    @Excel(name = "回复人名称")
    private String replyName;

    /**
     * 回复人头像
     */
    @Excel(name = "回复人头像")
    private String replyImg;

    /**
     * 是否隐藏评论 0否 1是
     */
    @Excel(name = "是否隐藏评论 0否 1是")
    private Integer hideContentType;

    /**
     * 父id
     */
    @Excel(name = "父id")
    private Long parentId;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setClockRecordId(Long clockRecordId) {
        this.clockRecordId = clockRecordId;
    }

    public Long getClockRecordId() {
        return clockRecordId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserImg(String userImg) {
        this.userImg = userImg;
    }

    public String getUserImg() {
        return userImg;
    }

    public void setclockContent(String clockContent) {
        this.clockContent = clockContent;
    }

    public String getclockContent() {
        return clockContent;
    }

    public void setReplyId(Long replyId) {
        this.replyId = replyId;
    }

    public Long getReplyId() {
        return replyId;
    }

    public void setReplyName(String replyName) {
        this.replyName = replyName;
    }

    public String getReplyName() {
        return replyName;
    }

    public void setReplyImg(String replyImg) {
        this.replyImg = replyImg;
    }

    public String getReplyImg() {
        return replyImg;
    }

    public void setHideContentType(Integer hideContentType) {
        this.hideContentType = hideContentType;
    }

    public Integer getHideContentType() {
        return hideContentType;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("clockRecordId", getClockRecordId())
                .append("userId", getUserId())
                .append("userName", getUserName())
                .append("userImg", getUserImg())
                .append("clockContent", getclockContent())
                .append("replyId", getReplyId())
                .append("replyName", getReplyName())
                .append("replyImg", getReplyImg())
                .append("hideContentType", getHideContentType())
                .append("parentId", getParentId())
                .append("isDelete", getIsDelete())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
