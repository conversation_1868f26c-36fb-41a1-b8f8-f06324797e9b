package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 课程评论草稿对象 review_course_draft
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
public class ReviewCourseDraft extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单id
     */
    @Excel(name = "订单id")
    private String orderId;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String userImg;

    /**
     * 评论分数 1,2,3,4,5
     */
    @Excel(name = "评论分数 1,2,3,4,5")
    private Integer contentMark;

    /**
     * 评论内容
     */
    @Excel(name = "评论内容")
    private String content;

    /**
     * 是否置顶 0否 1是
     */
    @Excel(name = "是否置顶 0否 1是")
    private Integer isPinned;

    /**
     * 是否隐藏 0否 1是
     */
    @Excel(name = "是否隐藏 0否 1是")
    private Integer isConceal;

    /**
     * 是否匿名提交 0否 1是
     */
    @Excel(name = "是否匿名提交 0否 1是")
    private Integer isAnonymity;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserImg(String userImg) {
        this.userImg = userImg;
    }

    public String getUserImg() {
        return userImg;
    }

    public void setContentMark(Integer contentMark) {
        this.contentMark = contentMark;
    }

    public Integer getContentMark() {
        return contentMark;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setIsPinned(Integer isPinned) {
        this.isPinned = isPinned;
    }

    public Integer getIsPinned() {
        return isPinned;
    }

    public void setIsConceal(Integer isConceal) {
        this.isConceal = isConceal;
    }

    public Integer getIsConceal() {
        return isConceal;
    }

    public void setIsAnonymity(Integer isAnonymity) {
        this.isAnonymity = isAnonymity;
    }

    public Integer getIsAnonymity() {
        return isAnonymity;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("orderId", getOrderId())
                .append("courseId", getCourseId())
                .append("userId", getUserId())
                .append("userName", getUserName())
                .append("userImg", getUserImg())
                .append("contentMark", getContentMark())
                .append("content", getContent())
                .append("isPinned", getIsPinned())
                .append("isConceal", getIsConceal())
                .append("isAnonymity", getIsAnonymity())
                .append("isDelete", getIsDelete())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
