package com.ruoyi.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 物流信息对象 deliver_goods_order
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Data
public class DeliverGoodsOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 收货人手机
     */
    @Excel(name = "收货人手机")
    private Long consigneeMobile;

    /**
     * 收货人名称
     */
    @Excel(name = "收货人名称")
    private String consigneeName;

    /**
     * 收货人地址
     */
    @Excel(name = "收货人地址")
    private String consigneeAddr;

    /**
     * 物流单号
     */
    @Excel(name = "物流单号")
    private String logisticsNumber;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderId;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /**
     * 教材名称
     */
    @Excel(name = "教材名称")
    private String teachingMaterialName;

    /**
     * 教材数量
     */
    @Excel(name = "教材数量")
    private Integer teachingMaterialNum;

    /**
     * 收货信息状态 0未填写 1已填写
     */
    @Excel(name = "收货信息状态 0未填写 1已填写")
    private Integer receivingInformationStatus;

    /**
     * 发货状态 0 未发货 1已发货
     */
    @Excel(name = "发货状态 0 未发货 1已发货")
    private Integer shipmentsStatus;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setConsigneeMobile(Long consigneeMobile) {
        this.consigneeMobile = consigneeMobile;
    }

    public Long getConsigneeMobile() {
        return consigneeMobile;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeAddr(String consigneeAddr) {
        this.consigneeAddr = consigneeAddr;
    }

    public String getConsigneeAddr() {
        return consigneeAddr;
    }

    public void setLogisticsNumber(String logisticsNumber) {
        this.logisticsNumber = logisticsNumber;
    }

    public String getLogisticsNumber() {
        return logisticsNumber;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setTeachingMaterialName(String teachingMaterialName) {
        this.teachingMaterialName = teachingMaterialName;
    }

    public String getTeachingMaterialName() {
        return teachingMaterialName;
    }

    public void setTeachingMaterialNum(Integer teachingMaterialNum) {
        this.teachingMaterialNum = teachingMaterialNum;
    }

    public Integer getTeachingMaterialNum() {
        return teachingMaterialNum;
    }

    public void setReceivingInformationStatus(Integer receivingInformationStatus) {
        this.receivingInformationStatus = receivingInformationStatus;
    }

    public Integer getReceivingInformationStatus() {
        return receivingInformationStatus;
    }

    public void setShipmentsStatus(Integer shipmentsStatus) {
        this.shipmentsStatus = shipmentsStatus;
    }

    public Integer getShipmentsStatus() {
        return shipmentsStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("consigneeMobile", getConsigneeMobile())
                .append("consigneeName", getConsigneeName())
                .append("consigneeAddr", getConsigneeAddr())
                .append("logisticsNumber", getLogisticsNumber())
                .append("teacherId", getTeacherId())
                .append("orderId", getOrderId())
                .append("payTime", getPayTime())
                .append("teachingMaterialName", getTeachingMaterialName())
                .append("teachingMaterialNum", getTeachingMaterialNum())
                .append("receivingInformationStatus", getReceivingInformationStatus())
                .append("shipmentsStatus", getShipmentsStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
