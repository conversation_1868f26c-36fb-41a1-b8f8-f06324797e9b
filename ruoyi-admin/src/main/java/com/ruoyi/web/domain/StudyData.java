package com.ruoyi.web.domain;


import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 学习资料对象 study_data
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
@Data
public class StudyData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Integer pageNum;

    private Integer pageSize;

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 学习文件名称 */
    @Excel(name = "学习文件名称")
    private String studyFileName;

    /** 学习文件类型 */
    @Excel(name = "学习文件类型")
    private String studyFileType;

    /** 文件url地址 */
    @Excel(name = "文件url地址")
    private String studyFileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    private Long studyFileSize;

    /** 审核状态 0未审核 1审核 */
    @Excel(name = "审核状态 0未审核 1审核")
    private Integer status;

    /** 是否删除 0否 1是 */
    @Excel(name = "是否删除 0否 1是")
    private Integer delType;



    /** 学习资料所属app  0问到好课 1问到课堂 */
    @Excel(name = "学习资料所属app  0问到好课 1问到课堂")
    private Integer appType;



    /** 搜索值 */
    @Excel(name = "搜索值")
    private String searchValue;

    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    @Override
    public String getSearchValue() {
        return searchValue;
    }

    @Override
    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setStudyFileName(String studyFileName) 
    {
        this.studyFileName = studyFileName;
    }

    public String getStudyFileName() 
    {
        return studyFileName;
    }
    public void setStudyFileType(String studyFileType) 
    {
        this.studyFileType = studyFileType;
    }

    public String getStudyFileType() 
    {
        return studyFileType;
    }
    public void setStudyFileUrl(String studyFileUrl) 
    {
        this.studyFileUrl = studyFileUrl;
    }

    public String getStudyFileUrl() 
    {
        return studyFileUrl;
    }
    public void setStudyFileSize(Long studyFileSize) 
    {
        this.studyFileSize = studyFileSize;
    }

    public Long getStudyFileSize() 
    {
        return studyFileSize;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setDelType(Integer delType) 
    {
        this.delType = delType;
    }

    public Integer getDelType() 
    {
        return delType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("courseId", getCourseId())
            .append("studyFileName", getStudyFileName())
            .append("studyFileType", getStudyFileType())
            .append("studyFileUrl", getStudyFileUrl())
            .append("studyFileSize", getStudyFileSize())
            .append("status", getStatus())
            .append("delType", getDelType())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
