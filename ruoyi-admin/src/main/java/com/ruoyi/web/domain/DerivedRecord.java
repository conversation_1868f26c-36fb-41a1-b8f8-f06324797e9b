package com.ruoyi.web.domain;


import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 导出记录对象 derived_record
 * 
 * <AUTHOR>
 * @date 2023-09-05
 */
@Data
public class DerivedRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private String pageSize;
    private String pageNum;

    /** 主键id */
    private Long id;

    /** 操作员id */
    @Excel(name = "操作员id")
    private Long operatorId;

    /** 操作员名称 */
    @Excel(name = "操作员名称")
    private String operatorName;

    /** 操作员手机号 */
    @Excel(name = "操作员手机号")
    private String operatorPhone;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setOperatorId(Long operatorId)
    {
        this.operatorId = operatorId;
    }

    public Long getOperatorId()
    {
        return operatorId;
    }
    public void setOperatorName(String operatorName)
    {
        this.operatorName = operatorName;
    }

    public String getOperatorName()
    {
        return operatorName;
    }
    public void setOperatorPhone(String operatorPhone)
    {
        this.operatorPhone = operatorPhone;
    }

    public String getOperatorPhone()
    {
        return operatorPhone;
    }
    public void setFileName(String fileName)
    {
        this.fileName = fileName;
    }

    public String getFileName()
    {
        return fileName;
    }
    public void setFilePath(String filePath)
    {
        this.filePath = filePath;
    }

    public String getFilePath()
    {
        return filePath;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("operatorId", getOperatorId())
                .append("operatorName", getOperatorName())
                .append("operatorPhone", getOperatorPhone())
                .append("fileName", getFileName())
                .append("filePath", getFilePath())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
