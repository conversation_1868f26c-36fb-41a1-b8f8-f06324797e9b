package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 课程抖音审核对象 course_dy_audit
 * 
 * <AUTHOR>
 * @date 2023-08-10
 */
public class CourseDyAudit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 课程主键id */
    @Excel(name = "课程主键id")
    private Long courseId;

    /** 抖音审核状态0未提审 1审核中 2审核通过 3审核驳回 */
    @Excel(name = "抖音审核状态0未提审 1审核中 2审核通过 3审核驳回")
    private Integer auditStatus;

    /** 审核状态如果是驳回的，则为失败消息 */
    @Excel(name = "审核状态如果是驳回的，则为失败消息")
    private String message;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setAuditStatus(Integer auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Integer getAuditStatus() 
    {
        return auditStatus;
    }
    public void setMessage(String message) 
    {
        this.message = message;
    }

    public String getMessage() 
    {
        return message;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("courseId", getCourseId())
            .append("auditStatus", getAuditStatus())
            .append("message", getMessage())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
