package com.ruoyi.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 问到投诉管理对象 wendao_complaint
 * 
 * <AUTHOR>
 * @date 2023-11-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WendaoComplaint extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long complaintId;

    /** 归属老师id（店铺id） */
    @Excel(name = "归属老师id", readConverterExp = "店=铺id")
    private Long teacherId;

    /** 关联订单号，订单表order_id */
    @Excel(name = "关联订单号，订单表order_id")
    private String orderId;

    /** 相关课程id */
    @Excel(name = "相关课程id")
    private Long courseId;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String title;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal price;

    /** 课程划线价 */
    @Excel(name = "课程划线价")
    private BigDecimal originalPrice;

    /** 购买人手机号码 */
    @Excel(name = "购买人手机号码")
    private String buyerPhoneNumber;

    /** 投诉问题 */
    @Excel(name = "投诉问题")
    private String question;

    /** 购买人昵称 */
    @Excel(name = "购买人昵称")
    private String buyerNickName;

    /** 0、抖音，1、微信，2、快手，，3、视频号 */
    @Excel(name = "0、抖音，1、微信，2、快手，，3、视频号")
    private Integer source;

    /** 投诉发起时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "投诉发起时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date complaintTime;

    /** 投诉单状态,0待处理，1平台处理中，2已处理 */
    @Excel(name = "投诉单状态,0待处理，1平台处理中，2已处理")
    private Integer complaintStatus;

    /** 投诉凭证图片 */
    @Excel(name = "投诉凭证图片")
    private String complaintImgs;

    /** 补充描述与凭证 */
    @Excel(name = "补充描述与凭证")
    private String complaintDesc;

    /** 投诉单号 */
    @Excel(name = "投诉单号")
    private String complaintNumber;

    /** 1、线上退款，2、线下退款，3线下沟通解决 */
    @Excel(name = "1、线上退款，2、线下退款，3线下沟通解决")
    private String handlingMethod;

    /** 处理描述 */
    @Excel(name = "处理描述")
    private String handlingDesc;

    /** 补充凭证 */
    @Excel(name = "补充凭证")
    private String handlingImgs;

    /** 课程主图 */
    @Excel(name = "课程主图")
    private String courseCoverUrl;

    /** 如果是退款，则为退款金额 */
    @Excel(name = "如果是退款，则为退款金额")
    private BigDecimal refundMoney;

    /** 关联订单实付金额 */
    @Excel(name = "关联订单实付金额")
    private BigDecimal orderRealPayMoney;

    /** 投诉用户id   （购买用户id） */
    @Excel(name = "投诉用户id   ", readConverterExp = "购=买用户id")
    private Long userId;

    /** 应用对应的用户openid */
    @Excel(name = "应用对应的用户openid")
    private String openid;

    /** 对应的appId */
    @Excel(name = "对应的appId")
    private String appId;

    /** 投诉类型1课程内容问题，2 订单问题，3售后服务，4课程使用方式，5其他，6视频加载异常，7退款问题 */
    @Excel(name = "投诉类型1课程内容问题，2 订单问题，3售后服务，4课程使用方式，5其他，6视频加载异常，7退款问题")
    private Integer complaintType;

    /** 投诉类型描述内容 */
    @Excel(name = "投诉类型描述内容")
    private String complaintTypeContent;


    private String alipayName;
    private String alipayAccount;
    private String operator;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("complaintId", getComplaintId())
            .append("teacherId", getTeacherId())
            .append("orderId", getOrderId())
            .append("courseId", getCourseId())
            .append("title", getTitle())
            .append("price", getPrice())
            .append("originalPrice", getOriginalPrice())
            .append("buyerPhoneNumber", getBuyerPhoneNumber())
            .append("question", getQuestion())
            .append("buyerNickName", getBuyerNickName())
            .append("source", getSource())
            .append("complaintTime", getComplaintTime())
            .append("complaintStatus", getComplaintStatus())
            .append("complaintImgs", getComplaintImgs())
            .append("complaintDesc", getComplaintDesc())
            .append("complaintNumber", getComplaintNumber())
            .append("handlingMethod", getHandlingMethod())
            .append("handlingDesc", getHandlingDesc())
            .append("handlingImgs", getHandlingImgs())
            .append("courseCoverUrl", getCourseCoverUrl())
            .append("refundMoney", getRefundMoney())
            .append("orderRealPayMoney", getOrderRealPayMoney())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("userId", getUserId())
            .append("openid", getOpenid())
            .append("appId", getAppId())
            .append("complaintType", getComplaintType())
            .append("complaintTypeContent", getComplaintTypeContent())
            .toString();
    }
}
