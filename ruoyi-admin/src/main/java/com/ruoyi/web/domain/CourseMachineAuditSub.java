package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 机审内容对象 course_machine_audit_sub
 * 
 * <AUTHOR>
 * @date 2024-01-02
 */
public class CourseMachineAuditSub extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 审核id,course_machine_audit主键id */
    @Excel(name = "审核id,course_machine_audit主键id")
    private Long pid;

    /** 当前审核中的课程的素材名称 */
    @Excel(name = "当前审核中的课程的素材名称")
    private String metrialName;

    /** 类型1视频，2音频，3图片 */
    @Excel(name = "类型1视频，2音频，3图片")
    private Integer metrialType;

    /** 雷同的素材名称 */
    @Excel(name = "雷同的素材名称")
    private String sameMetrialName;

    /** 雷同课程名称 */
    @Excel(name = "雷同课程名称")
    private String sameCourseName;

    /** 雷同店铺 */
    @Excel(name = "雷同店铺")
    private String sameShopName;

    private Long ppid;

    public Long getPpid() {
        return ppid;
    }

    public void setPpid(Long ppid) {
        this.ppid = ppid;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPid(Long pid) 
    {
        this.pid = pid;
    }

    public Long getPid() 
    {
        return pid;
    }
    public void setMetrialName(String metrialName) 
    {
        this.metrialName = metrialName;
    }

    public String getMetrialName() 
    {
        return metrialName;
    }
    public void setMetrialType(Integer metrialType) 
    {
        this.metrialType = metrialType;
    }

    public Integer getMetrialType() 
    {
        return metrialType;
    }
    public void setSameMetrialName(String sameMetrialName) 
    {
        this.sameMetrialName = sameMetrialName;
    }

    public String getSameMetrialName() 
    {
        return sameMetrialName;
    }
    public void setSameCourseName(String sameCourseName) 
    {
        this.sameCourseName = sameCourseName;
    }

    public String getSameCourseName() 
    {
        return sameCourseName;
    }
    public void setSameShopName(String sameShopName) 
    {
        this.sameShopName = sameShopName;
    }

    public String getSameShopName() 
    {
        return sameShopName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pid", getPid())
            .append("metrialName", getMetrialName())
            .append("metrialType", getMetrialType())
            .append("sameMetrialName", getSameMetrialName())
            .append("sameCourseName", getSameCourseName())
            .append("sameShopName", getSameShopName())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
