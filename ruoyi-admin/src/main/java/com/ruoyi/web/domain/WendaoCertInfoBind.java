package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资质绑定抖音账号对象 wendao_cert_info_bind
 * 
 * <AUTHOR>
 * @date 2023-09-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WendaoCertInfoBind extends BaseEntity

{
    private static final long serialVersionUID = 1L;


    private  Integer pageSize;
    private Integer pageNum;


    private Integer seq;

    /** $column.columnComment */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 实体类型 1: 个人 2:企业 */
    @Excel(name = "实体类型 1: 个人 2:企业")
    private Long entityType;

    /** 资质主键id */
    @Excel(name = "资质主键id")
    private Long certInfoId;

    /** 抖音号 */
    @Excel(name = "抖音号")
    private String douyinhao;

    /** 抖音号的openid */
    @Excel(name = "抖音号的openid")
    private String openid;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickName;

    /** 抖音号类型1个人,2企业号 */
    @Excel(name = "抖音号类型1个人,2企业号")
    private Integer douyinAccountType;

    /** 0绑定中,1已绑定,2已解绑/未绑定 */
    @Excel(name = "0绑定中,1已绑定,2已解绑/未绑定")
    private Integer bindStatus;

    /** 问到用户id,关联wendao_user */
    @Excel(name = "问到用户id,关联wendao_user")
    private Long wendaoUserId;

    /** appid */
    @Excel(name = "appid")
    private String appid;

    /** 行业角色实体ID  */
    @Excel(name = "行业角色实体ID ")
    private String merchantEntityId;

    /** 合作的服务商entity_id ，自营机构则无需填写该字段 */
    @Excel(name = "合作的服务商entity_id ，自营机构则无需填写该字段")
    private String partnerEntityId;

    /** 短视频绑定状态,0绑定中,1已绑定,2已解绑/未绑定 */
    @Excel(name = "短视频绑定状态,0绑定中,1已绑定,2已解绑/未绑定")
    private Integer shortVideoStatus;

    /** 直播绑定状态,0绑定中,1已绑定,2已解绑/未绑定 */
    @Excel(name = "直播绑定状态,0绑定中,1已绑定,2已解绑/未绑定")
    private Integer liveStreamStatus;

    private String avatarUrl;
    private Long userId;


    private String  businessLicenseCompanyName;


    private String firstClassTitle;

    private String secondClassTitle;

    private String teacherRealName;

    private Long wcentityType;

    private String entityName;


    private String qualificationType;


    private Integer appNameType;
}
