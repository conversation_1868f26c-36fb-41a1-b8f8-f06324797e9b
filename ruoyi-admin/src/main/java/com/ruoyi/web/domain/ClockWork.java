package com.ruoyi.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 打卡作业对象 clock_work
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@Data
public class ClockWork extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 打卡任务id
     */
    @Excel(name = "打卡任务id")
    private Long clockInQuestId;

    /**
     * 作业标题
     */
    @Excel(name = "作业标题")
    private String workTitle;

    /**
     * 作业内容
     */
    @Excel(name = "作业内容")
    private String workContent;

    /**
     * 作业图片
     */
    @Excel(name = "作业图片")
    private String workImgUrl;

    /**
     * 作业视频封面图
     */
    @Excel(name = "作业视频封面图")
    private String workVideoImg;

    /**
     * 作业视频
     */
    @Excel(name = "作业视频")
    private String workVideoUrl;

    /**
     * 作业音频
     */
    @Excel(name = "作业音频")
    private String workAudioUrl;

    /**
     * 示例作业图片
     */
    @Excel(name = "示例作业图片")
    private String exampleWorkImgUrl;

    /**
     * 示例作业视频封面图
     */
    @Excel(name = "示例作业视频封面图")
    private String exampleWorkVideoImg;

    /**
     * 示例作业视频
     */
    @Excel(name = "示例作业视频")
    private String exampleWorkVideoUrl;

    /**
     * 示例作业音频
     */
    @Excel(name = "示例作业音频")
    private String exampleWorkAudioUrl;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setClockInQuestId(Long clockInQuestId) {
        this.clockInQuestId = clockInQuestId;
    }

    public Long getClockInQuestId() {
        return clockInQuestId;
    }

    public void setWorkTitle(String workTitle) {
        this.workTitle = workTitle;
    }

    public String getWorkTitle() {
        return workTitle;
    }

    public void setWorkContent(String workContent) {
        this.workContent = workContent;
    }

    public String getWorkContent() {
        return workContent;
    }

    public void setWorkImgUrl(String workImgUrl) {
        this.workImgUrl = workImgUrl;
    }

    public String getWorkImgUrl() {
        return workImgUrl;
    }

    public void setWorkVideoUrl(String workVideoUrl) {
        this.workVideoUrl = workVideoUrl;
    }

    public String getWorkVideoUrl() {
        return workVideoUrl;
    }

    public void setWorkAudioUrl(String workAudioUrl) {
        this.workAudioUrl = workAudioUrl;
    }

    public String getWorkAudioUrl() {
        return workAudioUrl;
    }

    public void setExampleWorkImgUrl(String exampleWorkImgUrl) {
        this.exampleWorkImgUrl = exampleWorkImgUrl;
    }

    public String getExampleWorkImgUrl() {
        return exampleWorkImgUrl;
    }

    public void setExampleWorkVideoUrl(String exampleWorkVideoUrl) {
        this.exampleWorkVideoUrl = exampleWorkVideoUrl;
    }

    public String getExampleWorkVideoUrl() {
        return exampleWorkVideoUrl;
    }

    public void setExampleWorkAudioUrl(String exampleWorkAudioUrl) {
        this.exampleWorkAudioUrl = exampleWorkAudioUrl;
    }

    public String getExampleWorkAudioUrl() {
        return exampleWorkAudioUrl;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("clockInQuestId", getClockInQuestId())
                .append("workTitle", getWorkTitle())
                .append("workContent", getWorkContent())
                .append("workImgUrl", getWorkImgUrl())
                .append("workVideoUrl", getWorkVideoUrl())
                .append("workAudioUrl", getWorkAudioUrl())
                .append("exampleWorkImgUrl", getExampleWorkImgUrl())
                .append("exampleWorkVideoUrl", getExampleWorkVideoUrl())
                .append("exampleWorkAudioUrl", getExampleWorkAudioUrl())
                .append("beginTime", getBeginTime())
                .append("endTime", getEndTime())
                .append("isDelete", getIsDelete())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
