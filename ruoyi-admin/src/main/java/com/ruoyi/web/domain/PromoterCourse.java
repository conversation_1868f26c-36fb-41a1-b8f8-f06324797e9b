package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 推广员课程对象 promoter_course
 * 
 * <AUTHOR>
 * @date 2023-08-11
 */
public class PromoterCourse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 教师id */
    @Excel(name = "教师id")
    private Long teacherId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseTitle;

    /** 推广比例必须<100 */
    @Excel(name = "推广比例必须<100")
    private Long spreadRate;

    /** 发布平台，0抖音 1微信 2快手 3视频号 */
    @Excel(name = "发布平台，0抖音 1微信 2快手 3视频号")
    private String publishPlatform;

    /** 课程类型，0单课，1专栏 */
    @Excel(name = "课程类型，0单课，1专栏")
    private Integer courseType;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal coursePrice;

    /** 课程状态 */
    @Excel(name = "课程状态")
    private Integer courseStatus;

    public Integer getCourseType() {
        return courseType;
    }

    public void setCourseType(Integer courseType) {
        this.courseType = courseType;
    }

    public BigDecimal getCoursePrice() {
        return coursePrice;
    }

    public void setCoursePrice(BigDecimal coursePrice) {
        this.coursePrice = coursePrice;
    }

    public Integer getCourseStatus() {
        return courseStatus;
    }

    public void setCourseStatus(Integer courseStatus) {
        this.courseStatus = courseStatus;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPromoterId(Long promoterId) 
    {
        this.promoterId = promoterId;
    }

    public Long getPromoterId() 
    {
        return promoterId;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setCourseTitle(String courseTitle) 
    {
        this.courseTitle = courseTitle;
    }

    public String getCourseTitle() 
    {
        return courseTitle;
    }
    public void setSpreadRate(Long spreadRate) 
    {
        this.spreadRate = spreadRate;
    }

    public Long getSpreadRate() 
    {
        return spreadRate;
    }
    public void setPublishPlatform(String publishPlatform) 
    {
        this.publishPlatform = publishPlatform;
    }

    public String getPublishPlatform() 
    {
        return publishPlatform;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("promoterId", getPromoterId())
            .append("courseId", getCourseId())
            .append("courseTitle", getCourseTitle())
            .append("spreadRate", getSpreadRate())
            .append("publishPlatform", getPublishPlatform())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
