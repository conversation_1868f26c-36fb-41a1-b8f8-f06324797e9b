package com.ruoyi.web.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 评论回复对象 review_reply
 * 
 * <AUTHOR>
 * @date 2023-08-21
 */
@Data
public class ReviewReply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 课程评论id */
    @Excel(name = "课程评论id")
    private Long reviewCourseId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String userName;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 老师名称 */
    @Excel(name = "老师名称")
    private String teacherName;

    /** 回复内容 */
    @Excel(name = "回复内容")
    private String reviewContent;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setReviewCourseId(Long reviewCourseId) 
    {
        this.reviewCourseId = reviewCourseId;
    }

    public Long getReviewCourseId() 
    {
        return reviewCourseId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setTeacherName(String teacherName) 
    {
        this.teacherName = teacherName;
    }

    public String getTeacherName() 
    {
        return teacherName;
    }
    public void setReviewContent(String reviewContent) 
    {
        this.reviewContent = reviewContent;
    }

    public String getReviewContent() 
    {
        return reviewContent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("reviewCourseId", getReviewCourseId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("teacherId", getTeacherId())
            .append("teacherName", getTeacherName())
            .append("reviewContent", getReviewContent())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
