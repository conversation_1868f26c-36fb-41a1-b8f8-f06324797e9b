package com.ruoyi.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退款审核对象 order_refund_review
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public class OrderRefundReview extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键Id */
    private Long id;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderId;

    /** 店铺名称 */
    @Excel(name = "店铺名称")
    private String shopName;

    /** 店铺Id(老师Id) */
    @Excel(name = "店铺Id(老师Id)")
    private Long shopId;

    /** 店铺账号 */
    @Excel(name = "店铺账号")
    private String shopAccount;

    /** 购买人id */
    @Excel(name = "购买人id")
    private Long buyerUserId;

    /** 购买人头像 */
    @Excel(name = "购买人头像")
    private String buyerUserImg;

    /** 购买人姓名 */
    @Excel(name = "购买人姓名")
    private String buyerUserName;

    /** 购买人手机号 */
    @Excel(name = "购买人手机号")
    private String buyerUserMobile;

    /** 支付方式  支付宝支付 微信支付 银行卡支付 抖音支付 */
    @Excel(name = "支付方式  支付宝支付 微信支付 银行卡支付 抖音支付")
    private String payWay;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payPrice;

    /** 申请金额 */
    @Excel(name = "申请金额")
    private BigDecimal appliedAmount;

    /** 提交人 */
    @Excel(name = "提交人")
    private String submitter;

    /** 处理状态 0待处理,1,通过,2驳回 */
    @Excel(name = "处理状态 0待处理,1,通过,2驳回")
    private Integer handlingStatus;

    /** 来源的APP,1问到好课,2问到课堂 */
    @Excel(name = "来源的APP,1问到好课,2问到课堂")
    private Integer appNameType;

    /** 订单来源 0抖音 1微信 2快速 3视频号 */
    @Excel(name = "订单来源 0抖音 1微信 2快速 3视频号")
    private Integer orderPlatform;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 搜索的店铺信息
     */
    private String shopInfo;

    private String processor;

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public String getShopInfo() {
        return shopInfo;
    }

    public void setShopInfo(String shopInfo) {
        this.shopInfo = shopInfo;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setShopName(String shopName) 
    {
        this.shopName = shopName;
    }

    public String getShopName() 
    {
        return shopName;
    }
    public void setShopId(Long shopId) 
    {
        this.shopId = shopId;
    }

    public Long getShopId() 
    {
        return shopId;
    }
    public void setShopAccount(String shopAccount) 
    {
        this.shopAccount = shopAccount;
    }

    public String getShopAccount() 
    {
        return shopAccount;
    }
    public void setBuyerUserId(Long buyerUserId) 
    {
        this.buyerUserId = buyerUserId;
    }

    public Long getBuyerUserId() 
    {
        return buyerUserId;
    }
    public void setBuyerUserImg(String buyerUserImg) 
    {
        this.buyerUserImg = buyerUserImg;
    }

    public String getBuyerUserImg() 
    {
        return buyerUserImg;
    }
    public void setBuyerUserName(String buyerUserName) 
    {
        this.buyerUserName = buyerUserName;
    }

    public String getBuyerUserName() 
    {
        return buyerUserName;
    }
    public void setBuyerUserMobile(String buyerUserMobile) 
    {
        this.buyerUserMobile = buyerUserMobile;
    }

    public String getBuyerUserMobile() 
    {
        return buyerUserMobile;
    }
    public void setPayWay(String payWay) 
    {
        this.payWay = payWay;
    }

    public String getPayWay() 
    {
        return payWay;
    }
    public void setPayTime(Date payTime) 
    {
        this.payTime = payTime;
    }

    public Date getPayTime() 
    {
        return payTime;
    }
    public void setPayPrice(BigDecimal payPrice) 
    {
        this.payPrice = payPrice;
    }

    public BigDecimal getPayPrice() 
    {
        return payPrice;
    }
    public void setAppliedAmount(BigDecimal appliedAmount) 
    {
        this.appliedAmount = appliedAmount;
    }

    public BigDecimal getAppliedAmount() 
    {
        return appliedAmount;
    }
    public void setSubmitter(String submitter) 
    {
        this.submitter = submitter;
    }

    public String getSubmitter() 
    {
        return submitter;
    }
    public void setHandlingStatus(Integer handlingStatus) 
    {
        this.handlingStatus = handlingStatus;
    }

    public Integer getHandlingStatus() 
    {
        return handlingStatus;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }
    public void setOrderPlatform(Integer orderPlatform) 
    {
        this.orderPlatform = orderPlatform;
    }

    public Integer getOrderPlatform() 
    {
        return orderPlatform;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("shopName", getShopName())
            .append("shopId", getShopId())
            .append("shopAccount", getShopAccount())
            .append("buyerUserId", getBuyerUserId())
            .append("buyerUserImg", getBuyerUserImg())
            .append("buyerUserName", getBuyerUserName())
            .append("buyerUserMobile", getBuyerUserMobile())
            .append("payWay", getPayWay())
            .append("payTime", getPayTime())
            .append("payPrice", getPayPrice())
            .append("appliedAmount", getAppliedAmount())
            .append("submitter", getSubmitter())
            .append("handlingStatus", getHandlingStatus())
            .append("appNameType", getAppNameType())
            .append("orderPlatform", getOrderPlatform())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
