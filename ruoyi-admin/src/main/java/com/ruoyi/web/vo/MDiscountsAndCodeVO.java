package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class MDiscountsAndCodeVO {

    private Integer pageNum;

    private Integer pageSize;

    /** 优惠券id */
    @Excel(name = "优惠券id")
    private Long discountsId;

    /** 使用状态，0 未使用 ，1 已使用 ， 2已作废，3未领取 */
    @Excel(name = "使用状态，0 未使用 ，1 已使用 ， 2已作废，3未领取")
    private Integer useType;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String nickName;

    /** 优惠码 */
    @Excel(name = "优惠码")
    private String discountsCode;

    /** 操作员id */
    private Long operatorId;

    /** 操作员名称 */
    private String operatorName;

    /** 操作员手机号 */
    private String operatorPhone;

    /** 优惠券开始领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveCouponStartTime;

    /** 优惠券结束领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveCouponEndTime;


}
