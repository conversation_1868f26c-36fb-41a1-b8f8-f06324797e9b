package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class PromoterAndReceiveVO {

    private Integer pageNum;

    private Integer pageSize;

    /** 推广人id */
    private Long promoterId;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseTitle;

    /** 姓名 */
    @Excel(name = "姓名/手机号")
    private String nameOrPhone;

    /** 平台（多个，以逗号分隔）0快手，1抖音，2微信，3视频号 */
    @Excel(name = "平台", readConverterExp = "多=个，以逗号分隔")
    private String orderPlatform;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;





}
