package com.ruoyi.web.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

@Data
public class MDiscountsAndStatusVO {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 优惠券类型
     */
    @Excel(name = "优惠券类型, 0普通优惠券，1分享优惠券 ")
    private Integer discountsType;


    /**
     * 优惠券状态
     */
    @Excel(name = "优惠券状态，0 未开始，1进行中，2已结束 ")
    private Integer discountsStatus;

    /**
     * 优惠券名称
     */
    @Excel(name = "优惠券名称")
    private String discountsName;

}
