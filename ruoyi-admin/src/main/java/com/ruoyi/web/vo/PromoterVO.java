package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PromoterVO implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 推广员姓名 手机号 检索*/
    @Excel(name = "推广员姓名 手机号 检索")
    private String promoterKeywords;

    /** 是否推广这个视频 0 否 1是 */
    @Excel(name = "是否推广这个视频 0 否 1是")
    private Integer promotionStatus;

    /** 推广员姓名 */
    @Excel(name = "推广员姓名")
    private String promoterName;

    /** 平台 1：抖音，2：快手，3：微信 4: 视频号 */
    @Excel(name = "平台", readConverterExp = "1：抖音，2：快手，3：微信 4: 视频号")
    private String platform;

    /** 账号状态，0禁用，1启用 */
    @Excel(name = "账号状态，0禁用，1启用")
    private Integer accountState;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
