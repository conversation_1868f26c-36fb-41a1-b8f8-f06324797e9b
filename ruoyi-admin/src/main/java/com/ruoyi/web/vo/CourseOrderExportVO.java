package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 课程订单导出vo
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Data
public class CourseOrderExportVO {

    /** 主键id */
    private Long id;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 购买人id */
    @Excel(name = "购买人id")
    private Long buyerUserId;

    /** 购买人姓名 */
    @Excel(name = "购买人姓名")
    private String buyerUserName;

    /** 购买人头像 */
    @Excel(name = "购买人头像")
    private String buyerUserImg;

    /** 购买人手机号 */
    @Excel(name = "购买人手机号")
    private String buyerUserMobile;

    /** 课程总时长 ， 单位是秒 */
    @Excel(name = "课程总时长 ， 单位是秒")
    private Long courseDuration;

    /** 课程图片 */
    @Excel(name = "课程图片")
    private String courseImgUrl;

    /** 学习时长 ， 单位是秒 */
    @Excel(name = "学习时长 ， 单位是秒")
    private Long studyDuration;

    /** 有效期 */
    @Excel(name = "有效期")
    private Long validity;

    /** 推广比率 */
    @Excel(name = "推广比率")
    private String promotionRatio;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 是否是推广订单 0不是  1是 */
    @Excel(name = "是否是推广订单 0不是  1是")
    private Integer isPromoter;

    /** 推广员名称 */
    @Excel(name = "推广员名称")
    private String promoterName;

    /** 推广员电话 */
    @Excel(name = "推广员电话")
    private String promoterMobile;

    /** 我的收益 */
    @Excel(name = "我的收益")
    private BigDecimal myEarningsPrice;

    /** 推广员收益 */
    @Excel(name = "推广员收益")
    private BigDecimal promoterEarningsPrice;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String courseTitle;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal coursePrice;

    /** 是否赠送课程 0否 1是 */
    @Excel(name = "是否赠送课程 0否 1是")
    private Integer isCourse;

    /** 课程划线价格 */
    @Excel(name = "课程划线价格")
    private BigDecimal originalPrice;

    /** 订单状态 0 待支付 1已支付 2已退款 3已关闭 */
    @Excel(name = "订单状态 0 待支付 1已支付 2已退款 3已关闭")
    private Integer orderStatus;

    /** 订单类型 0课程订单 1推广订单 */
    @Excel(name = "订单类型 0课程订单 1推广订单")
    private Integer orderType;

    /** 订单来源 0抖音 1微信 2快速 3视频号 */
    @Excel(name = "订单来源 0抖音 1微信 2快速 3视频号")
    private Integer orderPlatform;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 资金类型 0在途 1已入账 */
    @Excel(name = "资金类型 0在途 1已入账")
    private Integer fundsType;

    /** 支付方式  支付宝支付 微信支付 银行卡支付 */
    @Excel(name = "支付方式  支付宝支付 微信支付 银行卡支付")
    private String payWay;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 平台订单号 */
    @Excel(name = "平台订单号")
    private String outOrderNumber;

    /** 渠道支付单号 */
    @Excel(name = "渠道支付单号")
    private String tradingOrderNumber;

    /** 物流单号 */
    @Excel(name = "物流单号")
    private String deliverGoodsOrderId;

    /**
     * 支付金额
     */
    @Excel(name = "支付金额")
    private BigDecimal payPrice;

    /**
     * 删除状态 0否 1是
     */
    @Excel(name = "删除状态 0否 1是")
    private Integer isDelete;


    /** 操作员id */
    private Long operatorId;

    /** 操作员名称 */
    private String operatorName;


    /** 操作员手机号 */
    private String operatorPhone;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 自己后台调用类型   1自己调用
     */
    private Integer type;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺名称")
    private String shopName;

    /**
     * 店铺账号/手机号
     */
    @Excel(name = "店铺账号")
    private String mobile;

    /**
     * 抖店店铺id
     */
    private Long ddShopId;
    /**
     * 短信状态 1、未回执，2、发送失败，3、发送成功
     */
    private Integer ddSmsStatus;
    /**
     * 核销状态 0未核销，1已核销
     */
    private Integer ddHexiaoStatus;
}
