package com.ruoyi.web.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserManagementVO {


    private Integer pageNum;

    private Integer pageSize;

    private String shopInfomation;

    private String appType;

    /**
     * 排序字段  userNum 用户数量  authorizationNum 授权数量  consumptionNum  消费次数
     * consumptioTotal  消费总额 refundNum   退款次数
     */
    private String orderByColumn;

    /**
     * 排序方法    ascending升序   descending 降序
     */
    private String isAsc;
}
