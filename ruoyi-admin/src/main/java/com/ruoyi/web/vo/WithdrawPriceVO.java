package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 提现金额对象 withdraw_price
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
public class WithdrawPriceVO implements Serializable {
    private Integer pageNum;

    private Integer pageSize;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 搜索值 */
    @Excel(name = "搜索值")
    private String searchValue;

    /** 个人-讲师姓名 */
    @Excel(name = "个人-讲师姓名")
    private String teacherName;

    /** 类目选择一级id */
    @Excel(name = "类目选择一级id")
    private Long firstClassId;

    /** 类目选择一级pid */
    @Excel(name = "类目选择一级pid")
    private Long firstClassPid;

    /** 类目选择一级名称 */
    @Excel(name = "类目选择一级名称")
    private String firstClassTitle;

    /** 类目选择一级抖音类目id */
    @Excel(name = "类目选择一级抖音类目id")
    private Long firstClassDouyinClassId;

    /** 类目选择二级id */
    @Excel(name = "类目选择二级id")
    private Long secondClassId;

    /** 类目选择二级pid */
    @Excel(name = "类目选择二级pid")
    private Long secondClassPid;

    /** 类目选择二级名称 */
    @Excel(name = "类目选择二级名称")
    private String secondClassTitle;

    /** 类目选择二级抖音类目id */
    @Excel(name = "类目选择二级抖音类目id")
    private Long secondClassDouyinClassId;

    /** 入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开 */
    @Excel(name = "入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开")
    private String platform;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
