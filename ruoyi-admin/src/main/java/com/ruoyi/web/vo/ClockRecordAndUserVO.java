package com.ruoyi.web.vo;


import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;


@Data
public class ClockRecordAndUserVO implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 任务id
     */
    @Excel(name = "任务id")
    private Long clockInQuestId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;
}
