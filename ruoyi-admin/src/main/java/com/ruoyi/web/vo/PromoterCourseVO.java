package com.ruoyi.web.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.web.domain.PromoterCourse;
import lombok.Data;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class PromoterCourseVO {


    @Excel(name = "id")
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 推广员姓名 */
    @Excel(name = "推广员姓名")
    private String promoterName;

    /** 推广员手机号码 */
    @Excel(name = "推广员手机号码")
    private String promoterPhone;


    private List<PromoterCourse> promoterCoursesList;


    /**
     * 授权账号
     */
    @Excel(name = "授权账号")
    private List<PromoterCourseAndAccountVO> authorizedAccountList;



    private Integer appNameType=1;

}
