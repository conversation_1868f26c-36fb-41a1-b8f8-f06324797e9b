package com.ruoyi.web.vo;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class MLiveStreamVO {

    private Integer pageNum;

    private Integer pageSize;

    //所属平台，入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开
    private String platform;
    /**
     * 店铺信息
     */
    private String shopInfomation;

    /**
     * 直播合计页排序   0  从大到小（降序）   1  从小到大（升序）
     */
    private Integer liveStreamCountSort;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 锚点信息 */
    @Excel(name = "锚点信息")
    private String anchorPoint;





}
