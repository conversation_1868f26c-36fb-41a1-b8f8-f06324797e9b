package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class MDiscountsStopActionVO {
    /** 主键id */
    private Long discountsId;

    private Long teacherId;

    /** 是否允许继续使用，0不允许，1允许 */
    @Excel(name = "是否允许继续使用，0不允许，1允许")
    private Integer isKeepUsing;

    /** 是否停止活动，0不停止，1停止 */
    @Excel(name = "是否停止活动，0不停止，1停止")
    private Integer isStopAction;

    /** 优惠券状态，0 未开始，1进行中，2已结束 */
    @Excel(name = "优惠券状态，0 未开始，1进行中，2已结束")
    private Integer discountsStatus;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;





}
