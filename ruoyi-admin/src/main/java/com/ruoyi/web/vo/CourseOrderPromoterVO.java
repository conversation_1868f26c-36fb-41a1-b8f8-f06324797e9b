package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 订单返回参数
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Data
public class CourseOrderPromoterVO {

    private Integer pageNum;

    private Integer pageSize;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 购买人姓名  购买人名称  购买人手机号 检索 */
    @Excel(name = "购买人姓名  购买人名称  购买人手机号 检索")
    private String buyerKeywords;

    /** 推广员姓名 手机号检索 */
    @Excel(name = "推广员姓名 手机号检索")
    private String promoterKeywords;

    /** 来源平台 0抖音 1微信 2快速 3视频号*/
    @Excel(name = "来源平台 0抖音 1微信 2快速 3视频号")
    private Integer platform;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
