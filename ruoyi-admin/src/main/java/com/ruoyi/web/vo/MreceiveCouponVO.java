package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class MreceiveCouponVO {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 优惠券id
     */
    @Excel(name = "优惠券id")
    private Long discountsId;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String nickName;

    /**
     * 使用状态
     */
    @Excel(name = "使用状态，0未使用，1已使用 ")
    private Integer status;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
