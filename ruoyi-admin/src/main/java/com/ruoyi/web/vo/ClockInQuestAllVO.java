package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 打卡任务对象 clock_in_quest
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@Data
public class ClockInQuestAllVO {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 任务类型 0打卡 1作业
     */
    @Excel(name = "任务类型 0打卡 1作业")
    private Integer questType;

    /**
     * 任务状态 0进行中 1已结束 2禁用
     */
    @Excel(name = "任务状态 0进行中 1已结束 2禁用")
    private Integer questStatus;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
