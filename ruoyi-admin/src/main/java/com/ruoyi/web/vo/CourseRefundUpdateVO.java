package com.ruoyi.web.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class CourseRefundUpdateVO {

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 退款状态,0待处理，1已退款，2超时自动退款，3已拒绝退款 */
    @Excel(name = "退款状态,0待处理,1已处理")
    private Integer refundStatus;

    /** 退款类型,1已退款，2超时自动退款，3已拒绝退款 */
    @Excel(name = "退款状态,1已退款，2超时自动退款，3已拒绝退款")
    private Integer refundType;

    /** 拒绝退款描述 */
        @Excel(name = "拒绝退款描述")
    private String refusalOfRefund;

    /** 拒绝退款图片 */
    @Excel(name = "拒绝退款图片")
    private String refusalOfRefundImg;

    /**
     * 修改时间
     */
    @Excel(name = "修改时间")
    private Date updateTime;

    /** 小红书拒绝时选其他，此字段必填 */
    @Excel(name = "小红书拒绝时选其他，此字段必填")
    private String xhsDescription;


}
