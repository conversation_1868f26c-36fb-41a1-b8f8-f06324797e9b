package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreLevelCourseVO {
    /**
     * 店铺所属APP  0问到好课，1问到课堂
     */
    private Integer appType;
    /**
     * 店铺信息
     */
    private String shopInfomation;
    /**
     * 课程名称
     */
    private String title;
    /**
     * 所属平台，入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开
     */
    private String platform;
    /**
     * 审核状态
     */
    private Integer dyAuditStatus;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;
    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private Integer pageSize;
    private Integer pageNum;

    /**
     * 排序字段
     */
    private String orderByColumn;
    /**
     * 排序方式  ascending升序   descending 降序
     */
    private String isAsc;
}
