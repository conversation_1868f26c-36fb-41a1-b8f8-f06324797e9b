package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 物流信息导出 deliver_goods_order
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Data
public class DeliverGoodsOrderExportVO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 收货人手机
     */
    @Excel(name = "收货人手机")
    private Long consigneeMobile;

    /**
     * 收货人名称
     */
    @Excel(name = "收货人名称")
    private String consigneeName;

    /**
     * 收货人地址
     */
    @Excel(name = "收货人地址")
    private String consigneeAddr;

    /**
     * 物流单号
     */
    @Excel(name = "物流单号")
    private String logisticsNumber;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderId;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /**
     * 教材名称
     */
    @Excel(name = "教材名称")
    private String teachingMaterialName;

    /**
     * 教材数量
     */
    @Excel(name = "教材数量")
    private Integer teachingMaterialNum;

    /**
     * 收货信息状态 0未填写 1已填写
     */
    @Excel(name = "收货信息状态 0未填写 1已填写")
    private Integer receivingInformationStatus;

    /**
     * 发货状态 0 未发货 1已发货
     */
    @Excel(name = "发货状态 0 未发货 1已发货")
    private Integer shipmentsStatus;

    /** 操作员id */
    private Long operatorId;

    /** 操作员名称 */
    private String operatorName;

    /** 操作员手机号 */
    private String operatorPhone;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺名称")
    private String shopName;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

}
