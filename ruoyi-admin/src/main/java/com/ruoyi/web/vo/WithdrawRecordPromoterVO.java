package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WithdrawRecordPromoterVO {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 搜索值
     */
//    @Excel(name = "店铺名称")
    private String searchValue;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺名称")
    private String shopName;

    /**
     * 店铺账号/手机号
     */
    @Excel(name = "店铺账号")
    private String mobile;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 提现审核状态,0审核成功，1待审核，2审核驳回，3打款失败
     */
    @Excel(name = "提现审核状态,0审核成功，1待审核，2审核驳回，3打款失败")
    private Integer withdrawAuditStatus;

    /**
     * 推广员id
     */
    @Excel(name = "推广员id")
    private Long promoterId;

    /**
     * 推广员姓名
     */
    @Excel(name = "推广员姓名")
    private String promoterName;

    /**
     * 推广员手机号
     */
    @Excel(name = "推广员手机号")
    private String promoterPhone;

    /**
     * 提现订单号
     */
    @Excel(name = "提现订单号")
    private String withdrawOrderNumber;

    /**
     * 提现人名称
     */
    @Excel(name = "提现人名称")
    private String withdrawName;

    /**
     * 提现人头像
     */
    @Excel(name = "提现人头像")
    private String withdrawNameImg;

    /**
     * 在途金额
     */
    @Excel(name = "在途金额")
    private BigDecimal fundsTransitPrice;

    /**
     * 可提现金额
     */
    @Excel(name = "可提现金额")
    private BigDecimal mayWithdrawPrice;

    /**
     * 提现金额
     */
    @Excel(name = "提现金额")
    private BigDecimal withdrawPrice;

    /**
     * 提现账号
     */
    @Excel(name = "提现账号")
    private String accountPhone;

    /**
     * 到账金额
     */
    @Excel(name = "到账金额")
    private BigDecimal accountPrice;

    /**
     * 服务费比例
     */
    @Excel(name = "服务费比例")
    private Integer servicePriceRatio;

    /**
     * 服务费
     */
    @Excel(name = "服务费")
    private BigDecimal servicePrice;

    /**
     * 收入来源 0抖音 1微信 2快速 3视频号
     */
    @Excel(name = "收入来源 0抖音 1微信 2快速 3视频号")
    private Integer incomePlatform;

    /**
     * 提现账户 0支付宝提现 1公司账户提现
     */
    @Excel(name = "提现账户 0支付宝提现 1公司账户提现")
    private Integer accountType;

    /**
     * 申请提现时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请提现时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawApplyTime;

    /**
     * 提现到账时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提现到账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawAccountTime;

    /**
     * 财务处理时间（取通过，驳回时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "财务处理时间（取通过，驳回时间）", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processingTime;

    /**
     * 第三方处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "第三方处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tripartiteProcessingTime;

    /**
     * 打款流水号
     */
    @Excel(name = "打款流水号")
    private String paymentSequenceNumber;

    /**
     * 支付宝转账订单号
     */
    @Excel(name = "支付宝转账订单号")
    private String zfbOrderId;

    /**
     * 提现状态 0提现成功 1提现中 2提现失败 3提现审核中
     */
    @Excel(name = "提现状态 0提现成功 1提现中 2提现失败 3提现审核中")
    private Integer withdrawStatus;


    /**
     * 实体类型 1: 个人 2:机构 3:公共资质
     */
    @Excel(name = "实体类型 1: 个人 2:机构 3:公共资质")
    private Integer entityType;

    /**
     * 备注信息
     */
    @Excel(name = "备注信息")
    private String remarkMessage;

    /**
     * 凭证
     */
    @Excel(name = "凭证")
    private String evidence;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /**
     * 操作员id
     */
    private Long operatorId;

    /**
     * 操作员名称
     */
    private String operatorName;

    /**
     * 操作员手机号
     */
    private String operatorPhone;

    private Integer appNameType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processBeginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processEndTime;
}
