package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 老师流水记录对象 teacher_flow_record
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
@Data
public class TeacherFlowRecordVO implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 0抖音 1微信 2快手 3视频号*/
    @Excel(name = "0抖音 1微信 2快手 3视频号")
    private String platform;

    /** 提现订单号 */
    @Excel(name = "提现订单号")
    private String withdrawOrderNumber;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 自己后台调用类型   1自己调用
     */
    private Integer type;
}
