package com.ruoyi.web.vo;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class MLiveStreamAndUpdateVO {

    /**
     * 主键id
     */
    private Long id;


    /**
     * 教师id
     */
    @Excel(name = "教师id")
    private Long teacherId;

    /**
     * 锚点信息
     */
    @Excel(name = "锚点信息")
    private String anchorPoint;

    /**
     * 主题id
     */
    @Excel(name = "主题id")
    private Long themeId;


    /**
     * 课程id数组
     */
    private List<MLiveStreamAndCourseIdSerialVO> courseIdList;







}
