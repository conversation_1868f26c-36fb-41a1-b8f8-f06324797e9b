package com.ruoyi.web.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 提现金额对象 withdraw_price
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
public class WithdrawPriceAndFrozenAccountVO implements Serializable {

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 推广员id
     */
    @Excel(name = "推广员id")
    private Long promoterId;


    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 冻结状态  0 正常  1冻结 */
    @Excel(name = "冻结状态 0 正常  1冻结")
    private String freezeStatus;

    /** 0抖音 1微信 2快手 3视频号*/
    @Excel(name = "0抖音 1微信 2快手 3视频号")
    private Integer platform;

}
