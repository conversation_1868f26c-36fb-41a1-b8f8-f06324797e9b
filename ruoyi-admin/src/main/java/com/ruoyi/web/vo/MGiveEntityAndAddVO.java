package com.ruoyi.web.vo;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class MGiveEntityAndAddVO {

    private Long id;

    /** 教师id */
    @Excel(name = "教师id")
    private Long teacherId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 获取类型 0 手机号 1地址 */
    @Excel(name = "获取类型 0 手机号 1地址")
    private Integer type;

    /** 附赠实物 */
    @Excel(name = "附赠实物")
    private List<MGiveEntityAndTextbookVO> textBookList;


}
