package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class CourseRefundVO {

    private Integer pageNum;

    private Integer pageSize;

    private Integer refundPlatform;

    private Integer appNameType;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;


    /**
     * 渠道支付单号
     */
    @Excel(name = "渠道支付单号")
    private String tradingOrderNumber;


    /** 退款订单编号 */
    @Excel(name = "退款订单编号")
    private String refundId;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 购买人手机号 */
    @Excel(name = "购买人手机号")
    private String buyerUserMobile;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String courseTitle;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 退款状态,0待处理 */
    @Excel(name = "退款状态,0待处理,1已处理")
    private Integer refundStatus;

    /** 退款类型,1已退款，2超时自动退款，3已拒绝退款 */
    @Excel(name = "退款状态,1已退款，2超时自动退款，3已拒绝退款")
    private Integer refundType;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺账号/手机号
     */
    private String mobile;

    /**
     * 自己后台调用类型   1自己调用
     */
    private Integer type;

}
