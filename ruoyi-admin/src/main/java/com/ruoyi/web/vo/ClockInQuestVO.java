package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.web.domain.ClockWork;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 打卡任务对象 clock_in_quest
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@Data
public class ClockInQuestVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 课程名称
     */
    @Excel(name = "课程名称")
    private String courseName;

    /**
     * 打卡标题
     */
    @Excel(name = "打卡标题")
    private String clockTitle;

    /**
     * 参与人数
     */
    @Excel(name = "参与人数")
    private Long participateNumber;

    /**
     * 虚拟人数
     */
    @Excel(name = "虚拟人数")
    private Long virtualNumber;

    /**
     * 虚拟头像列表
     */
    @Excel(name = "虚拟头像列表")
    private String virtualImg;

    /**
     * 任务类型 0打卡 1作业
     */
    @Excel(name = "任务类型 0打卡 1作业")
    private Integer questType;

    /**
     * 任务状态 0进行中 1已结束 2禁用
     */
    @Excel(name = "任务状态 0进行中 1已结束 2禁用")
    private Integer questStatus;

    /**
     * 任务周期    天
     */
    @Excel(name = "任务周期    天")
    private Integer questPeriod;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 设置打卡作业
     */
    private List<ClockWork> clockWorkList;
}
