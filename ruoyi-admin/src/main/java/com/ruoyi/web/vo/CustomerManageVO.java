package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class CustomerManageVO {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 用户来源 0抖音 1微信 2快速 3视频号
     */
    @Excel(name = "用户来源 0抖音 1微信 2快速 3视频号")
    private Integer customerPlatform;

    /**
     * 用户手机号
     */
    @Excel(name = "用户手机号")
    private String customerMobile;

    /** 黑名单 0否  1是 */
    @Excel(name = "黑名单 0否  1是")
    private Integer blacklistStatus;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;


    @Excel(name = "购买人姓名  购买人名称  购买人手机号 检索")
    private String customerName;

    @Excel(name = "是否授权 0 未授权   1  已授权")
    private Integer isAuthorization;

}
