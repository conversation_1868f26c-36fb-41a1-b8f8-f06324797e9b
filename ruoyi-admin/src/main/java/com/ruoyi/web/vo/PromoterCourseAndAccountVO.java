package com.ruoyi.web.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class PromoterCourseAndAccountVO {

    /**
     * 账号
     */
    @Excel(name = "账号")
    private Long account;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String avatarUrl;

    /**
     * 来源平台1：抖音，2：快手，3：微信
     */
    @Excel(name = "来源平台1：抖音，2：快手，3：微信")
    private Integer platform;

    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称")
    private String nickName;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 判断是否是哪个平台
     */
    private boolean flag;


    private boolean flagState;


}
