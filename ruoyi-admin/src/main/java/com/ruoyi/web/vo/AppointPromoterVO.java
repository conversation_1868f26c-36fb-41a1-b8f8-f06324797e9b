package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class AppointPromoterVO {

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 推广员id */
    private Long promoterId;

    /** 推广比例必须<100 */
    @Excel(name = "推广比例必须<100")
    private Integer spreadRate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;



}
