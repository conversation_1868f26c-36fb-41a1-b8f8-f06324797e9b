package com.ruoyi.web.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DoudianShopConfigVO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 店铺id
     */
    @Excel(name = "店铺id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺名称")
    private String shopName;

    /**
     * 应用appKey
     */
    @Excel(name = "应用appKey")
    private String appKey;

    /**
     * 应用appSecret
     */
    @Excel(name = "应用appSecret")
    private String appSecret;

    /**
     * 店铺分类控制,默认0为不限制，多个以逗号分隔
     */
    @Excel(name = "店铺分类控制,默认0为不限制，多个以逗号分隔")
    private String firstCatIdLimit;

    /**
     * 商品主图预设值图
     */
    @Excel(name = "商品主图预设值图")
    private String productMainImgPlaceholder;

    /**
     * 商品详情预设图
     */
    @Excel(name = "商品详情预设图")
    private String productDetailImgPlaceholder;

    /**
     * 是否公共店铺，1是，0否，默认1
     */
    @Excel(name = "是否公共店铺，1是，0否，默认1")
    private Integer isPublic;

    /**
     * 问到老师id，如果是公共店铺则为0，非公共则为老师id
     */
    @Excel(name = "问到老师id，如果是公共店铺则为0，非公共则为老师id")
    private Long teacherId;

    /**
     * 关联问到店铺
     */
    @Excel(name = "关联问到店铺")
    private String wendaoShopName;

    /**
     * 领取课程预存金额预警阈值，小于此值会发送给老师短信提醒充值
     */
    @Excel(name = "领取课程预存金额预警阈值，小于此值会发送给老师短信提醒充值")
    private BigDecimal preMoneyEarlyWarning;

    /**
     * 是否支持第三方卡券，0不支持，1支持，根据实际情况设置，默认0
     */
    @Excel(name = "是否支持第三方卡券，0不支持，1支持，根据实际情况设置，默认0")
    private Integer isSupportThirdCoupon;

    /**
     * 是否开启了第三方那个卡券，0不开腔，1开启，根据需要及是否支持开启，默认0
     */
    @Excel(name = "是否开启了第三方那个卡券，0不开腔，1开启，根据需要及是否支持开启，默认0")
    private Integer openThirdCoupon;
}
