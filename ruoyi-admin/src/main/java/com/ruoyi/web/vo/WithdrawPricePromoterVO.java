package com.ruoyi.web.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 提现金额对象 withdraw_price
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
public class WithdrawPricePromoterVO implements Serializable {
    private Integer pageNum;

    private Integer pageSize;

    /**
     * 推广员id
     */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 推广员名称 */
    @Excel(name = "推广员名称")
    private String promoterName;

    /**
     * 推广员电话
     */
    @Excel(name = "推广员电话")
    private String promoterPhone;

    /** 平台，0抖音 1微信 2快手 3视频号 */
    @Excel(name = "平台，0抖音 1微信 2快手 3视频号")
    private String platform;

}
