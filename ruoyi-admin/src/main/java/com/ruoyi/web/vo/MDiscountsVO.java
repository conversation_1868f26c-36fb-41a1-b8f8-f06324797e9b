package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class MDiscountsVO {
    /** 主键id */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;


    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    private String discountsName;

    /** 优惠券类型, 0普通优惠券，1分享优惠券 */
    @Excel(name = "优惠券类型, 0普通优惠券，1分享优惠券")
    private Integer discountsType;

    /** 优惠券价格 */
    @Excel(name = "优惠券价格")
    private BigDecimal discountsMoney;

    /** 优惠券使用开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券使用开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date discountsStartTime;

    /** 优惠券使用结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券使用结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date discountsEndTime;

    /** 优惠券总数 */
    @Excel(name = "优惠券总数")
    private Integer discountsSum;

    /** 优惠券是否无限，0 不是 ， 1 是 */
    @Excel(name = "优惠券是否无限，0 不是 ， 1 是")
    private Integer discountsNumberType;

    /** 优惠券状态，0 未开始，1进行中，2已结束 */
    @Excel(name = "优惠券状态，0 未开始，1进行中，2已结束")
    private Integer discountsStatus;

    /** 领取优惠券总数 */
    @Excel(name = "领取优惠券总数")
    private Integer receiveCouponSum;

    /** 优惠券开始领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券开始领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveCouponStartTime;

    /** 优惠券结束领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券结束领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveCouponEndTime;

    /** 优惠券状态，0 未开始，1进行中，2已结束 */
    @Excel(name = "限制时间状态；0限制时间范围；1限制有效时间")
    private Integer timeStatus;

    @Excel(name = "课程id")
    private Long courseId;

    @Excel(name = "限领次数")
    private Integer receiveMax;

    /** 是否允许继续使用，0不允许，1允许 */
    @Excel(name = "是否允许继续使用，0不允许，1允许")
    private Integer isKeepUsing;

    /** 是否停止活动，0不停止，1停止 */
    @Excel(name = "是否停止活动，0不停止，1停止")
    private Integer isStopAction;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Excel(name = "限制有效时间")
    private Integer validTime;

    //课程id和课程价格集合
    private List<MpriceAndCourseIdVO>  priceAndCourseIdList;



}
