package com.ruoyi.web.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 问到投诉退款管理对象 wendao_complaint_refund
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WendaoComplaintRefundVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Integer pageSize;
    private Integer pageNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;


    private Integer courseDuration;
    private Integer studyDuration;

    private String tradingOrderNumber;

    private String mobile;



    /** 主键id */
    private Integer complaintId;

    /** 归属老师id（店铺id） */
    @Excel(name = "归属老师id", readConverterExp = "店=铺id")
    private Integer teacherId;

    /** 关联订单号，订单表order_id */
    @Excel(name = "关联订单号，订单表order_id")
    private String orderId;

    /** 相关课程id */
    @Excel(name = "相关课程id")
    private Integer courseId;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String title;

    /** 投诉退款编号 */
    @Excel(name = "投诉退款编号")
    private String complaintRefundNumber;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal price;

    /** 课程划线价 */
    @Excel(name = "课程划线价")
    private BigDecimal originalPrice;

    /** 购买人手机号码 */
    @Excel(name = "购买人手机号码")
    private String buyerPhoneNumber;

    /** 投诉问题 */
    @Excel(name = "投诉问题")
    private String question;

    /** 购买人昵称 */
    @Excel(name = "购买人昵称")
    private String buyerNickName;

    /** 0、抖音，1、微信，2、快手，，3、视频号 */
    @Excel(name = "0、抖音，1、微信，2、快手，，3、视频号")
    private Integer source;

    /** 投诉发起时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "投诉发起时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date complaintTime;

    /** 投诉退款单状态,0待退款，1退款成功，2退款失败，3打款失败 */
    @Excel(name = "投诉退款单状态,0待退款，1退款成功，2退款失败，3打款失败")
    private Integer complaintStatus;

    /** 投诉凭证图片 */
    @Excel(name = "投诉凭证图片")
    private String complaintImgs;

    /** 补充描述与凭证 */
    @Excel(name = "补充描述与凭证")
    private String complaintDesc;

    /** 投诉单号 */
    @Excel(name = "投诉单号")
    private String complaintNumber;

    /** 1、线上退款，2、线下退款，3线下沟通解决 */
    @Excel(name = "1、线上退款，2、线下退款，3线下沟通解决")
    private String handlingMethod;

    /** 处理描述 */
    @Excel(name = "处理描述")
    private String handlingDesc;

    /** 补充凭证 */
    @Excel(name = "补充凭证")
    private String handlingImgs;

    /** 课程主图 */
    @Excel(name = "课程主图")
    private String courseCoverUrl;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundMoney;

    /** 关联订单实付金额 */
    @Excel(name = "关联订单实付金额")
    private BigDecimal orderRealPayMoney;

    /** 投诉用户id   （购买用户id） */
    @Excel(name = "投诉用户id   ", readConverterExp = "购=买用户id")
    private Long userId;

    /** 应用对应的用户openid */
    @Excel(name = "应用对应的用户openid")
    private String openid;

    /** 对应的appId */
    @Excel(name = "对应的appId")
    private String appId;

    /** 投诉类型1课程内容问题，2 订单问题，3售后服务，4课程使用方式，5其他，6视频加载异常，7退款问题 */
    @Excel(name = "投诉类型1课程内容问题，2 订单问题，3售后服务，4课程使用方式，5其他，6视频加载异常，7退款问题")
    private Integer complaintType;

    /** 投诉类型描述内容 */
    @Excel(name = "投诉类型描述内容")
    private String complaintTypeContent;

    /** 支付宝姓名 */
    @Excel(name = "支付宝姓名")
    private String alipayName;

    /** 支付宝账号 */
    @Excel(name = "支付宝账号")
    private String alipayAccount;

    /** 操作人 */
    @Excel(name = "操作人")
    private String operator;





}
