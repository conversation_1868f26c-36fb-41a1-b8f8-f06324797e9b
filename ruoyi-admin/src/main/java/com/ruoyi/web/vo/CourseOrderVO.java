package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 订单返回参数
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Data
public class CourseOrderVO {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 订单id
     */
    @Excel(name = "订单id")
    private String orderId;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 购买人id
     */
    @Excel(name = "购买人id")
    private Long buyerUserId;

    /**
     * 购买人姓名
     */
    @Excel(name = "购买人姓名")
    private String buyerUserName;

    /**
     * 购买人手机号
     */
    @Excel(name = "购买人手机号")
    private String buyerUserMobile;

    /**
     * 是否是推广订单 0不是  1是
     */
    @Excel(name = "是否是推广订单 0不是  1是")
    private Integer isPromoter;

    /**
     * 推广员id
     */
    @Excel(name = "推广员id")
    private Long promoterId;

    /**
     * 推广员名称
     */
    @Excel(name = "推广员名称")
    private String promoterName;

    /**
     * 推广员电话
     */
    @Excel(name = "推广员电话")
    private String promoterMobile;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 课程标题
     */
    @Excel(name = "课程标题")
    private String courseTitle;

    /**
     * 是否赠送课程 0否 1是
     */
    @Excel(name = "是否赠送课程 0否 1是")
    private Integer isCourse;

    /**
     * 订单状态 0 待支付 1已支付 2已退款 3已关闭
     */
    @Excel(name = "订单状态 0 待支付 1已支付 2已退款 3已关闭")
    private Integer orderStatus;

    /**
     * 订单类型 0课程订单 1推广订单
     */
    @Excel(name = "订单类型 0课程订单 1推广订单")
    private Integer orderType;

    /**
     * 订单来源
     */
    @Excel(name = "订单来源 0抖音 1微信 2快速 3视频号")
    private Integer orderPlatform;

    /**
     * 资金类型 0在途 1已入账
     */
    @Excel(name = "资金类型 0在途 1已入账")
    private Integer fundsType;

    /** 平台订单号 */
    @Excel(name = "平台订单号")
    private String outOrderNumber;

    /**
     * 渠道支付单号
     */
    @Excel(name = "渠道支付单号")
    private String tradingOrderNumber;

    /**
     * 收货人手机
     */
    @Excel(name = "收货人手机")
    private Long consigneeMobile;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺账号/手机号
     */
    private String mobile;

    /**
     * 自己后台调用类型   1自己调用
     */
    private Integer type;

    private Integer appNameType;

    /**
     * 分账状态,0未分账,1已分账
     */
    private Integer settleStatus;

    private Integer moneyOrder;

    /**
     * 抖店店铺id
     */
    private Long ddShopId;
    /**
     * 短信状态 1、未回执，2、发送失败，3、发送成功
     */
    private Integer ddSmsStatus;
    /**
     * 抖店短信消息id
     */
    private String ddSmsId;
    /**
     * 核销状态 0未核销，1已核销
     */
    private Integer ddHexiaoStatus;


    private Long userId;
    private Long deptId;

    private List<Long> teacherIds;


}
