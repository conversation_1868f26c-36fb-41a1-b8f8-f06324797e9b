package com.ruoyi.web.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 提现 withdraw_record
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
public class WithdrawWdVO extends BaseEntity
{
    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 提现订单号 */
    @Excel(name = "提现订单号")
    private String withdrawOrderNumber;

    /** 提现人名称 */
    @Excel(name = "提现人名称")
    private String withdrawName;

    /**提现账号*/
    @Excel(name = "提现账号")
    private String accountPhone;

    /** 到账金额 */
    @Excel(name = "到账金额")
    private BigDecimal accountPrice;

    /** 提现金额 */
    @Excel(name = "提现金额")
    private BigDecimal withdrawPrice;

    /** 收入来源 0抖音 1微信 2快速 3视频号 */
    @Excel(name = "收入来源 0抖音 1微信 2快速 3视频号")
    private Integer incomePlatform;

    /** 提现账户 0支付宝提现 1公司账户提现 */
    @Excel(name = "提现账户 0支付宝提现 1公司账户提现")
    private Integer accountType;

    /** 凭证*/
    @Excel(name = "凭证")
    private String evidence;

    /** 提现备注*/
    private String remark;

    /**落地公司名称*/
    private String landingCompanyName;

    /** 服务费比例 */
    @Excel(name = "服务费比例")
    private Integer servicePriceRatio;

    /** 服务费 */
    @Excel(name = "服务费")
    private BigDecimal servicePrice;


}
