package com.ruoyi.web.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopGiveEntityVO {

    private Integer pageSize;
    private Integer pageNum;
    private String shopInfomation;

    private Integer teacherId;

    private Integer id;

    //所属平台，入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开
    private Integer platform;
    /**
     * 排序字段  merchandiseCount  商品数量   giveMerchandiseCount  赠送商品数量
     * authMobileCount  授权手机数量   authAddressCount  授权地址数量
     */
    private String orderByColumn;

    /**
     * 排序方法    ascending升序   descending 降序
     */
    private String isAsc;
}
