package com.ruoyi.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 提现记录对象 withdraw_record
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawRecordVO {
    /**
     *分页参数
     */
    private Integer pageNum;
    private Integer pageSize;


    /** 主键id */
    private Integer id;

    /**店铺名称**/
    private String shopName;
    private Integer withdrawAuditStatus;




    /**  店铺账号   **/
    private String mobile;
    /**
     * 老师id   店铺的id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 提现人名称
     */
    @Excel(name = "提现人名称")
    private String withdrawName;

    @Excel(name = "老师真实姓名")
    private String teacherName;

    /**
     * 收入来源 0抖音 1微信 2快速 3视频号
     */
    @Excel(name = "收入来源 0抖音 1微信 2快速 3视频号")
    private Integer incomePlatform;

    /** 操作员id */
    private Long operatorId;

    /** 操作员名称 */
    private String operatorName;

    /** 操作员手机号 */
    private String operatorPhone;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
