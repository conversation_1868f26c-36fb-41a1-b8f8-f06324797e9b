package com.ruoyi.web.vo;


import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;


@Data
public class ClockRecordVO implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String userName;

    /**
     * 回复状态 0未回复 1已回复
     */
    @Excel(name = "回复状态 0未回复 1已回复")
    private Integer replyStatus;

    /**
     * 0打卡 1作业
     */
    @Excel(name = "0打卡 1作业")
    private Integer type;

}
