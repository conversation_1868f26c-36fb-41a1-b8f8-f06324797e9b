package com.ruoyi.controller.system;

import com.alibaba.fastjson2.JSON;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Test1 {
    public static void main(String[] args) throws IOException {
        //从本地磁盘读取文本文件内容
        String filePath = "C:\\Users\\<USER>\\Desktop\\stream-response(1).txt";
        String content = FileUtils.readFileToString(new File(filePath), "UTF-8");
//        System.out.println(content);

        TestDTO testDTO = JSON.parseObject(content, TestDTO.class);
        List<JsonRootBean> unChapter = testDTO.getData().getUn_chapter();
        Map<String,String> map = new HashMap<>();
        for(JsonRootBean j:unChapter){
//            System.out.println(j.getContent());
            String newString  = j.getContent().replace("https://media.zaixian100f.com/","");
            newString = newString.split("/")[1];
            newString = newString.split("\\.")[0];
//            System.out.println(newString);
            map.put(newString, j.getTitle());
        }

        //读取"F:\视频下载1"目录下的文件列表
        File dir = new File("F:\\视频下载1");
        File[] files = dir.listFiles();
        for(File file:files){
            String fileName = file.getName();
            String[] split = fileName.split("\\.");
            String newString = split[0];
            if(map.containsKey(newString)){
                //重命名文件为value
                String value = map.get(newString);
                file.renameTo(new File("F:\\视频下载1\\"+value+"."+split[1]));
                System.out.println(fileName+"-->"+value);
            }
        }
    }
}
