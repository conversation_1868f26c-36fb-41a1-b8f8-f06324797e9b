package com.ruoyi.controller.system;

import java.util.ArrayList;
import java.util.List;

public class Test {
    public static void main(String[] args) {
List<String> list = new ArrayList<>();
        list.add("newteacher.wendao101.com");
        list.add("y.wendao101.com");
        list.add("cdn.wendao101.com");
        list.add("temp.wendao101.com");
        list.add("developer-product.wendao101.com");
        list.add("kf.wendao101.com");
        list.add("nginx.wendao101.com");
        list.add("open-sandbox.wendao101.com");
        list.add("test.wendao101.com");
        list.add("csgl.wendao101.com");

        String temp = "server {\n" +
                "\t\tlisten 80;\n" +
                "\t\t#填写证书绑定的域名\n" +
                "\t\tserver_name dycallback.wendao101.com;\n" +
                "\t\t#将所有HTTP请求通过rewrite指令重定向到HTTPS。\n" +
                "\t\t\t\tlocation / {\n" +
                "\t\t\t\t\t\tproxy_set_header Host $http_host;\n" +
                "\t\t\t\t\t\tproxy_set_header X-Real-IP $remote_addr;\n" +
                "\t\t\t\t\t\tproxy_set_header REMOTE-HOST $remote_addr;\n" +
                "\t\t\t\t\t\tproxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n" +
                "\t\t\t\t\t\tproxy_pass http://dycallback.wendao101.com/;\n" +
                "\t\t\t\t}\n" +
                "\n" +
                "\t\t   error_page   500 502 503 504  /50x.html;\n" +
                "\t\t\t\tlocation = /50x.html {\n" +
                "\t\t\t\t\t\troot   html;\n" +
                "\t\t\t\t}\n" +
                "}";

        for(String s :list){
            //String replace = temp.replace("dycallback.wendao101.com", s);
            System.out.println("************** "+s);
        }

    }

}
