package com.ruoyi.controller.system;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;

public class Test222 {
    public static void main(String[] args) throws IOException {
        //以一行一行的方式读取文本文件
        File file = new File("C:\\Users\\<USER>\\Desktop\\vurls.txt");// Text文件
        BufferedReader br = new BufferedReader(new FileReader(file));// 构造一个BufferedReader类来读取文件
        String s = null;
        while ((s = br.readLine()) != null) {// 使用readLine方法，一次读一行
            //System.out.println(s);
            //执行windows cmd 命令 F:\视频下载\m3u8d.exe -u https://example.com/index.m3u8  并获取输出
            String command = "F:\\视频下载\\m3u8d.exe download -u " + s;
            try {
                Process p = Runtime.getRuntime().exec(command);
                StreamCaptureThread errorStream = new StreamCaptureThread(p.getErrorStream());
                StreamCaptureThread outputStream = new StreamCaptureThread(p.getInputStream());
                new Thread(errorStream).start();
                new Thread(outputStream).start();
                p.waitFor();

                String result = command + "\n" + outputStream.output.toString()
                        + errorStream.output.toString();
                System.out.print(result);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        br.close();

    }



}
