server {
		listen 80;
		#填写证书绑定的域名
		server_name newteacher.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://newteacher.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name y.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://y.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name cdn.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://cdn.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name temp.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://temp.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name developer-product.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://developer-product.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name kf.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://kf.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name nginx.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://nginx.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name open-sandbox.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://open-sandbox.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name test.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://test.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}
server {
		listen 80;
		#填写证书绑定的域名
		server_name csgl.wendao101.com;
		#将所有HTTP请求通过rewrite指令重定向到HTTPS。
				location / {
						proxy_set_header Host $http_host;
						proxy_set_header X-Real-IP $remote_addr;
						proxy_set_header REMOTE-HOST $remote_addr;
						proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
						proxy_pass http://csgl.wendao101.com/;
				}

		   error_page   500 502 503 504  /50x.html;
				location = /50x.html {
						root   html;
				}
}