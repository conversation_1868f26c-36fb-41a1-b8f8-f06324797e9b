package com.ruoyi.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;

public class JieXiJsonData {
    public static void main(String[] args) throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\腾讯云点播视频处理\\熔化和焊接复训.txt";
        String content = FileUtils.readFileToString(new File(filePath), "UTF-8");
//        System.out.println(content);

        JSONObject jsonObject = JSON.parseObject(content, JSONObject.class);
        JSONObject response = jsonObject.getJSONObject("Response");
        JSONArray mediaInfoSet = response.getJSONArray("MediaInfoSet");
        //mediaInfoSet.size();
        for (int i = 0; i < mediaInfoSet.size(); i++) {
            JSONObject jsonObject1 = mediaInfoSet.getJSONObject(i);
            //FileId
            String fileId = jsonObject1.getString("FileId");
            //BasicInfo
            JSONObject jsonObject2 = jsonObject1.getJSONObject("BasicInfo");
            String coverUrl = jsonObject2.getString("CoverUrl");
            String mediaUrl = jsonObject2.getString("MediaUrl");
            //Name
            String name = jsonObject2.getString("Name");
            //MetaData
            JSONObject jsonObject3 = jsonObject1.getJSONObject("MetaData");
            //VideoDuration
            Double videoDuration = jsonObject3.getDouble("VideoDuration");
            //Size
            Integer size = jsonObject3.getInteger("Size");
            //VideoStreamSet
            JSONArray videoStreamSet = jsonObject3.getJSONArray("VideoStreamSet");
            JSONObject jsonObject4 = videoStreamSet.getJSONObject(0);
            //Height
            Integer height = jsonObject4.getInteger("Height");
            //Width
            Integer width = jsonObject4.getInteger("Width");

            //打印以上所有变量名称和变量值
//            System.out.println("fileId:" + fileId);
//            System.out.println("coverUrl:" + coverUrl);
//            System.out.println("mediaUrl:" + mediaUrl);
//            System.out.println("name:" + name);
//            System.out.println("videoDuration:" + videoDuration);
//            System.out.println("size:" + size);
//            System.out.println("height:" + height);
//            System.out.println("width:" + width);

            String groupId ="6649";
            //组装SQL语句
            //INSERT INTO `t_video` (`teacher_id`, `path_url`, `video_pic_url`, `file_name`, `file_after_trans_size`, `file_original_size`, `is_delete`, `transcode_status`, `tcvod_file_id`, `tcvod_media_url_before_trans`, `tcvod_media_url_720p`, `create_time`, `update_time`, `procedure_error_msg`, `transcode_error_msg`, `cover_by_snapshot_error_msg`, `duration`, `video_height`, `video_width`, `app_name_type`, `source_material_id`, `old_id`, `md5`, `sha1`) VALUES (254127478, 'https://1319546384.vod-qcloud.com/667f701dvodtranssh1319546384/43fa40541253642699883393086/v.f100030.mp4', 'https://1319546384.vod-qcloud.com/667f701dvodtranssh1319546384/43fa40541253642699883393086/coverBySnapshot/coverBySnapshot_10_0.jpg', '8乡村奶奶洗菜课（上）.mp4', 46127135, 441932376, 0, 1, '1253642699883393086', 'https://1319546384.vod-qcloud.com/8f958275vodsh1319546384/43fa40541253642699883393086/j6nfmMgQ2b4A.mp4', 'https://1319546384.vod-qcloud.com/667f701dvodtranssh1319546384/43fa40541253642699883393086/v.f100030.mp4', '2024-07-08 11:52:29', '2024-07-08 11:55:20', NULL, NULL, NULL, 360.93, 1280, 720, 2, NULL, NULL, NULL, NULL);
            String sqlTemplate = "INSERT INTO `t_video` (`teacher_id`, `path_url`, `video_pic_url`, `file_name`, `file_after_trans_size`, `file_original_size`, `is_delete`, `transcode_status`, `tcvod_file_id`, `tcvod_media_url_before_trans`, `tcvod_media_url_720p`, `create_time`, `update_time`, `procedure_error_msg`, `transcode_error_msg`, `cover_by_snapshot_error_msg`, `duration`, `video_height`, `video_width`, `app_name_type`, `source_material_id`, `old_id`, `md5`, `sha1`) " +
                    "VALUES (254127478, '%s', '%s', '%s', %d, %d, 0, 1, '%s', '%s', '%s', sysDate(), sysDate(), NULL, NULL, NULL, %.2f, %d, %d, 2, NULL, NULL, NULL, NULL);";

//mediaUrl
            //CoverUrl
            //name
            //size
            //size
            //fileId
            //mediaUrl
            //mediaUrl
            //videoDuration
            //height
            //width
            String sql = String.format(sqlTemplate, mediaUrl, coverUrl, name, size, size, fileId, mediaUrl, mediaUrl, videoDuration, height, width);
            System.out.println(sql);
//break;

        }
    }
}
