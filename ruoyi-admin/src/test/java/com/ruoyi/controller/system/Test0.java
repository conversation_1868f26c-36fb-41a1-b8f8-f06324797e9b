package com.ruoyi.controller.system;

import com.alibaba.fastjson2.JSON;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Test0 {
    public static void main(String[] args) throws IOException {
        //从本地磁盘读取文本文件内容
        String filePath = "C:\\Users\\<USER>\\Desktop\\stream-response(1).txt";
        String content = FileUtils.readFileToString(new File(filePath), "UTF-8");
//        System.out.println(content);

        TestDTO testDTO = JSON.parseObject(content, TestDTO.class);
        List<JsonRootBean> unChapter = testDTO.getData().getUn_chapter();
        Map<String,String> map = new HashMap<>();
        for(JsonRootBean j:unChapter){
           //System.out.println(j.getContent());
            System.out.println(j.getTitle());
        }


    }
}
