/**
  * Copyright 2024 json.cn 
  */
package com.ruoyi.controller.system;
import java.util.Date;

/**
 * Auto-generated: 2024-07-06 9:56:8
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/
 */
public class JsonRootBean {

    private String title;
    private int course_id;
    private int user_id;
    private int hit_num;
    private String courseware_id;
    private Date updated_at;
    private int study_state;
    private String curriculum_id;
    private int type;
    private int state;
    private int org_show;
    private int id;
    private int number;
    private int length;
    private String now_minute;
    private String start_time;
    private int free;
    private int chapter_id;
    private String end_time;
    private Date created_at;
    private String content;
    public void setTitle(String title) {
         this.title = title;
     }
     public String getTitle() {
         return title;
     }

    public void setCourse_id(int course_id) {
         this.course_id = course_id;
     }
     public int getCourse_id() {
         return course_id;
     }

    public void setUser_id(int user_id) {
         this.user_id = user_id;
     }
     public int getUser_id() {
         return user_id;
     }

    public void setHit_num(int hit_num) {
         this.hit_num = hit_num;
     }
     public int getHit_num() {
         return hit_num;
     }

    public void setCourseware_id(String courseware_id) {
         this.courseware_id = courseware_id;
     }
     public String getCourseware_id() {
         return courseware_id;
     }

    public void setUpdated_at(Date updated_at) {
         this.updated_at = updated_at;
     }
     public Date getUpdated_at() {
         return updated_at;
     }

    public void setStudy_state(int study_state) {
         this.study_state = study_state;
     }
     public int getStudy_state() {
         return study_state;
     }

    public void setCurriculum_id(String curriculum_id) {
         this.curriculum_id = curriculum_id;
     }
     public String getCurriculum_id() {
         return curriculum_id;
     }

    public void setType(int type) {
         this.type = type;
     }
     public int getType() {
         return type;
     }

    public void setState(int state) {
         this.state = state;
     }
     public int getState() {
         return state;
     }

    public void setOrg_show(int org_show) {
         this.org_show = org_show;
     }
     public int getOrg_show() {
         return org_show;
     }

    public void setId(int id) {
         this.id = id;
     }
     public int getId() {
         return id;
     }

    public void setNumber(int number) {
         this.number = number;
     }
     public int getNumber() {
         return number;
     }

    public void setLength(int length) {
         this.length = length;
     }
     public int getLength() {
         return length;
     }

    public void setNow_minute(String now_minute) {
         this.now_minute = now_minute;
     }
     public String getNow_minute() {
         return now_minute;
     }

    public void setStart_time(String start_time) {
         this.start_time = start_time;
     }
     public String getStart_time() {
         return start_time;
     }

    public void setFree(int free) {
         this.free = free;
     }
     public int getFree() {
         return free;
     }

    public void setChapter_id(int chapter_id) {
         this.chapter_id = chapter_id;
     }
     public int getChapter_id() {
         return chapter_id;
     }

    public void setEnd_time(String end_time) {
         this.end_time = end_time;
     }
     public String getEnd_time() {
         return end_time;
     }

    public void setCreated_at(Date created_at) {
         this.created_at = created_at;
     }
     public Date getCreated_at() {
         return created_at;
     }

    public void setContent(String content) {
         this.content = content;
     }
     public String getContent() {
         return content;
     }

}